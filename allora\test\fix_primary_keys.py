#!/usr/bin/env python3
"""
Fix Primary Key Constraints for Allora Database
The tables have id columns but they're not set as primary keys
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
import logging
from sqlalchemy import inspect, text
from sqlalchemy.exc import SQLAlchemyError

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_primary_keys():
    """Fix primary key constraints for tables that have id columns but no primary key"""
    
    print("🔑 FIXING PRIMARY KEY CONSTRAINTS")
    print("="*80)
    
    with app.app_context():
        inspector = inspect(db.engine)
        tables = inspector.get_table_names()
        
        # Tables that should have primary keys on their id column
        critical_tables = [
            'user', 'users', 'product', 'products', 'order', 'orders', 
            'order_item', 'seller', 'sellers', 'cart_item', 'product_review',
            'wishlist', 'payment_transaction', 'shipping_carriers', 'coupon',
            'rma_request', 'rma_item', 'search_analytics', 'community_post',
            'post_comment', 'admin_user', 'hashtag', 'post_hashtag', 'post_like',
            'community_stats', 'sales', 'price_history', 'community_insight',
            'user_interaction_logs', 'user_behavior_profiles', 'user_sessions',
            'o_auth_provider', 'user_o_auth', 'guest_session', 'saved_cart',
            'shipping_zone', 'shipping_method', 'abandoned_cart', 'email_notification',
            'tax_rate', 'coupon_usage', 'user_address', 'payment_method',
            'product_image', 'product_variant', 'admin_activity_log', 'inventory_log',
            'sales_channel', 'channel_inventory', 'inventory_sync_log', 'inventory_conflict',
            'sync_queue', 'content_page', 'banner', 'recently_viewed', 'availability_notification',
            'product_comparison', 'support_ticket', 'support_message', 'support_attachment',
            'payment_gateway', 'invoice', 'refund', 'rma_timeline', 'rma_document',
            'return_shipment', 'rma_approval', 'rma_rule', 'rma_configuration', 'rma_stats',
            'shipments', 'tracking_events', 'fulfillment_rules', 'carrier_rates',
            'newsletter_subscription', 'visual_search_analytics', 'cookie_consent',
            'seller_store', 'seller_commission', 'seller_payout', 'cookie_consent_history',
            'cookie_audit_log', 'data_export_request'
        ]
        
        fixed_count = 0
        error_count = 0
        
        for table_name in critical_tables:
            if table_name in tables:
                try:
                    # Check current table structure
                    columns = inspector.get_columns(table_name)
                    
                    # Check if id column exists
                    id_column = None
                    for col in columns:
                        if col['name'] == 'id':
                            id_column = col
                            break
                    
                    if not id_column:
                        print(f"⚠️  Table '{table_name}' has no 'id' column")
                        continue
                    
                    # Check if id column is already a primary key
                    if id_column.get('primary_key', False):
                        print(f"✅ Table '{table_name}' already has primary key")
                        continue
                    
                    # Add primary key constraint to existing id column
                    try:
                        # First, make sure the column is NOT NULL and AUTO_INCREMENT
                        alter_column_query = f"""
                            ALTER TABLE `{table_name}` 
                            MODIFY COLUMN `id` INT NOT NULL AUTO_INCREMENT
                        """
                        db.session.execute(text(alter_column_query))
                        
                        # Then add primary key constraint
                        add_pk_query = f"""
                            ALTER TABLE `{table_name}` 
                            ADD PRIMARY KEY (`id`)
                        """
                        db.session.execute(text(add_pk_query))
                        db.session.commit()
                        
                        print(f"✅ Fixed primary key for table '{table_name}'")
                        fixed_count += 1
                        
                    except Exception as e:
                        if "Multiple primary key defined" in str(e) or "Duplicate entry" in str(e):
                            print(f"⏭️  Table '{table_name}' already has primary key constraint")
                        else:
                            print(f"❌ Error fixing primary key for '{table_name}': {e}")
                            error_count += 1
                        db.session.rollback()
                        
                except Exception as e:
                    print(f"❌ Error analyzing table '{table_name}': {e}")
                    error_count += 1
        
        print(f"\n📊 SUMMARY:")
        print(f"✅ Fixed primary keys: {fixed_count}")
        print(f"❌ Errors encountered: {error_count}")

def verify_primary_keys():
    """Verify that primary keys are properly set"""
    
    print("\n🔍 VERIFYING PRIMARY KEY FIXES")
    print("="*80)
    
    with app.app_context():
        inspector = inspect(db.engine)
        tables = inspector.get_table_names()
        
        tables_without_pk = []
        tables_with_pk = []
        
        for table_name in tables:
            try:
                columns = inspector.get_columns(table_name)
                has_pk = any(col.get('primary_key', False) for col in columns)
                
                if has_pk:
                    tables_with_pk.append(table_name)
                else:
                    tables_without_pk.append(table_name)
                    
            except Exception as e:
                print(f"❌ Error checking table '{table_name}': {e}")
        
        print(f"✅ Tables with primary keys: {len(tables_with_pk)}")
        print(f"❌ Tables without primary keys: {len(tables_without_pk)}")
        
        if tables_without_pk:
            print(f"\n⚠️  Tables still missing primary keys:")
            for table in tables_without_pk[:10]:  # Show first 10
                print(f"  - {table}")
            if len(tables_without_pk) > 10:
                print(f"  ... and {len(tables_without_pk) - 10} more")

def main():
    print("🔧 Starting Primary Key Repair...")
    
    # Fix primary keys
    fix_primary_keys()
    
    # Verify the fixes
    verify_primary_keys()
    
    print("\n✅ Primary key repair completed!")

if __name__ == '__main__':
    main()
