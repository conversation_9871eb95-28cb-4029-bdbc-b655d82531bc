#!/usr/bin/env python3
"""
Simple test Flask app to verify basic functionality
"""
from flask import Flask, jsonify
from flask_cors import CORS
from datetime import datetime

app = Flask(__name__)

# Configure CORS
CORS(app, 
     resources={
         r"/api/*": {
             "origins": ["http://localhost:3000", "http://127.0.0.1:3000"],
             "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
             "allow_headers": ["Content-Type", "Authorization", "X-Requested-With"],
             "supports_credentials": True
         }
     },
     supports_credentials=True)

@app.route('/', methods=['GET'])
def home():
    return jsonify({'message': 'Simple test Flask app is working!'})

@app.route('/api/health', methods=['GET'])
def health_check():
    return jsonify({
        'status': 'healthy',
        'message': 'Simple test backend is running',
        'timestamp': datetime.utcnow().isoformat(),
        'version': '1.0.0',
        'cors_enabled': True
    })

@app.route('/api/test', methods=['GET'])
def test_endpoint():
    return jsonify({
        'message': 'Test endpoint working',
        'cors_headers': True
    })

if __name__ == '__main__':
    print("Starting simple test Flask app...")
    print("Routes:")
    for rule in app.url_map.iter_rules():
        print(f"  {rule.rule} -> {rule.endpoint} [{', '.join(rule.methods)}]")
    
    app.run(debug=False, host='0.0.0.0', port=5001)  # Use different port
