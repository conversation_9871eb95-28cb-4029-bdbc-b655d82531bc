#!/usr/bin/env python3
"""
Flask-SocketIO Integration Script
===============================

This script integrates Flask-SocketIO with the existing Flask application
to replace the FastAPI WebSocket implementation.

Usage:
    python integrate_socketio.py

Author: Allora Development Team
Date: 2025-07-13
"""

import os
import sys

def update_app_py():
    """Update app.py to integrate Flask-SocketIO"""
    
    app_py_path = "app.py"
    
    # Read current app.py
    with open(app_py_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check if SocketIO is already integrated
    if 'flask_socketio_manager' in content:
        print("✅ Flask-SocketIO already integrated in app.py")
        return True
    
    # Find the app creation line
    if 'app = Flask(__name__)' in content:
        # Add SocketIO import and initialization
        socketio_integration = '''
# Import Flask-SocketIO manager
try:
    from flask_socketio_manager import init_socketio, socketio_manager
    
    # Initialize Socket<PERSON> with the Flask app
    socketio = init_socketio(app)
    logger.info("Flask-SocketIO initialized successfully")
    
    # Update inventory broadcast functions to use SocketIO
    def broadcast_inventory_update_socketio(product_id, variant_id, new_quantity, old_quantity):
        """Broadcast inventory update via SocketIO"""
        try:
            from flask_socketio_manager import broadcast_inventory_update
            broadcast_inventory_update(product_id, new_quantity, old_quantity)
        except Exception as e:
            logger.error(f"Error broadcasting inventory update via SocketIO: {e}")
    
    # Replace WebSocket broadcast functions
    InventoryUpdateManager.broadcast_inventory_update = staticmethod(broadcast_inventory_update_socketio)
    
except ImportError as e:
    logger.warning(f"Could not import Flask-SocketIO manager: {e}")
    socketio = None
except Exception as e:
    logger.error(f"Error initializing Flask-SocketIO: {e}")
    socketio = None
'''
        
        # Insert after app creation
        insertion_point = content.find('app = Flask(__name__)') + len('app = Flask(__name__)')
        new_content = content[:insertion_point] + socketio_integration + content[insertion_point:]
        
        # Write updated content
        with open(app_py_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ Flask-SocketIO integration added to app.py")
        return True
    
    else:
        print("❌ Could not find app creation line in app.py")
        return False

def update_run_with_waitress():
    """Update run_with_waitress.py to use SocketIO"""
    
    run_file_path = "run_with_waitress.py"
    
    # Read current file
    with open(run_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check if SocketIO is already integrated
    if 'socketio.run' in content:
        print("✅ SocketIO already integrated in run_with_waitress.py")
        return True
    
    # Replace waitress serve with SocketIO run for development
    if 'serve(app, host=HOST, port=PORT, threads=THREADS)' in content:
        new_content = content.replace(
            'serve(app, host=HOST, port=PORT, threads=THREADS)',
            '''# Check if SocketIO is available
    try:
        from app import socketio
        if socketio:
            print("🔌 Starting with Flask-SocketIO support...")
            socketio.run(app, host=HOST, port=PORT, debug=False, allow_unsafe_werkzeug=True)
        else:
            print("⚠️  SocketIO not available, using Waitress...")
            serve(app, host=HOST, port=PORT, threads=THREADS)
    except ImportError:
        print("⚠️  SocketIO not available, using Waitress...")
        serve(app, host=HOST, port=PORT, threads=THREADS)'''
        )
        
        # Write updated content
        with open(run_file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ SocketIO integration added to run_with_waitress.py")
        return True
    
    else:
        print("❌ Could not find serve line in run_with_waitress.py")
        return False

def create_socketio_routes():
    """Create SocketIO routes file for API endpoints"""
    
    routes_content = '''"""
Flask-SocketIO API Routes
========================

API endpoints for triggering SocketIO events and managing connections.
"""

from flask import Blueprint, request, jsonify
from flask_socketio_manager import socketio_manager

# Create blueprint for SocketIO API routes
socketio_api_bp = Blueprint('socketio_api', __name__, url_prefix='/api/socketio')

@socketio_api_bp.route('/broadcast', methods=['POST'])
def broadcast_message():
    """Broadcast message to all connected clients"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Broadcast to all clients
        socketio_manager.socketio.emit('broadcast', data)
        
        return jsonify({'status': 'Message broadcasted successfully'})
        
    except Exception as e:
        return jsonify({'error': f'Broadcast failed: {str(e)}'}), 500

@socketio_api_bp.route('/notify-user/<user_id>', methods=['POST'])
def notify_user(user_id):
    """Send notification to specific user"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        socketio_manager.send_notification(user_id, data)
        
        return jsonify({'status': 'Notification sent successfully'})
        
    except Exception as e:
        return jsonify({'error': f'Notification failed: {str(e)}'}), 500

@socketio_api_bp.route('/connections', methods=['GET'])
def get_connections():
    """Get connection statistics"""
    try:
        stats = socketio_manager.get_connection_stats()
        return jsonify(stats)
        
    except Exception as e:
        return jsonify({'error': f'Failed to get stats: {str(e)}'}), 500

@socketio_api_bp.route('/health', methods=['GET'])
def health_check():
    """Health check for SocketIO service"""
    try:
        stats = socketio_manager.get_connection_stats()
        
        # Test Redis connection
        redis_status = "healthy"
        try:
            socketio_manager.redis_client.ping()
        except:
            redis_status = "unhealthy"
        
        return jsonify({
            'status': 'healthy',
            'connections': stats['total_connections'],
            'redis_status': redis_status,
            'timestamp': '2025-07-13T00:00:00Z'
        })
        
    except Exception as e:
        return jsonify({'error': f'Health check failed: {str(e)}'}), 500

# Event trigger endpoints
@socketio_api_bp.route('/events/inventory-update', methods=['POST'])
def trigger_inventory_update():
    """Trigger inventory update event"""
    try:
        data = request.get_json()
        product_id = data.get('product_id')
        new_quantity = data.get('new_quantity')
        old_quantity = data.get('old_quantity')
        
        if product_id is None or new_quantity is None:
            return jsonify({'error': 'Missing required fields'}), 400
        
        socketio_manager.broadcast_inventory_update(product_id, new_quantity, old_quantity)
        
        return jsonify({'status': 'Inventory update broadcasted'})
        
    except Exception as e:
        return jsonify({'error': f'Failed to broadcast: {str(e)}'}), 500

@socketio_api_bp.route('/events/price-update', methods=['POST'])
def trigger_price_update():
    """Trigger price update event"""
    try:
        data = request.get_json()
        product_id = data.get('product_id')
        new_price = data.get('new_price')
        old_price = data.get('old_price')
        
        if not all([product_id, new_price is not None, old_price is not None]):
            return jsonify({'error': 'Missing required fields'}), 400
        
        socketio_manager.broadcast_price_update(product_id, new_price, old_price)
        
        return jsonify({'status': 'Price update broadcasted'})
        
    except Exception as e:
        return jsonify({'error': f'Failed to broadcast: {str(e)}'}), 500

@socketio_api_bp.route('/events/cart-update', methods=['POST'])
def trigger_cart_update():
    """Trigger cart update event"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        session_id = data.get('session_id')
        cart_data = data.get('cart_data', {})
        
        socketio_manager.send_cart_update(user_id, session_id, cart_data)
        
        return jsonify({'status': 'Cart update sent'})
        
    except Exception as e:
        return jsonify({'error': f'Failed to send update: {str(e)}'}), 500
'''
    
    with open('socketio_routes.py', 'w', encoding='utf-8') as f:
        f.write(routes_content)
    
    print("✅ SocketIO routes file created")

def register_socketio_routes():
    """Add SocketIO routes registration to app.py"""
    
    app_py_path = "app.py"
    
    # Read current app.py
    with open(app_py_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check if routes are already registered
    if 'socketio_routes' in content:
        print("✅ SocketIO routes already registered")
        return True
    
    # Find a good place to add route registration
    if 'app.register_blueprint(' in content:
        # Find the last blueprint registration
        lines = content.split('\n')
        insert_line = -1
        
        for i, line in enumerate(lines):
            if 'app.register_blueprint(' in line:
                insert_line = i + 1
        
        if insert_line > 0:
            # Insert SocketIO routes registration
            socketio_registration = '''
# Register SocketIO API routes
try:
    from socketio_routes import socketio_api_bp
    app.register_blueprint(socketio_api_bp)
    logger.info("SocketIO API routes registered successfully")
except ImportError as e:
    logger.warning(f"Could not import SocketIO routes: {e}")
except Exception as e:
    logger.error(f"Error registering SocketIO routes: {e}")
'''
            
            lines.insert(insert_line, socketio_registration)
            new_content = '\n'.join(lines)
            
            # Write updated content
            with open(app_py_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("✅ SocketIO routes registration added to app.py")
            return True
    
    print("❌ Could not find suitable place to register SocketIO routes")
    return False

def main():
    """Main integration function"""
    print("🔌 Flask-SocketIO Integration Script")
    print("=" * 50)
    
    success_count = 0
    total_steps = 5
    
    # Step 1: Update app.py
    if update_app_py():
        success_count += 1
    
    # Step 2: Update run_with_waitress.py
    if update_run_with_waitress():
        success_count += 1
    
    # Step 3: Create SocketIO routes
    try:
        create_socketio_routes()
        success_count += 1
    except Exception as e:
        print(f"❌ Failed to create SocketIO routes: {e}")
    
    # Step 4: Register SocketIO routes
    if register_socketio_routes():
        success_count += 1
    
    # Step 5: Check if Flask-SocketIO is installed
    try:
        import flask_socketio
        print("✅ Flask-SocketIO is installed")
        success_count += 1
    except ImportError:
        print("❌ Flask-SocketIO not installed")
        print("💡 Install with: pip install flask-socketio")
    
    # Summary
    print("\n" + "=" * 50)
    print(f"📊 Integration Summary: {success_count}/{total_steps} steps completed")
    
    if success_count == total_steps:
        print("🎉 Flask-SocketIO integration completed successfully!")
        print("\n📋 Next Steps:")
        print("1. Install Flask-SocketIO: pip install flask-socketio")
        print("2. Restart the server: python run_with_waitress.py")
        print("3. Test WebSocket connection at: ws://localhost:5000")
    elif success_count >= 3:
        print("✅ Most integration steps completed - minor fixes needed")
    else:
        print("⚠️  Integration incomplete - manual fixes required")
    
    return success_count == total_steps

if __name__ == '__main__':
    main()
