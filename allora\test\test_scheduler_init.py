#!/usr/bin/env python3
"""
Scheduler Initialization Test Suite
===================================

Comprehensive testing for the scheduler initialization functionality.
Tests all components, manager, and integration.

Author: Allora Development Team
Date: 2025-07-13
"""

import sys
import os
import time
import threading
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_scheduler_init_structure():
    """Test the scheduler init module structure"""
    print("🔍 Testing Scheduler Init Structure")
    print("=" * 45)
    
    try:
        # Import scheduler init module
        import scheduler_init
        
        print("✅ Scheduler init module imported successfully")
        
        # Test core classes
        required_classes = [
            'SchedulerManager'
        ]
        
        for class_name in required_classes:
            if hasattr(scheduler_init, class_name):
                print(f"   ✅ {class_name}: Found")
            else:
                print(f"   ❌ {class_name}: Missing")
        
        # Test factory functions
        factory_functions = [
            'get_scheduler_manager', 'initialize_schedulers', 'get_scheduler_status',
            'stop_all_schedulers', 'init_schedulers_with_app', 'create_scheduler_blueprint'
        ]
        
        for func_name in factory_functions:
            if hasattr(scheduler_init, func_name):
                print(f"   ✅ {func_name}: Found")
            else:
                print(f"   ❌ {func_name}: Missing")
        
        # Test specific functions
        specific_functions = ['register_inventory_scheduler']
        for func_name in specific_functions:
            if hasattr(scheduler_init, func_name):
                print(f"   ✅ {func_name}: Found")
            else:
                print(f"   ❌ {func_name}: Missing")
        
        return True
        
    except ImportError as e:
        print(f"❌ Scheduler init import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Scheduler init structure test error: {e}")
        return False

def test_scheduler_manager():
    """Test SchedulerManager functionality"""
    print("\n⚙️ Testing SchedulerManager")
    print("=" * 30)
    
    try:
        from scheduler_init import SchedulerManager
        
        # Create manager instance
        manager = SchedulerManager()
        print("✅ SchedulerManager instance created")
        
        # Test initial state
        print(f"   ✅ Initial running state: {manager.running}")
        print(f"   ✅ Initial schedulers count: {len(manager.schedulers)}")
        
        # Test scheduler registration
        class MockScheduler:
            def __init__(self, name):
                self.name = name
                self.running = False
            
            def start(self):
                self.running = True
                print(f"   Mock scheduler {self.name} started")
            
            def stop(self):
                self.running = False
                print(f"   Mock scheduler {self.name} stopped")
        
        # Register a mock scheduler
        manager.register_scheduler('test_scheduler', MockScheduler, 'test')
        print("   ✅ Mock scheduler registered")
        
        # Test status before initialization
        status = manager.get_status()
        print(f"   ✅ Status retrieved: {len(status['schedulers'])} schedulers")
        
        # Test scheduler retrieval (should be None before initialization)
        scheduler_instance = manager.get_scheduler('test_scheduler')
        print(f"   ✅ Scheduler retrieval before init: {scheduler_instance is None}")
        
        return True
        
    except Exception as e:
        print(f"❌ SchedulerManager test error: {e}")
        return False

def test_global_manager():
    """Test global scheduler manager functionality"""
    print("\n🌐 Testing Global Manager")
    print("=" * 30)
    
    try:
        from scheduler_init import get_scheduler_manager, get_scheduler_status
        
        # Get global manager
        manager1 = get_scheduler_manager()
        manager2 = get_scheduler_manager()
        print("✅ Global manager instances retrieved")
        
        # Test singleton pattern
        is_singleton = manager1 is manager2
        print(f"   ✅ Singleton pattern: {is_singleton}")
        
        # Test status function
        status = get_scheduler_status()
        print("✅ Global status function works")
        print(f"   Manager running: {status['manager_running']}")
        print(f"   Schedulers count: {len(status['schedulers'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ Global manager test error: {e}")
        return False

def test_inventory_scheduler_registration():
    """Test inventory scheduler registration"""
    print("\n📦 Testing Inventory Scheduler Registration")
    print("=" * 50)
    
    try:
        from scheduler_init import register_inventory_scheduler, get_scheduler_manager
        
        # Get initial state
        manager = get_scheduler_manager()
        initial_count = len(manager.schedulers)
        
        # Register inventory scheduler
        register_inventory_scheduler()
        print("✅ Inventory scheduler registration attempted")
        
        # Check if scheduler was registered
        final_count = len(manager.schedulers)
        scheduler_added = final_count > initial_count
        print(f"   ✅ Scheduler added: {scheduler_added}")
        
        # Check if inventory_sync scheduler exists
        if 'inventory_sync' in manager.schedulers:
            print("   ✅ inventory_sync scheduler: Registered")
            config = manager.schedulers['inventory_sync']
            print(f"      Class: {config['class'].__name__}")
            print(f"      Initialized: {config['initialized']}")
        else:
            print("   ⚠️  inventory_sync scheduler: Not found (may be due to import issues)")
        
        return True
        
    except Exception as e:
        print(f"❌ Inventory scheduler registration test error: {e}")
        return False

def test_flask_integration():
    """Test Flask integration functionality"""
    print("\n🌐 Testing Flask Integration")
    print("=" * 35)
    
    try:
        from scheduler_init import create_scheduler_blueprint
        
        # Create scheduler blueprint
        blueprint = create_scheduler_blueprint()
        print("✅ Scheduler blueprint created")
        
        # Test blueprint properties
        print(f"   ✅ Blueprint name: {blueprint.name}")
        print(f"   ✅ URL prefix: {blueprint.url_prefix}")
        
        # Test blueprint routes
        routes = []
        for rule in blueprint.deferred_functions:
            if hasattr(rule, 'rule'):
                routes.append(rule.rule)
        
        print(f"   ✅ Blueprint routes: {len(routes)} registered")
        
        return True
        
    except Exception as e:
        print(f"❌ Flask integration test error: {e}")
        return False

def test_app_integration():
    """Test scheduler integration with Flask app"""
    print("\n🔗 Testing App Integration")
    print("=" * 30)
    
    try:
        # Import Flask app
        from app import app
        
        print("✅ Flask app imported successfully")
        
        # Check if scheduler blueprint is registered
        blueprints = [bp.name for bp in app.blueprints.values()]
        if 'scheduler' in blueprints:
            print("   ✅ Scheduler blueprint: Registered")
        else:
            print("   ❌ Scheduler blueprint: Not registered")
        
        # Check scheduler routes
        scheduler_routes = []
        for rule in app.url_map.iter_rules():
            if '/api/admin/scheduler' in rule.rule:
                scheduler_routes.append(rule.rule)
        
        if scheduler_routes:
            print(f"   ✅ Scheduler routes: {len(scheduler_routes)} found")
            for route in scheduler_routes:
                print(f"      • {route}")
        else:
            print("   ❌ No scheduler routes found")
        
        return True
        
    except Exception as e:
        print(f"❌ App integration test error: {e}")
        return False

def test_api_endpoints():
    """Test scheduler API endpoints"""
    print("\n🌐 Testing API Endpoints")
    print("=" * 30)
    
    try:
        import requests
        
        base_url = "http://127.0.0.1:5000"
        
        # Test endpoints
        endpoints = [
            {
                'name': 'Scheduler Status',
                'url': f"{base_url}/api/admin/scheduler/status",
                'method': 'GET',
                'description': 'Get scheduler status'
            },
            {
                'name': 'Restart Schedulers',
                'url': f"{base_url}/api/admin/scheduler/restart",
                'method': 'POST',
                'description': 'Restart all schedulers'
            }
        ]
        
        for endpoint in endpoints:
            print(f"🔍 Testing: {endpoint['name']}")
            print(f"   URL: {endpoint['url']}")
            print(f"   Method: {endpoint['method']}")
            
            try:
                if endpoint['method'] == 'GET':
                    response = requests.get(endpoint['url'], timeout=10)
                else:
                    response = requests.post(endpoint['url'], timeout=10)
                
                print(f"   ✅ Status Code: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"   ✅ Response Format: Valid JSON")
                        
                        if 'success' in data:
                            print(f"   ✅ Success: {data['success']}")
                        
                        if 'data' in data and isinstance(data['data'], dict):
                            scheduler_data = data['data']
                            if 'manager_running' in scheduler_data:
                                print(f"   ✅ Manager Running: {scheduler_data['manager_running']}")
                            if 'schedulers' in scheduler_data:
                                print(f"   ✅ Schedulers: {len(scheduler_data['schedulers'])} registered")
                        
                    except:
                        print(f"   ⚠️  Response: Non-JSON content")
                else:
                    print(f"   ⚠️  HTTP Status: {response.status_code}")
                    
            except requests.exceptions.ConnectionError:
                print(f"   ❌ Connection Error: Server not running")
            except requests.exceptions.Timeout:
                print(f"   ❌ Timeout Error: Request took too long")
            except Exception as e:
                print(f"   ❌ Error: {e}")
            
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ API endpoints test error: {e}")
        return False

def analyze_scheduler_system():
    """Analyze the scheduler system's purpose and functionality"""
    print("\n📋 Scheduler System Analysis")
    print("=" * 35)
    
    print("🎯 PRIMARY PURPOSE:")
    print("   The Scheduler Initialization system provides centralized management")
    print("   of background schedulers and tasks with delayed initialization to")
    print("   avoid circular dependencies in the Flask application.")
    print()
    
    print("🔧 KEY FEATURES:")
    print("   1. Centralized Scheduler Management")
    print("      • SchedulerManager class for unified control")
    print("      • Registration system for delayed initialization")
    print("      • Thread-safe scheduler lifecycle management")
    print()
    
    print("   2. Circular Dependency Prevention")
    print("      • Delayed initialization with configurable delay")
    print("      • Lazy imports to avoid import cycles")
    print("      • Background thread initialization")
    print()
    
    print("   3. Flask Integration")
    print("      • Blueprint for scheduler management API")
    print("      • Status monitoring endpoints")
    print("      • Restart functionality for schedulers")
    print()
    
    print("   4. Inventory Scheduler Integration")
    print("      • Automatic registration of inventory sync scheduler")
    print("      • Background inventory synchronization tasks")
    print("      • Retry mechanisms and error handling")
    print()
    
    print("📊 BUSINESS VALUE:")
    print("   • Automated background task management")
    print("   • Inventory synchronization across sales channels")
    print("   • Centralized scheduler monitoring and control")
    print("   • Improved system reliability through proper initialization")
    print("   • Reduced manual intervention for routine tasks")
    print()

def run_all_tests():
    """Run all scheduler initialization tests"""
    print("🚀 Scheduler Initialization Test Suite")
    print("=" * 45)
    print()
    
    tests = [
        test_scheduler_init_structure,
        test_scheduler_manager,
        test_global_manager,
        test_inventory_scheduler_registration,
        test_flask_integration,
        test_app_integration
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    # Analyze scheduler system
    analyze_scheduler_system()
    
    # Test API endpoints (requires running server)
    print("⚠️  Note: API endpoint testing requires the server to be running")
    print("   Start the server with: python run_with_waitress.py")
    print()
    
    try:
        test_api_endpoints()
    except Exception as e:
        print(f"❌ API endpoint testing failed: {e}")
        print("   This is expected if the server is not running")
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 30)
    passed = sum(results)
    total = len(results)
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! Scheduler initialization is properly integrated.")
    else:
        print("⚠️  Some tests failed. Please review the scheduler initialization.")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
