#!/usr/bin/env python3
"""
Comprehensive ML Models Runner for Seeded Data
==============================================

This script runs all 4 ML models optimized for the seeded database:
1. Recommendation Model - Personalized product recommendations
2. Price Trends Model - Price prediction and trend analysis
3. Inventory Prediction Model - Stock level and demand forecasting
4. Visual Search Model - Image-based product search features

All models are optimized to work precisely with the seeded data structure
and save their pkl files to the correct organized folders.

Usage:
    python run_all_ml_models.py [--model=MODEL_NAME] [--skip-errors]

Options:
    --model=MODEL_NAME  Run only specific model (recommendation, price, inventory, visual)
    --skip-errors      Continue running other models if one fails
"""

import os
import sys
import time
import argparse
from datetime import datetime

def run_recommendation_model():
    """Run the Recommendation Model"""
    print("🤖 Running Recommendation Model...")
    print("=" * 50)
    
    try:
        # Import and run the recommendation model
        sys.path.append(os.path.join('models', 'Recommendation Model'))
        from recommendation_model import run_advanced_recommendation_system
        
        start_time = time.time()
        recommendations, summary = run_advanced_recommendation_system()
        end_time = time.time()
        
        print(f"✅ Recommendation Model completed in {end_time - start_time:.2f} seconds")
        print(f"   📊 Users analyzed: {summary['total_users_analyzed']}")
        print(f"   🎯 Avg recommendations per user: {summary['avg_recommendations_per_user']}")
        print(f"   🔧 Methods used: {len(summary['recommendation_methods'])}")
        print(f"   📈 Total interactions: {summary['total_interactions']}")
        print(f"   💾 Files saved: {len(summary['files_saved'])}")
        
        return True, summary
        
    except Exception as e:
        print(f"❌ Recommendation Model failed: {e}")
        return False, str(e)

def run_price_trends_model():
    """Run the Price Trends Model"""
    print("\n💰 Running Price Trends Model...")
    print("=" * 50)
    
    try:
        # Import and run the price trends model
        sys.path.append(os.path.join('models', 'Price Trends Model'))
        from price_trend_model import run_advanced_price_trend_prediction
        
        start_time = time.time()
        predictions, summary = run_advanced_price_trend_prediction()
        end_time = time.time()
        
        print(f"✅ Price Trends Model completed in {end_time - start_time:.2f} seconds")
        print(f"   📊 Products analyzed: {summary['total_products_analyzed']}")
        print(f"   📈 Increasing trends: {summary['increasing_trends']}")
        print(f"   📉 Decreasing trends: {summary['decreasing_trends']}")
        print(f"   ➡️  Stable trends: {summary['stable_trends']}")
        print(f"   🎯 High confidence predictions: {summary['high_confidence_predictions']}")
        
        return True, summary
        
    except Exception as e:
        print(f"❌ Price Trends Model failed: {e}")
        return False, str(e)

def run_inventory_model():
    """Run the Inventory Prediction Model"""
    print("\n📦 Running Inventory Prediction Model...")
    print("=" * 50)
    
    try:
        # Import and run the inventory model
        sys.path.append(os.path.join('models', 'Inventory Prediction Model'))
        from inventory_prediction_model import run_advanced_inventory_prediction
        
        start_time = time.time()
        predictions, summary = run_advanced_inventory_prediction()
        end_time = time.time()
        
        print(f"✅ Inventory Model completed in {end_time - start_time:.2f} seconds")
        print(f"   📊 Products analyzed: {summary['total_products_analyzed']}")
        print(f"   🔮 Total predicted sales (7d): {summary['total_predicted_sales_7d']}")
        print(f"   📦 Total recommended restock: {summary['total_recommended_restock']}")
        print(f"   ⚠️  High-risk products: {summary['high_risk_products']}")
        print(f"   🤖 Prediction methods: {len(summary['prediction_methods_used'])}")
        
        return True, summary
        
    except Exception as e:
        print(f"❌ Inventory Model failed: {e}")
        return False, str(e)

def run_visual_search_model():
    """Run the Visual Search Model"""
    print("\n🖼️  Running Visual Search Model...")
    print("=" * 50)
    
    try:
        # Import and run the visual search model
        sys.path.append(os.path.join('models', 'Visual Search'))
        from visual_search_model import run_advanced_visual_search_feature_extraction
        
        start_time = time.time()
        features, summary = run_advanced_visual_search_feature_extraction()
        end_time = time.time()
        
        print(f"✅ Visual Search Model completed in {end_time - start_time:.2f} seconds")
        print(f"   📊 Products processed: {summary['total_products_processed']}")
        print(f"   🖼️  Images analyzed: {summary['total_images_analyzed']}")
        print(f"   🎯 Feature extraction success rate: {summary['success_rate']:.1%}")
        print(f"   🤖 Models used: {len(summary['models_used'])}")
        print(f"   📊 Features per image: {summary['features_per_image']}")
        
        return True, summary
        
    except Exception as e:
        print(f"❌ Visual Search Model failed: {e}")
        return False, str(e)

def main():
    """Main function to run all ML models"""
    parser = argparse.ArgumentParser(description='Run ML models optimized for seeded data')
    parser.add_argument('--model', choices=['recommendation', 'price', 'inventory', 'visual'], 
                       help='Run only specific model')
    parser.add_argument('--skip-errors', action='store_true', 
                       help='Continue running other models if one fails')
    
    args = parser.parse_args()
    
    print("🚀 ALLORA ML MODELS RUNNER")
    print("=" * 60)
    print(f"🕐 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 Optimized for seeded database data")
    print()
    
    # Define models to run
    models = {
        'recommendation': run_recommendation_model,
        'price': run_price_trends_model,
        'inventory': run_inventory_model,
        'visual': run_visual_search_model
    }
    
    # Filter models if specific model requested
    if args.model:
        models = {args.model: models[args.model]}
    
    # Run models
    results = {}
    total_start_time = time.time()
    
    for model_name, model_func in models.items():
        try:
            success, summary = model_func()
            results[model_name] = {'success': success, 'summary': summary}
            
            if not success and not args.skip_errors:
                print(f"\n❌ Stopping due to {model_name} model failure")
                break
                
        except KeyboardInterrupt:
            print(f"\n⚠️  Interrupted by user during {model_name} model")
            break
        except Exception as e:
            print(f"\n❌ Unexpected error in {model_name} model: {e}")
            results[model_name] = {'success': False, 'summary': str(e)}
            
            if not args.skip_errors:
                break
    
    total_end_time = time.time()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 EXECUTION SUMMARY")
    print("=" * 60)
    
    successful_models = [name for name, result in results.items() if result['success']]
    failed_models = [name for name, result in results.items() if not result['success']]
    
    print(f"🕐 Total execution time: {total_end_time - total_start_time:.2f} seconds")
    print(f"✅ Successful models: {len(successful_models)}/{len(results)}")
    print(f"❌ Failed models: {len(failed_models)}")
    print()
    
    if successful_models:
        print("✅ SUCCESSFUL MODELS:")
        for model_name in successful_models:
            print(f"   🎯 {model_name.title()} Model - Ready for use")
    
    if failed_models:
        print("\n❌ FAILED MODELS:")
        for model_name in failed_models:
            error = results[model_name]['summary']
            print(f"   ⚠️  {model_name.title()} Model - {error}")
    
    print(f"\n💾 Model files saved to respective pkl folders:")
    print(f"   📁 models/Recommendation Model/pkl/")
    print(f"   📁 models/Price Trends Model/pkl/")
    print(f"   📁 models/Inventory Prediction Model/pkl/")
    print(f"   📁 models/Visual Search/pkl/")
    
    print(f"\n🎉 ML Models execution completed!")
    
    # Return success status
    return len(failed_models) == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
