import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from dotenv import load_dotenv

load_dotenv()

def test_email_config():
    try:
        # Email configuration
        smtp_server = os.getenv('MAIL_SERVER', 'smtp.gmail.com')
        smtp_port = int(os.getenv('MAIL_PORT', 587))
        username = os.getenv('MAIL_USERNAME')
        password = os.getenv('MAIL_PASSWORD')
        
        if not username or not password:
            print("❌ Email credentials not configured")
            return False
        
        # Create message
        msg = MIMEMultipart()
        msg['From'] = username
        msg['To'] = "<EMAIL>"  # Send to self for testing
        msg['Subject'] = "Allora E-commerce - Email Test"
        
        body = """
        This is a test email from Allora E-commerce platform.
        
        If you receive this email, the email configuration is working correctly.
        
        Best regards,
        Allora Team
        """
        msg.attach(MIMEText(body, 'plain'))
        
        # Send email
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(username, password)
        text = msg.as_string()
        server.sendmail(username, msg['To'], text)
        server.quit()
        
        print("✅ Email sent successfully")
        return True
        
    except Exception as e:
        print(f"❌ Email test failed: {e}")
        return False

if __name__ == "__main__":
    test_email_config()