#!/usr/bin/env python3
"""
Database Performance Optimization Script
Adds indexes to improve product search and filtering performance
"""

import os
import sys
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import text

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the app and database
from app import app, db

def add_performance_indexes():
    """Add database indexes for better performance"""
    
    print("🚀 Starting database performance optimization...")
    
    with app.app_context():
        try:
            # List of indexes to create (MySQL compatible)
            indexes = [
                # Product table indexes for search performance
                ("idx_product_name", "CREATE INDEX idx_product_name ON product (name);"),
                ("idx_product_category", "CREATE INDEX idx_product_category ON product (category);"),
                ("idx_product_brand", "CREATE INDEX idx_product_brand ON product (brand);"),
                ("idx_product_material", "CREATE INDEX idx_product_material ON product (material);"),
                ("idx_product_price", "CREATE INDEX idx_product_price ON product (price);"),
                ("idx_product_sustainability", "CREATE INDEX idx_product_sustainability ON product (sustainability_score);"),
                ("idx_product_stock", "CREATE INDEX idx_product_stock ON product (stock_quantity);"),
                ("idx_product_rating", "CREATE INDEX idx_product_rating ON product (average_rating);"),

                # Composite indexes for common query patterns
                ("idx_product_category_price", "CREATE INDEX idx_product_category_price ON product (category, price);"),
                ("idx_product_brand_price", "CREATE INDEX idx_product_brand_price ON product (brand, price);"),

                # Other table indexes
                ("idx_sales_product_date", "CREATE INDEX idx_sales_product_date ON sales (product_id, sale_date);"),
                ("idx_price_history_product_date", "CREATE INDEX idx_price_history_product_date ON price_history (product_id, recorded_date);"),
                ("idx_cart_user", "CREATE INDEX idx_cart_user ON cart_item (user_id);"),
                ("idx_recently_viewed_user", "CREATE INDEX idx_recently_viewed_user ON recently_viewed (user_id, viewed_at);"),
            ]
            
            success_count = 0
            error_count = 0
            
            for index_name, sql_command in indexes:
                try:
                    print(f"📊 Creating index: {index_name}")

                    # Check if index already exists (MySQL way)
                    check_sql = f"""
                    SELECT COUNT(*) as count FROM information_schema.statistics
                    WHERE table_schema = DATABASE()
                    AND table_name = '{sql_command.split('ON ')[1].split(' (')[0]}'
                    AND index_name = '{index_name}';
                    """

                    result = db.session.execute(text(check_sql)).fetchone()

                    if result and result[0] > 0:
                        print(f"⚠️  Index {index_name} already exists, skipping...")
                        error_count += 1
                        continue

                    # Create the index
                    db.session.execute(text(sql_command))
                    db.session.commit()
                    print(f"✅ Successfully created index: {index_name}")
                    success_count += 1

                except Exception as e:
                    error_msg = str(e)
                    if "Duplicate key name" in error_msg or "already exists" in error_msg:
                        print(f"⚠️  Index {index_name} already exists, skipping...")
                    else:
                        print(f"⚠️  Warning creating index {index_name}: {error_msg}")
                    db.session.rollback()
                    error_count += 1
                    continue
            
            print(f"\n🎉 Database optimization completed!")
            print(f"✅ Successfully created: {success_count} indexes")
            if error_count > 0:
                print(f"⚠️  Warnings/Skipped: {error_count} indexes")
            
            # Analyze tables for better query planning (PostgreSQL/MySQL)
            try:
                print("\n📈 Analyzing tables for query optimization...")
                tables_to_analyze = ['product', 'sales', 'price_history', 'cart_item', 'orders', 'recently_viewed']
                
                for table in tables_to_analyze:
                    try:
                        # Try PostgreSQL ANALYZE syntax first
                        db.session.execute(text(f"ANALYZE {table};"))
                        print(f"✅ Analyzed table: {table}")
                    except:
                        try:
                            # Try MySQL ANALYZE syntax
                            db.session.execute(text(f"ANALYZE TABLE {table};"))
                            print(f"✅ Analyzed table: {table}")
                        except:
                            # Skip if not supported
                            print(f"⚠️  Table analysis not supported for: {table}")
                            continue
                
                db.session.commit()
                
            except Exception as e:
                print(f"⚠️  Table analysis skipped: {str(e)}")
                db.session.rollback()
            
            print("\n🚀 Performance optimization complete!")
            print("📊 Your database queries should now be significantly faster!")
            
        except Exception as e:
            print(f"❌ Error during database optimization: {str(e)}")
            db.session.rollback()
            return False
    
    return True

def check_existing_indexes():
    """Check what indexes already exist"""

    print("🔍 Checking existing database indexes...")

    with app.app_context():
        try:
            # MySQL way to check indexes
            result = db.session.execute(text("""
                SELECT DISTINCT index_name FROM information_schema.statistics
                WHERE table_schema = DATABASE()
                AND table_name = 'product'
                AND index_name != 'PRIMARY'
                ORDER BY index_name;
            """))

            indexes = result.fetchall()

            if indexes:
                print("📋 Existing indexes on product table:")
                for idx in indexes:
                    print(f"   • {idx[0]}")
            else:
                print("📋 No custom indexes found on product table")

        except Exception as e:
            print(f"⚠️  Could not check existing indexes: {str(e)}")

if __name__ == "__main__":
    print("🔧 Allora Database Performance Optimization")
    print("=" * 50)
    
    # Check existing indexes first
    check_existing_indexes()
    
    print("\n" + "=" * 50)
    
    # Add performance indexes
    success = add_performance_indexes()
    
    if success:
        print("\n✅ Database optimization completed successfully!")
        print("🚀 Your Allora e-commerce platform should now load much faster!")
    else:
        print("\n❌ Database optimization failed. Please check the error messages above.")
        sys.exit(1)
