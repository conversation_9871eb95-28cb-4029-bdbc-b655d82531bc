#!/usr/bin/env python3
"""
Create ML Test Data for Smart Product Discovery
==============================================

Creates sample user interactions and behavior data to test:
- Personalized recommendations
- Recently viewed products
- Trending analysis
- User behavior tracking

Author: Allora Development Team
Date: 2025-07-11
"""

import sys
import os
from datetime import datetime, timedelta
import random

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import Flask app and models
from app import app, db, User, Product, UserInteractionLog

def create_test_users():
    """Create test users for ML testing"""
    print("👥 Creating test users...")
    
    with app.app_context():
        try:
            # Create test users if they don't exist
            test_users = [
                {
                    'username': 'eco_shopper_1',
                    'email': '<EMAIL>',
                    'password': 'test123',
                    'first_name': 'Eco',
                    'last_name': 'Shopper1'
                },
                {
                    'username': 'sustainable_buyer',
                    'email': '<EMAIL>',
                    'password': 'test123',
                    'first_name': 'Sustainable',
                    'last_name': 'Buyer'
                },
                {
                    'username': 'green_consumer',
                    'email': '<EMAIL>',
                    'password': 'test123',
                    'first_name': 'Green',
                    'last_name': 'Consumer'
                }
            ]
            
            created_users = []
            for user_data in test_users:
                existing_user = User.query.filter_by(email=user_data['email']).first()
                if not existing_user:
                    user = User(
                        username=user_data['username'],
                        email=user_data['email'],
                        password=user_data['password'],
                        first_name=user_data['first_name'],
                        last_name=user_data['last_name']
                    )
                    db.session.add(user)
                    created_users.append(user)
                else:
                    created_users.append(existing_user)
            
            db.session.commit()
            print(f"✅ Created/found {len(created_users)} test users")
            return created_users
            
        except Exception as e:
            print(f"❌ Error creating test users: {e}")
            db.session.rollback()
            return []

def create_user_interactions():
    """Create realistic user interactions for ML testing"""
    print("🔍 Creating user interactions...")
    
    with app.app_context():
        try:
            # Get test users
            users = User.query.limit(3).all()
            if not users:
                print("❌ No users found")
                return False
            
            # Get products
            products = Product.query.all()
            if not products:
                print("❌ No products found")
                return False
            
            print(f"📦 Found {len(products)} products and {len(users)} users")
            
            interactions_created = 0
            
            # Create interactions for each user over the last 30 days
            for user in users:
                # Each user views 10-20 products
                num_interactions = random.randint(10, 20)
                
                # Select random products for this user
                user_products = random.sample(products, min(num_interactions, len(products)))
                
                for i, product in enumerate(user_products):
                    # Create interactions over the last 30 days
                    days_ago = random.randint(1, 30)
                    hours_ago = random.randint(0, 23)
                    interaction_time = datetime.utcnow() - timedelta(days=days_ago, hours=hours_ago)
                    
                    # Create view interaction
                    interaction = UserInteractionLog(
                        user_id=user.id,
                        product_id=product.id,
                        interaction_type='view',
                        interaction_value=random.uniform(0.5, 1.0),  # Engagement score
                        timestamp=interaction_time,
                        session_id=f'session_{user.id}_{i}',
                        context_data={
                            'page': 'product_detail',
                            'duration': random.randint(30, 300),  # seconds
                            'source': 'search' if random.random() > 0.5 else 'category'
                        }
                    )
                    db.session.add(interaction)
                    interactions_created += 1
                    
                    # Sometimes add click or add_to_cart interactions
                    if random.random() > 0.7:  # 30% chance
                        click_interaction = UserInteractionLog(
                            user_id=user.id,
                            product_id=product.id,
                            interaction_type='click',
                            interaction_value=random.uniform(0.7, 1.0),
                            timestamp=interaction_time + timedelta(seconds=random.randint(10, 60)),
                            session_id=f'session_{user.id}_{i}',
                            context_data={
                                'element': 'product_image',
                                'page': 'product_detail'
                            }
                        )
                        db.session.add(click_interaction)
                        interactions_created += 1
                    
                    if random.random() > 0.9:  # 10% chance
                        cart_interaction = UserInteractionLog(
                            user_id=user.id,
                            product_id=product.id,
                            interaction_type='add_to_cart',
                            interaction_value=1.0,
                            timestamp=interaction_time + timedelta(seconds=random.randint(60, 300)),
                            session_id=f'session_{user.id}_{i}',
                            context_data={
                                'quantity': random.randint(1, 3),
                                'page': 'product_detail'
                            }
                        )
                        db.session.add(cart_interaction)
                        interactions_created += 1
            
            db.session.commit()
            print(f"✅ Created {interactions_created} user interactions")
            return True
            
        except Exception as e:
            print(f"❌ Error creating user interactions: {e}")
            db.session.rollback()
            return False

def test_ml_apis():
    """Test the ML-powered APIs"""
    print("🤖 Testing ML-powered APIs...")
    
    with app.app_context():
        try:
            users = User.query.limit(3).all()
            if not users:
                print("❌ No users found for testing")
                return False
            
            test_user = users[0]
            print(f"🧪 Testing with user: {test_user.username} (ID: {test_user.id})")
            
            # Test personalized recommendations
            from smart_discovery_api import get_personalized_recommendations
            print("🎯 Testing personalized recommendations...")
            
            # Test recently viewed
            print("👁️ Testing recently viewed...")
            
            # Test trending
            print("🔥 Testing trending products...")
            
            print("✅ ML APIs are ready for testing")
            return True
            
        except Exception as e:
            print(f"❌ Error testing ML APIs: {e}")
            return False

def main():
    print("🤖 SMART PRODUCT DISCOVERY ML TEST DATA CREATOR")
    print("=" * 60)
    
    # Create test users
    users = create_test_users()
    if not users:
        print("❌ Failed to create test users")
        return False
    
    # Create user interactions
    if not create_user_interactions():
        print("❌ Failed to create user interactions")
        return False
    
    # Test ML APIs
    if not test_ml_apis():
        print("❌ Failed to test ML APIs")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 ML TEST DATA CREATION COMPLETE!")
    print("=" * 60)
    
    print("\n✅ What was created:")
    print("   • Test users with realistic profiles")
    print("   • User interaction history (views, clicks, cart additions)")
    print("   • Behavior patterns for ML analysis")
    print("   • Data for personalized recommendations")
    print("   • Recently viewed product history")
    print("   • Trending analysis data")
    
    print("\n🚀 Now you can test:")
    print("   • Featured Products: Admin-configured high-sustainability products")
    print("   • Trending Products: ML-powered trending analysis")
    print("   • Personalized Recommendations: AdvancedRecommendationSystem")
    print("   • Recently Viewed: User behavior tracking")
    
    print("\n🌐 Visit http://localhost:3000 to see the Smart Product Discovery in action!")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
