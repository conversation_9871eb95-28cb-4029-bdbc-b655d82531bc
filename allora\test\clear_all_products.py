#!/usr/bin/env python3
"""
Clear All Products Script
This script will remove all products from the database and related data.
"""

import os
import sys
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def clear_all_products():
    """Clear all products and related data from the database"""
    try:
        from app import app, db, Product, OrderItem, CartItem, Review, Wishlist
        
        with app.app_context():
            print("🗑️  Starting product cleanup process...")
            print("=" * 50)
            
            # Get initial count
            initial_count = Product.query.count()
            print(f"📊 Found {initial_count} products in database")
            
            if initial_count == 0:
                print("✅ Database is already empty!")
                return
            
            # Show products before deletion
            print("\n📋 Products to be deleted:")
            products = Product.query.all()
            for i, product in enumerate(products[:10], 1):  # Show first 10
                print(f"  {i}. ID: {product.id} - {product.name} - ₹{product.price}")
            
            if len(products) > 10:
                print(f"  ... and {len(products) - 10} more products")
            
            # Confirm deletion
            print(f"\n⚠️  WARNING: This will permanently delete ALL {initial_count} products!")
            print("This action cannot be undone.")
            
            confirm = input("\nType 'DELETE ALL PRODUCTS' to confirm: ")
            if confirm != 'DELETE ALL PRODUCTS':
                print("❌ Operation cancelled.")
                return
            
            print("\n🔄 Starting deletion process...")
            
            # Delete related data first (to avoid foreign key constraints)
            print("1. Clearing cart items...")
            cart_items_deleted = CartItem.query.filter(CartItem.product_id.in_([p.id for p in products])).delete(synchronize_session=False)
            print(f"   ✅ Deleted {cart_items_deleted} cart items")
            
            print("2. Clearing order items...")
            order_items_deleted = OrderItem.query.filter(OrderItem.product_id.in_([p.id for p in products])).delete(synchronize_session=False)
            print(f"   ✅ Deleted {order_items_deleted} order items")
            
            print("3. Clearing reviews...")
            reviews_deleted = Review.query.filter(Review.product_id.in_([p.id for p in products])).delete(synchronize_session=False)
            print(f"   ✅ Deleted {reviews_deleted} reviews")
            
            print("4. Clearing wishlist items...")
            wishlist_deleted = Wishlist.query.filter(Wishlist.product_id.in_([p.id for p in products])).delete(synchronize_session=False)
            print(f"   ✅ Deleted {wishlist_deleted} wishlist items")
            
            print("5. Clearing products...")
            products_deleted = Product.query.delete()
            print(f"   ✅ Deleted {products_deleted} products")
            
            # Commit all changes
            db.session.commit()
            
            # Verify deletion
            final_count = Product.query.count()
            print(f"\n📊 Final product count: {final_count}")
            
            if final_count == 0:
                print("✅ All products successfully deleted!")
                print(f"📈 Summary:")
                print(f"   - Products deleted: {products_deleted}")
                print(f"   - Cart items deleted: {cart_items_deleted}")
                print(f"   - Order items deleted: {order_items_deleted}")
                print(f"   - Reviews deleted: {reviews_deleted}")
                print(f"   - Wishlist items deleted: {wishlist_deleted}")
            else:
                print(f"⚠️  Warning: {final_count} products still remain")
                
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure you're running this from the backend directory")
    except Exception as e:
        print(f"❌ Error during cleanup: {e}")
        try:
            db.session.rollback()
        except:
            pass

def show_product_count():
    """Show current product count without deleting"""
    try:
        from app import app, db, Product
        
        with app.app_context():
            count = Product.query.count()
            print(f"📊 Current products in database: {count}")
            
            if count > 0:
                print("\n📋 Product list:")
                products = Product.query.all()
                for i, product in enumerate(products, 1):
                    print(f"  {i}. ID: {product.id} - {product.name} - ₹{product.price}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🧹 Product Database Cleanup Tool")
    print("=" * 40)
    
    if len(sys.argv) > 1 and sys.argv[1] == "--count":
        show_product_count()
    else:
        clear_all_products()
