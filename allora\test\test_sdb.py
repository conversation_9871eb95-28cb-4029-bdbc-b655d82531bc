#!/usr/bin/env python3
"""
Quick Database Test
===================
Test if database models are working correctly.
"""

from app import app, db, CartItem, Order, Wishlist, User

def test_database():
    """Test database connectivity and models"""
    with app.app_context():
        try:
            print("🔍 Testing database models...")
            
            # Test User model
            user_count = User.query.count()
            print(f"✅ Users: {user_count}")
            
            # Test CartItem model
            cart_count = CartItem.query.count()
            print(f"✅ Cart items: {cart_count}")
            
            # Test Order model
            order_count = Order.query.count()
            print(f"✅ Orders: {order_count}")
            
            # Test Wishlist model
            wishlist_count = Wishlist.query.count()
            print(f"✅ Wishlist items: {wishlist_count}")
            
            print("✅ All database models working correctly!")
            return True
            
        except Exception as e:
            print(f"❌ Database error: {e}")
            return False

if __name__ == "__main__":
    test_database()
