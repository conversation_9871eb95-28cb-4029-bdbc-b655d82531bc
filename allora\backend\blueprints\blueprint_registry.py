"""
Blueprint Registry
==================

Centralized blueprint registration system for the Allora API.
Handles all blueprint registration with proper versioning and error handling.
"""

import logging
from flask import Flask

logger = logging.getLogger(__name__)

def register_all_blueprints(app: Flask) -> None:
    """
    Register all blueprints with the Flask application.
    
    Args:
        app: Flask application instance
    """
    logger.info("Starting blueprint registration...")
    
    # Track registration success/failure
    registered_blueprints = []
    failed_blueprints = []
    
    # New organized blueprints
    new_blueprints = [
        {
            'name': 'Authentication',
            'module': 'blueprints.auth',
            'blueprint': 'auth_bp',
            'critical': True
        },
        {
            'name': 'Products',
            'module': 'blueprints.products',
            'blueprint': 'products_bp',
            'critical': True
        },
        {
            'name': 'Orders',
            'module': 'blueprints.orders',
            'blueprint': 'orders_bp',
            'critical': True
        }
    ]
    
    # Existing blueprints (updated with versioning)
    existing_blueprints = [
        {
            'name': 'Search System',
            'module': 'search_system.search_api',
            'blueprint': 'search_bp',
            'critical': False
        },
        {
            'name': 'Order Fulfillment',
            'module': 'order_fulfillment.fulfillment_api',
            'blueprint': 'fulfillment_bp',
            'critical': False
        },
        {
            'name': 'RMA System',
            'module': 'rma.rma_api',
            'blueprint': 'rma_bp',
            'critical': False
        },
        {
            'name': 'Sustainability',
            'module': 'sustainability_api',
            'blueprint': 'sustainability_bp',
            'critical': False
        },
        {
            'name': 'Community Highlights',
            'module': 'community_highlights_api',
            'blueprint': 'community_highlights_bp',
            'critical': False
        }
    ]
    
    # Register new blueprints
    for blueprint_config in new_blueprints:
        success = _register_blueprint(app, blueprint_config)
        if success:
            registered_blueprints.append(blueprint_config['name'])
        else:
            failed_blueprints.append(blueprint_config['name'])
    
    # Register existing blueprints
    for blueprint_config in existing_blueprints:
        success = _register_blueprint(app, blueprint_config)
        if success:
            registered_blueprints.append(blueprint_config['name'])
        else:
            failed_blueprints.append(blueprint_config['name'])
    
    # Register additional blueprints that might exist
    additional_blueprints = [
        {
            'name': 'Search Analytics',
            'module': 'search_system.search_analytics_api',
            'blueprint': 'search_analytics_bp',
            'critical': False
        },
        {
            'name': 'User Behavior',
            'module': 'models.RecommendationModel.user_behavior_api',
            'blueprint': 'behavior_api',
            'critical': False
        },
        {
            'name': 'Recommendation API',
            'module': 'models.RecommendationModel.recommendation_api',
            'blueprint': 'recommendation_api',
            'critical': False
        },
        {
            'name': 'Webhook Handlers',
            'module': 'webhook_handlers',
            'blueprint': 'webhook_bp',
            'critical': False
        },
        {
            'name': 'Tracking Dashboard',
            'module': 'tracking_dashboard',
            'blueprint': 'dashboard_bp',
            'critical': False
        }
    ]
    
    for blueprint_config in additional_blueprints:
        success = _register_blueprint(app, blueprint_config)
        if success:
            registered_blueprints.append(blueprint_config['name'])
        else:
            failed_blueprints.append(blueprint_config['name'])
    
    # Log registration summary
    logger.info(f"Blueprint registration complete:")
    logger.info(f"✅ Successfully registered: {len(registered_blueprints)} blueprints")
    for name in registered_blueprints:
        logger.info(f"   • {name}")
    
    if failed_blueprints:
        logger.warning(f"⚠️  Failed to register: {len(failed_blueprints)} blueprints")
        for name in failed_blueprints:
            logger.warning(f"   • {name}")
    
    # Log total routes registered
    total_routes = len(list(app.url_map.iter_rules()))
    logger.info(f"📊 Total API routes registered: {total_routes}")

def _register_blueprint(app: Flask, blueprint_config: dict) -> bool:
    """
    Register a single blueprint with error handling.
    
    Args:
        app: Flask application instance
        blueprint_config: Blueprint configuration dictionary
    
    Returns:
        True if registration successful, False otherwise
    """
    try:
        module_name = blueprint_config['module']
        blueprint_name = blueprint_config['blueprint']
        display_name = blueprint_config['name']
        is_critical = blueprint_config.get('critical', False)
        
        # Import the module
        module = __import__(module_name, fromlist=[blueprint_name])
        blueprint = getattr(module, blueprint_name)
        
        # Register the blueprint
        app.register_blueprint(blueprint)
        
        logger.info(f"✅ {display_name} blueprint registered successfully")
        return True
        
    except ImportError as e:
        if is_critical:
            logger.error(f"❌ Critical blueprint '{display_name}' failed to import: {e}")
            raise  # Re-raise for critical blueprints
        else:
            logger.warning(f"⚠️  Optional blueprint '{display_name}' could not be imported: {e}")
            return False
    
    except AttributeError as e:
        if is_critical:
            logger.error(f"❌ Critical blueprint '{display_name}' attribute error: {e}")
            raise
        else:
            logger.warning(f"⚠️  Optional blueprint '{display_name}' attribute error: {e}")
            return False
    
    except Exception as e:
        if is_critical:
            logger.error(f"❌ Critical blueprint '{display_name}' registration failed: {e}")
            raise
        else:
            logger.warning(f"⚠️  Optional blueprint '{display_name}' registration failed: {e}")
            return False

def get_registered_routes(app: Flask) -> dict:
    """
    Get information about all registered routes.
    
    Args:
        app: Flask application instance
    
    Returns:
        Dictionary with route information
    """
    routes_info = {
        'total_routes': 0,
        'routes_by_method': {},
        'routes_by_blueprint': {},
        'versioned_routes': 0,
        'unversioned_routes': 0
    }
    
    for rule in app.url_map.iter_rules():
        routes_info['total_routes'] += 1
        
        # Count by HTTP method
        methods = rule.methods - {'HEAD', 'OPTIONS'}
        for method in methods:
            if method not in routes_info['routes_by_method']:
                routes_info['routes_by_method'][method] = 0
            routes_info['routes_by_method'][method] += 1
        
        # Count by blueprint
        blueprint_name = rule.endpoint.split('.')[0] if '.' in rule.endpoint else 'main'
        if blueprint_name not in routes_info['routes_by_blueprint']:
            routes_info['routes_by_blueprint'][blueprint_name] = 0
        routes_info['routes_by_blueprint'][blueprint_name] += 1
        
        # Count versioned vs unversioned
        if '/api/v1/' in rule.rule:
            routes_info['versioned_routes'] += 1
        elif '/api/' in rule.rule:
            routes_info['unversioned_routes'] += 1
    
    return routes_info

def print_routes_summary(app: Flask) -> None:
    """
    Print a summary of all registered routes.
    
    Args:
        app: Flask application instance
    """
    routes_info = get_registered_routes(app)
    
    print("\n" + "="*60)
    print("🚀 ALLORA API ROUTES SUMMARY")
    print("="*60)
    print(f"📊 Total Routes: {routes_info['total_routes']}")
    print(f"✅ Versioned Routes (/api/v1/): {routes_info['versioned_routes']}")
    print(f"⚠️  Unversioned Routes (/api/): {routes_info['unversioned_routes']}")
    
    print(f"\n📋 Routes by HTTP Method:")
    for method, count in sorted(routes_info['routes_by_method'].items()):
        print(f"   {method}: {count}")
    
    print(f"\n🧩 Routes by Blueprint:")
    for blueprint, count in sorted(routes_info['routes_by_blueprint'].items()):
        print(f"   {blueprint}: {count}")
    
    print("="*60)
