import sentry_sdk
import os
from dotenv import load_dotenv

load_dotenv()

def test_sentry_config():
    try:
        sentry_dsn = os.getenv('SENTRY_DSN')

        if not sentry_dsn:
            print("❌ Sentry DSN not configured")
            return False

        # Initialize Sentry
        sentry_sdk.init(
            dsn=sentry_dsn,
            traces_sample_rate=1.0
        )

        # Test error capture
        try:
            1 / 0  # Intentional error
        except ZeroDivisionError:
            sentry_sdk.capture_exception()

        print("✅ Sentry test error sent")
        print("Check your Sentry dashboard for the test error")
        return True

    except Exception as e:
        print(f"❌ Sentry test failed: {e}")
        return False

if __name__ == "__main__":
    test_sentry_config()