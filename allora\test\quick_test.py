import requests

try:
    # Test rating sort
    print("Testing rating sort...")
    response = requests.get("http://localhost:5000/api/products?sort_by=rating&sort_order=desc&per_page=5")
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        products = data.get('products', [])
        print("Top 5 products by rating:")
        for p in products:
            print(f"- {p['name']}: {p['average_rating']}⭐ ({p['total_reviews']} reviews)")
    else:
        print(f"Error: {response.text}")
        
    print("\nTesting popularity sort...")
    response = requests.get("http://localhost:5000/api/products?sort_by=popularity&sort_order=desc&per_page=5")
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        products = data.get('products', [])
        print("Top 5 products by popularity:")
        for p in products:
            print(f"- {p['name']}: {p['total_reviews']} reviews ({p['average_rating']}⭐)")
    else:
        print(f"Error: {response.text}")
        
except Exception as e:
    print(f"Error: {e}")
