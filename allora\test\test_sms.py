import requests
import os
from dotenv import load_dotenv

load_dotenv()

# Test SMS configuration using Fast2SMS API
def test_sms_config():
    print("🚀 Starting SMS Test...")
    print("=" * 40)

    try:
        # Get credentials with debugging
        api_key = os.getenv('SMS_API_KEY')
        sender_id = os.getenv('SMS_SENDER_ID')

        print(f"🔑 API Key: {api_key[:10]}..." if api_key else "❌ No API Key found")
        print(f"📤 Sender ID: {sender_id}")

        if not api_key:
            print("❌ SMS API key not configured in .env file")
            return False

        if not sender_id:
            print("❌ SMS Sender ID not configured in .env file")
            return False

        # Fast2SMS API endpoint
        url = "https://www.fast2sms.com/dev/bulkV2"
        print(f"🌐 API Endpoint: {url}")

        # Fast2SMS parameters
        data = {
            'authorization': api_key,
            'sender_id': sender_id,
            'message': 'Test message from Allora E-commerce platform',
            'language': 'english',
            'route': 'q',
            'numbers': '918307213228'  # with country code
        }

        print(f"📋 Request Data: {data}")

        # Fast2SMS uses headers for authorization
        headers = {
            'authorization': api_key,
            'Content-Type': 'application/x-www-form-urlencoded'
        }

        print("📡 Sending SMS request...")

        # Add timeout to prevent hanging
        response = requests.post(url, data=data, headers=headers, timeout=30)

        print(f"📊 Response Status: {response.status_code}")
        print(f"📄 Response Headers: {dict(response.headers)}")
        print(f"📝 Response Text: {response.text}")

        if response.status_code == 200:
            try:
                result = response.json()
                print(f"📦 JSON Response: {result}")

                if result.get('return') == True:
                    print("✅ SMS sent successfully!")
                    return True
                else:
                    print(f"❌ SMS API returned error: {result}")
                    return False
            except Exception as json_error:
                print(f"❌ Failed to parse JSON response: {json_error}")
                print(f"Raw response: {response.text}")
                return False
        else:
            print(f"❌ SMS failed with HTTP status {response.status_code}")
            print(f"Error details: {response.text}")
            return False

    except requests.exceptions.Timeout:
        print("❌ Request timed out after 30 seconds")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - check your internet connection")
        return False
    except Exception as e:
        print(f"❌ SMS test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Fast2SMS API Test Script")
    print("=" * 50)

    try:
        result = test_sms_config()

        print("\n" + "=" * 50)
        if result:
            print("🎉 SMS Test COMPLETED SUCCESSFULLY!")
        else:
            print("❌ SMS Test FAILED!")
        print("=" * 50)

    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()

    print("🏁 Test script finished")