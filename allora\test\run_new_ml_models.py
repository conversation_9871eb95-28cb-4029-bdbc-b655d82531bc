#!/usr/bin/env python3
"""
New Advanced ML Models Runner for Seeded Data
=============================================

This script runs all 4 new advanced ML models optimized for seeded database:
1. Recommendation Model - Hybrid collaborative + content-based filtering
2. Price Trends Model - Ensemble price prediction with technical analysis
3. Inventory Prediction Model - Demand forecasting with safety stock calculation
4. Visual Search Model - Multi-feature visual similarity with deep learning

All models are designed to work precisely with seeded data and save pkl files
directly in their respective model folders.

Usage:
    python run_new_ml_models.py [--model=MODEL_NAME] [--skip-errors]

Options:
    --model=MODEL_NAME  Run only specific model (recommendation, price, inventory, visual)
    --skip-errors      Continue running other models if one fails
"""

import os
import sys
import time
import argparse
from datetime import datetime

def run_recommendation_model():
    """Run the new Recommendation Model"""
    print("🤖 Running New Advanced Recommendation Model...")
    print("=" * 60)
    
    try:
        # Import and run the recommendation model
        sys.path.append(os.path.join('models', 'Recommendation Model'))
        from recommendation_model import SeededDataRecommendationModel
        
        start_time = time.time()
        model = SeededDataRecommendationModel()
        success = model.train_and_save_model()
        end_time = time.time()
        
        if success:
            print(f"✅ Recommendation Model completed in {end_time - start_time:.2f} seconds")
            print(f"   📁 Model saved to: models/Recommendation Model/recommendation_model.pkl")
            print(f"   🎯 Features: Hybrid collaborative + content-based filtering")
            print(f"   🔧 Optimized for: 50 users, 100 products, seeded interactions")
            
            return True, {
                'execution_time': end_time - start_time,
                'model_file': 'models/Recommendation Model/recommendation_model.pkl',
                'features': 'hybrid_collaborative_content_filtering',
                'optimization': 'seeded_data_v5.0'
            }
        else:
            return False, "Model training failed"
        
    except Exception as e:
        print(f"❌ Recommendation Model failed: {e}")
        return False, str(e)

def run_price_trends_model():
    """Run the new Price Trends Model"""
    print("\n💰 Running New Advanced Price Trends Model...")
    print("=" * 60)
    
    try:
        # Import and run the price trends model
        sys.path.append(os.path.join('models', 'Price Trends Model'))
        from price_trends_model import SeededDataPriceTrendsModel
        
        start_time = time.time()
        model = SeededDataPriceTrendsModel()
        success = model.train_and_save_model()
        end_time = time.time()
        
        if success:
            print(f"✅ Price Trends Model completed in {end_time - start_time:.2f} seconds")
            print(f"   📁 Model saved to: models/Price Trends Model/price_trends_model.pkl")
            print(f"   🎯 Features: Ensemble ML + technical indicators + seasonality")
            print(f"   🔧 Optimized for: 100 products, category-specific volatility")
            
            return True, {
                'execution_time': end_time - start_time,
                'model_file': 'models/Price Trends Model/price_trends_model.pkl',
                'features': 'ensemble_ml_technical_analysis',
                'optimization': 'seeded_data_v5.0'
            }
        else:
            return False, "Model training failed"
        
    except Exception as e:
        print(f"❌ Price Trends Model failed: {e}")
        return False, str(e)

def run_inventory_model():
    """Run the new Inventory Prediction Model"""
    print("\n📦 Running New Advanced Inventory Prediction Model...")
    print("=" * 60)
    
    try:
        # Import and run the inventory model
        sys.path.append(os.path.join('models', 'Inventory Prediction Model'))
        from inventory_model import SeededDataInventoryModel
        
        start_time = time.time()
        model = SeededDataInventoryModel()
        success = model.train_and_save_model()
        end_time = time.time()
        
        if success:
            print(f"✅ Inventory Model completed in {end_time - start_time:.2f} seconds")
            print(f"   📁 Model saved to: models/Inventory Prediction Model/inventory_model.pkl")
            print(f"   🎯 Features: Demand forecasting + safety stock + reorder points")
            print(f"   🔧 Optimized for: Category-specific demand patterns + seasonality")
            
            return True, {
                'execution_time': end_time - start_time,
                'model_file': 'models/Inventory Prediction Model/inventory_model.pkl',
                'features': 'demand_forecasting_safety_stock',
                'optimization': 'seeded_data_v5.0'
            }
        else:
            return False, "Model training failed"
        
    except Exception as e:
        print(f"❌ Inventory Model failed: {e}")
        return False, str(e)

def run_visual_search_model():
    """Run the new Visual Search Model"""
    print("\n🖼️  Running New Advanced Visual Search Model...")
    print("=" * 60)
    
    try:
        # Import and run the visual search model
        sys.path.append(os.path.join('models', 'Visual Search Model'))
        from visual_search_model import SeededDataVisualSearchModel
        
        start_time = time.time()
        model = SeededDataVisualSearchModel()
        success = model.train_and_save_model()
        end_time = time.time()
        
        if success:
            print(f"✅ Visual Search Model completed in {end_time - start_time:.2f} seconds")
            print(f"   📁 Model saved to: models/Visual Search Model/visual_search_model.pkl")
            print(f"   🎯 Features: Deep CNN + color + texture + shape analysis")
            print(f"   🔧 Optimized for: Picsum placeholder images + visual clustering")
            
            return True, {
                'execution_time': end_time - start_time,
                'model_file': 'models/Visual Search Model/visual_search_model.pkl',
                'features': 'multi_feature_visual_similarity',
                'optimization': 'seeded_data_v5.0'
            }
        else:
            return False, "Model training failed"
        
    except Exception as e:
        print(f"❌ Visual Search Model failed: {e}")
        return False, str(e)

def main():
    """Main function to run all new ML models"""
    parser = argparse.ArgumentParser(description='Run new advanced ML models optimized for seeded data')
    parser.add_argument('--model', choices=['recommendation', 'price', 'inventory', 'visual'], 
                       help='Run only specific model')
    parser.add_argument('--skip-errors', action='store_true', 
                       help='Continue running other models if one fails')
    
    args = parser.parse_args()
    
    print("🚀 NEW ADVANCED ML MODELS RUNNER")
    print("=" * 70)
    print(f"🕐 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 Optimized for seeded database data (v5.0)")
    print("💾 Models save pkl files directly in their folders")
    print()
    
    # Define models to run
    models = {
        'recommendation': run_recommendation_model,
        'price': run_price_trends_model,
        'inventory': run_inventory_model,
        'visual': run_visual_search_model
    }
    
    # Filter models if specific model requested
    if args.model:
        models = {args.model: models[args.model]}
    
    # Run models
    results = {}
    total_start_time = time.time()
    
    for model_name, model_func in models.items():
        try:
            success, summary = model_func()
            results[model_name] = {'success': success, 'summary': summary}
            
            if not success and not args.skip_errors:
                print(f"\n❌ Stopping due to {model_name} model failure")
                break
                
        except KeyboardInterrupt:
            print(f"\n⚠️  Interrupted by user during {model_name} model")
            break
        except Exception as e:
            print(f"\n❌ Unexpected error in {model_name} model: {e}")
            results[model_name] = {'success': False, 'summary': str(e)}
            
            if not args.skip_errors:
                break
    
    total_end_time = time.time()
    
    # Summary
    print("\n" + "=" * 70)
    print("📋 EXECUTION SUMMARY")
    print("=" * 70)
    
    successful_models = [name for name, result in results.items() if result['success']]
    failed_models = [name for name, result in results.items() if not result['success']]
    
    print(f"🕐 Total execution time: {total_end_time - total_start_time:.2f} seconds")
    print(f"✅ Successful models: {len(successful_models)}/{len(results)}")
    print(f"❌ Failed models: {len(failed_models)}")
    print()
    
    if successful_models:
        print("✅ SUCCESSFUL MODELS:")
        for model_name in successful_models:
            summary = results[model_name]['summary']
            if isinstance(summary, dict):
                print(f"   🎯 {model_name.title()} Model")
                print(f"      ⏱️  Time: {summary['execution_time']:.1f}s")
                print(f"      📁 File: {summary['model_file']}")
                print(f"      🔧 Features: {summary['features']}")
            else:
                print(f"   🎯 {model_name.title()} Model - Ready for use")
    
    if failed_models:
        print("\n❌ FAILED MODELS:")
        for model_name in failed_models:
            error = results[model_name]['summary']
            print(f"   ⚠️  {model_name.title()} Model - {error}")
    
    print(f"\n💾 Model files saved in respective folders:")
    print(f"   📁 models/Recommendation Model/recommendation_model.pkl")
    print(f"   📁 models/Price Trends Model/price_trends_model.pkl")
    print(f"   📁 models/Inventory Prediction Model/inventory_model.pkl")
    print(f"   📁 models/Visual Search Model/visual_search_model.pkl")
    
    print(f"\n🎉 New Advanced ML Models execution completed!")
    print(f"🔧 All models are optimized for seeded data (v5.0)")
    
    # Return success status
    return len(failed_models) == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
