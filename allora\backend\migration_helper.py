"""
Migration Helper Script
=======================

Helper script to assist in migrating endpoints from the monolithic app.py
to organized blueprints with proper versioning and response formats.
"""

import re
import logging
from typing import List, Dict, Tuple

logger = logging.getLogger(__name__)

class EndpointMigrationHelper:
    """Helper class for migrating endpoints to blueprints."""
    
    def __init__(self):
        self.url_pattern_fixes = {
            # Fix inconsistent URL patterns
            '/api/community_posts': '/api/v1/community/posts',
            '/api/community-highlights': '/api/v1/community/highlights',
            '/api/product-comparison': '/api/v1/products/comparison',
            '/api/recently-viewed': '/api/v1/users/recently-viewed',
            '/api/availability-notifications': '/api/v1/products/availability-notifications',
            '/api/best-sellers': '/api/v1/products/best-sellers',
            '/api/new-arrivals': '/api/v1/products/new-arrivals',
            '/api/price-trends': '/api/v1/analytics/price-trends',
            '/api/inventory-prediction': '/api/v1/analytics/inventory-prediction',
            '/api/user-behavior': '/api/v1/analytics/user-behavior',
            '/api/search-analytics': '/api/v1/analytics/search-analytics',
            '/api/visual-search': '/api/v1/search/visual-search',
            '/api/order-history': '/api/v1/orders/history',
            '/api/payment-methods': '/api/v1/payments/methods',
            '/api/shipping-rates': '/api/v1/fulfillment/shipping-rates',
            '/api/tracking-info': '/api/v1/fulfillment/tracking-info',
            '/api/return-request': '/api/v1/rma/return-request',
            '/api/admin-dashboard': '/api/v1/admin/dashboard',
            '/api/seller-dashboard': '/api/v1/sellers/dashboard',
            '/api/support-tickets': '/api/v1/support/tickets',
            '/api/wishlist-items': '/api/v1/users/wishlist-items',
            '/api/cart-items': '/api/v1/orders/cart-items',
            '/api/checkout-session': '/api/v1/orders/checkout-session',
        }
        
        self.blueprint_mapping = {
            # Authentication endpoints
            'signup': 'auth',
            'login': 'auth', 
            'logout': 'auth',
            'refresh': 'auth',
            'oauth': 'auth',
            'password': 'auth',
            'verify': 'auth',
            
            # Product endpoints
            'products': 'products',
            'categories': 'products',
            'reviews': 'products',
            'variants': 'products',
            'comparison': 'products',
            'best-sellers': 'products',
            'new-arrivals': 'products',
            'availability': 'products',
            
            # Order endpoints
            'orders': 'orders',
            'cart': 'orders',
            'checkout': 'orders',
            'order-history': 'orders',
            
            # User endpoints
            'profile': 'users',
            'addresses': 'users',
            'wishlist': 'users',
            'recently-viewed': 'users',
            'preferences': 'users',
            
            # Payment endpoints
            'payments': 'payments',
            'payment-methods': 'payments',
            'invoices': 'payments',
            'refunds': 'payments',
            
            # Community endpoints
            'community': 'community',
            'posts': 'community',
            'comments': 'community',
            'highlights': 'community',
            'hashtags': 'community',
            
            # Analytics endpoints
            'analytics': 'analytics',
            'price-trends': 'analytics',
            'inventory-prediction': 'analytics',
            'user-behavior': 'analytics',
            'search-analytics': 'analytics',
            'sales': 'analytics',
            
            # Admin endpoints
            'admin': 'admin',
            'dashboard': 'admin',
            'users-management': 'admin',
            'content-management': 'admin',
            'settings': 'admin',
            
            # Seller endpoints
            'sellers': 'sellers',
            'seller-dashboard': 'sellers',
            'seller-products': 'sellers',
            'seller-orders': 'sellers',
            
            # Support endpoints
            'support': 'support',
            'tickets': 'support',
            'messages': 'support',
            'attachments': 'support'
        }
    
    def analyze_app_routes(self, app_file_path: str) -> Dict:
        """
        Analyze routes in the main app.py file.
        
        Args:
            app_file_path: Path to the app.py file
        
        Returns:
            Dictionary with route analysis
        """
        try:
            with open(app_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Find all route decorators
            route_pattern = r'@app\.route\([\'"]([^\'"]+)[\'"](?:,\s*methods=\[([^\]]+)\])?\)'
            routes = re.findall(route_pattern, content)
            
            analysis = {
                'total_routes': len(routes),
                'routes_by_blueprint': {},
                'url_pattern_issues': [],
                'versioning_issues': [],
                'routes_details': []
            }
            
            for route_url, methods in routes:
                # Determine blueprint
                blueprint = self._determine_blueprint(route_url)
                
                if blueprint not in analysis['routes_by_blueprint']:
                    analysis['routes_by_blueprint'][blueprint] = 0
                analysis['routes_by_blueprint'][blueprint] += 1
                
                # Check URL pattern issues
                if self._has_url_pattern_issues(route_url):
                    analysis['url_pattern_issues'].append(route_url)
                
                # Check versioning issues
                if not route_url.startswith('/api/v1/'):
                    analysis['versioning_issues'].append(route_url)
                
                # Store route details
                analysis['routes_details'].append({
                    'url': route_url,
                    'methods': methods.replace("'", "").replace('"', '').split(', ') if methods else ['GET'],
                    'blueprint': blueprint,
                    'needs_versioning': not route_url.startswith('/api/v1/'),
                    'needs_url_fix': self._has_url_pattern_issues(route_url)
                })
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing app routes: {e}")
            return {}
    
    def _determine_blueprint(self, route_url: str) -> str:
        """Determine which blueprint a route should belong to."""
        # Remove /api/ prefix and version if present
        clean_url = route_url.replace('/api/v1/', '').replace('/api/', '')
        
        # Check each part of the URL
        parts = clean_url.split('/')
        for part in parts:
            if part in self.blueprint_mapping:
                return self.blueprint_mapping[part]
        
        # Default mapping based on URL patterns
        if 'auth' in clean_url or 'login' in clean_url or 'signup' in clean_url:
            return 'auth'
        elif 'product' in clean_url:
            return 'products'
        elif 'order' in clean_url or 'cart' in clean_url:
            return 'orders'
        elif 'user' in clean_url or 'profile' in clean_url:
            return 'users'
        elif 'payment' in clean_url:
            return 'payments'
        elif 'community' in clean_url:
            return 'community'
        elif 'admin' in clean_url:
            return 'admin'
        elif 'seller' in clean_url:
            return 'sellers'
        elif 'analytics' in clean_url:
            return 'analytics'
        else:
            return 'misc'
    
    def _has_url_pattern_issues(self, route_url: str) -> bool:
        """Check if URL has pattern issues (underscores, inconsistent naming)."""
        return '_' in route_url or route_url in self.url_pattern_fixes
    
    def generate_migration_plan(self, analysis: Dict) -> str:
        """
        Generate a migration plan based on route analysis.
        
        Args:
            analysis: Route analysis from analyze_app_routes
        
        Returns:
            Migration plan as formatted string
        """
        plan = []
        plan.append("🚀 ENDPOINT MIGRATION PLAN")
        plan.append("=" * 50)
        plan.append(f"Total routes to migrate: {analysis['total_routes']}")
        plan.append(f"URL pattern issues: {len(analysis['url_pattern_issues'])}")
        plan.append(f"Versioning issues: {len(analysis['versioning_issues'])}")
        plan.append("")
        
        plan.append("📋 Routes by Blueprint:")
        for blueprint, count in sorted(analysis['routes_by_blueprint'].items()):
            plan.append(f"  • {blueprint}: {count} routes")
        plan.append("")
        
        plan.append("⚠️  URL Pattern Issues:")
        for url in analysis['url_pattern_issues']:
            fixed_url = self.url_pattern_fixes.get(url, url)
            plan.append(f"  • {url} → {fixed_url}")
        plan.append("")
        
        plan.append("🔧 Migration Steps:")
        plan.append("1. Create missing blueprint files")
        plan.append("2. Move routes to appropriate blueprints")
        plan.append("3. Update URL patterns to use hyphens")
        plan.append("4. Add /api/v1/ versioning prefix")
        plan.append("5. Update response formats")
        plan.append("6. Update authentication decorators")
        plan.append("7. Test all migrated endpoints")
        plan.append("")
        
        return "\n".join(plan)
    
    def fix_url_pattern(self, url: str) -> str:
        """Fix URL pattern to use consistent hyphen-separated format."""
        if url in self.url_pattern_fixes:
            return self.url_pattern_fixes[url]
        
        # Convert underscores to hyphens
        fixed_url = url.replace('_', '-')
        
        # Add versioning if not present
        if fixed_url.startswith('/api/') and not fixed_url.startswith('/api/v1/'):
            fixed_url = fixed_url.replace('/api/', '/api/v1/')
        
        return fixed_url

# Usage example
if __name__ == "__main__":
    helper = EndpointMigrationHelper()
    
    # Analyze current app.py
    analysis = helper.analyze_app_routes('app.py')
    
    # Generate migration plan
    plan = helper.generate_migration_plan(analysis)
    print(plan)
    
    # Save plan to file
    with open('migration_plan.txt', 'w') as f:
        f.write(plan)
