# Shiprocket Integration Setup Guide for Allora Backend

**Complete setup guide for integrating Shiprocket multi-carrier shipping with your Allora e-commerce backend.**

---

## 🎯 **Overview**

This guide will help you integrate Shiprocket shipping into your Allora backend, giving you access to 17+ courier partners through a single API integration.

### **What You'll Get:**
- ✅ **Multi-carrier shipping** (Blue Dart, FedEx, Delhivery, Ecom Express, etc.)
- ✅ **Automatic rate comparison** and best rate selection
- ✅ **Real-time tracking** across all carriers
- ✅ **COD & Prepaid support** for 19,000+ pin codes
- ✅ **International shipping** to 220+ countries
- ✅ **Unified dashboard** for all shipments

---

## 🚀 **Quick Start (5 Minutes)**

### **Step 1: Create Shiprocket Account**
1. Visit: https://www.shiprocket.in/
2. Click "Sign Up" (Free account)
3. Complete registration and verify email/phone
4. Add at least one pickup location

### **Step 2: Configure Environment**
```bash
# Run the interactive setup script
python shiprocket_env_setup.py

# Or manually add to .env file:
SHIPROCKET_EMAIL=<EMAIL>
SHIPROCKET_PASSWORD=your_password
SHIPROCKET_SANDBOX=true
```

### **Step 3: Test Integration**
```bash
# Quick validation
python validate_shiprocket.py

# Comprehensive test
python test_shiprocket_integration.py
```

### **Step 4: Start Shipping!**
Your existing fulfillment APIs now support Shiprocket:
```bash
POST /api/fulfillment/rates          # Get rates from 17+ carriers
POST /api/fulfillment/shipments      # Create shipments
GET /api/fulfillment/shipments/{id}/track  # Track across all carriers
```

---

## 📋 **Detailed Setup Instructions**

### **1. Account Setup**

#### **Create Shiprocket Account:**
1. **Visit**: https://www.shiprocket.in/
2. **Sign Up**: Click "Sign Up" and fill details
3. **Verify**: Complete email and phone verification
4. **KYC**: Upload business documents (for production)

#### **Add Pickup Location:**
1. Login to Shiprocket dashboard
2. Go to "Settings" → "Pickup Locations"
3. Click "Add Pickup Location"
4. Fill your business address details
5. Set as default pickup location

### **2. Environment Configuration**

#### **Option A: Interactive Setup (Recommended)**
```bash
cd allora/backend
python shiprocket_env_setup.py
```

#### **Option B: Manual Configuration**
Add these variables to your `.env` file:

```bash
# ===========================================
# SHIPROCKET SHIPPING CONFIGURATION
# ===========================================

# Account Credentials
SHIPROCKET_EMAIL=<EMAIL>
SHIPROCKET_PASSWORD=your_password

# Environment Settings
SHIPROCKET_SANDBOX=true
SHIPROCKET_BASE_URL=https://apiv2.shiprocket.in/v1
SHIPROCKET_STAGING_URL=https://staging-apiv2.shiprocket.in/v1

# Default Configuration
SHIPROCKET_DEFAULT_PICKUP_LOCATION=primary
SHIPROCKET_AUTO_AWB=true
SHIPROCKET_AUTO_PICKUP=true

# Feature Flags
SHIPROCKET_ENABLE_COD=true
SHIPROCKET_ENABLE_INTERNATIONAL=true
SHIPROCKET_ENABLE_INSURANCE=true

# Default Package Dimensions
SHIPROCKET_DEFAULT_LENGTH=10
SHIPROCKET_DEFAULT_WIDTH=10
SHIPROCKET_DEFAULT_HEIGHT=10
SHIPROCKET_DEFAULT_WEIGHT=0.5
```

### **3. Testing & Validation**

#### **Quick Validation:**
```bash
python validate_shiprocket.py
```

#### **Comprehensive Testing:**
```bash
python test_shiprocket_integration.py
```

#### **Expected Output:**
```
✅ Environment Variables Test: PASS
✅ API Connection Test: PASS
✅ Rate Calculation Test: PASS
✅ Carrier Integration Test: PASS
✅ Configuration Validation: PASS

🎉 Shiprocket integration is fully configured and working!
```

---

## 🔧 **API Usage Examples**

### **1. Calculate Shipping Rates**
```python
POST /api/fulfillment/rates
{
    "carrier": "shiprocket",
    "origin": {
        "pincode": "400001"
    },
    "destination": {
        "pincode": "110001"
    },
    "package": {
        "weight": 1.5,
        "length": 20,
        "width": 15,
        "height": 10,
        "declared_value": 2500
    },
    "cod": true
}
```

**Response:**
```json
{
    "success": true,
    "rates": [
        {
            "carrier": "shiprocket",
            "service_name": "Shiprocket - Delhivery",
            "cost": 85.50,
            "currency": "INR",
            "estimated_days": 3,
            "guaranteed": false,
            "cod_available": true
        },
        {
            "carrier": "shiprocket",
            "service_name": "Shiprocket - Blue Dart",
            "cost": 120.00,
            "currency": "INR",
            "estimated_days": 1,
            "guaranteed": true,
            "cod_available": true
        }
    ]
}
```

### **2. Create Shipment**
```python
POST /api/fulfillment/shipments
{
    "carrier": "shiprocket",
    "order_id": "ORD-12345",
    "customer_info": {
        "name": "John Doe",
        "email": "<EMAIL>",
        "phone": "9876543210"
    },
    "shipping_address": {
        "name": "John Doe",
        "address_line_1": "123 Customer Street",
        "city": "Delhi",
        "state": "Delhi",
        "postal_code": "110001",
        "phone": "9876543210"
    },
    "items": [
        {
            "name": "Product Name",
            "sku": "SKU123",
            "quantity": 2,
            "price": 1250,
            "hsn": 123456
        }
    ],
    "package": {
        "weight": 1.5,
        "length": 20,
        "width": 15,
        "height": 10
    },
    "payment_method": "COD",
    "auto_awb": true,
    "courier_company_id": 12
}
```

### **3. Track Shipment**
```python
GET /api/fulfillment/shipments/{awb_number}/track
```

**Response:**
```json
{
    "success": true,
    "tracking_data": {
        "awb_number": "AWB123456789",
        "current_status": "In Transit",
        "estimated_delivery": "2025-07-16",
        "events": [
            {
                "timestamp": "2025-07-14T10:30:00Z",
                "status": "Picked Up",
                "location": "Mumbai Hub",
                "description": "Package picked up from origin"
            },
            {
                "timestamp": "2025-07-14T18:45:00Z",
                "status": "In Transit",
                "location": "Delhi Hub",
                "description": "Package in transit to destination"
            }
        ]
    }
}
```

---

## 🌍 **Available Courier Partners**

### **Domestic Couriers:**
1. **Blue Dart** - Premium express delivery
2. **FedEx** - International & domestic express
3. **Delhivery** - Pan-India e-commerce logistics
4. **Ecom Express** - E-commerce specialist
5. **DTDC** - Cost-effective surface delivery
6. **Xpressbees** - Last-mile delivery
7. **Shadowfax** - Same-day delivery
8. **Ekart** - Flipkart logistics
9. **Amazon Shipping** - Amazon logistics
10. **India Post** - Government postal service

### **International Couriers:**
1. **DHL Express** - Worldwide express
2. **FedEx International** - Global coverage
3. **Aramex** - Middle East & Asia
4. **UPS** - Americas & Europe

---

## 💰 **Pricing & Benefits**

### **Pricing Model:**
- **No Setup Fee** - Free account creation
- **No Monthly Fee** - Pay only per shipment
- **Transparent Pricing** - Rate card in dashboard
- **Volume Discounts** - Better rates for higher volumes

### **Typical Rates (Domestic):**
```
Surface (3-5 days):
- Within City: ₹30-50 for 500g
- Metro to Metro: ₹40-70 for 500g
- Metro to Non-Metro: ₹50-90 for 500g

Express (1-2 days):
- Within City: ₹50-80 for 500g
- Metro to Metro: ₹70-120 for 500g
- Metro to Non-Metro: ₹90-150 for 500g

Additional Charges:
- COD: ₹15-25 per shipment
- Fuel Surcharge: 10-15% of base rate
- GST: 18% on total charges
```

---

## 🔧 **Advanced Configuration**

### **Webhook Setup (Optional):**
1. Login to Shiprocket dashboard
2. Go to "Settings" → "Webhooks"
3. Add webhook URL: `https://yourdomain.com/api/webhooks/shiprocket`
4. Select events: Order status updates, delivery confirmations

### **Production Deployment:**
```bash
# Update environment for production
SHIPROCKET_SANDBOX=false
SHIPROCKET_EMAIL=your_production_email
SHIPROCKET_PASSWORD=your_production_password
```

### **Custom Pickup Locations:**
```bash
# Add multiple pickup locations
SHIPROCKET_WAREHOUSE_1=warehouse_mumbai
SHIPROCKET_WAREHOUSE_2=warehouse_delhi
SHIPROCKET_WAREHOUSE_3=warehouse_bangalore
```

---

## 🚨 **Troubleshooting**

### **Common Issues:**

#### **Authentication Failed:**
- ✅ Check email and password in .env file
- ✅ Verify account is active in Shiprocket dashboard
- ✅ Ensure no special characters in password

#### **No Pickup Locations:**
- ✅ Add pickup location in Shiprocket dashboard
- ✅ Verify pickup location is approved
- ✅ Check default pickup location name

#### **Rate Calculation Fails:**
- ✅ Verify pin codes are serviceable
- ✅ Check package weight and dimensions
- ✅ Ensure declared value is reasonable

#### **Shipment Creation Fails:**
- ✅ Verify all required fields are present
- ✅ Check customer phone number format
- ✅ Ensure pickup location exists

---

## 📞 **Support & Resources**

### **Shiprocket Support:**
- **Phone**: +91-11-4084-5555
- **Email**: <EMAIL>
- **API Support**: <EMAIL>
- **Dashboard**: https://app.shiprocket.in/

### **Documentation:**
- **API Docs**: https://apidocs.shiprocket.in/
- **Rate Calculator**: Available in dashboard
- **Pin Code Checker**: Serviceability verification

---

## ✅ **Success Checklist**

- [ ] ✅ Shiprocket account created and verified
- [ ] ✅ Pickup location added and approved
- [ ] ✅ Environment variables configured
- [ ] ✅ API authentication successful
- [ ] ✅ Rate calculation working
- [ ] ✅ Test shipment created
- [ ] ✅ Tracking functionality verified
- [ ] ✅ Webhooks configured (optional)
- [ ] ✅ Production credentials ready

---

**🎉 Congratulations! Your Allora backend now supports multi-carrier shipping through Shiprocket!**

*Your customers can now enjoy the best shipping rates and fastest delivery options across India and internationally.*
