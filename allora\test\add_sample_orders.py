#!/usr/bin/env python3
"""
Add Sample Orders for Sustainability Testing
===========================================

Creates sample orders with sustainable products to test the sustainability
impact dashboard and metrics calculations.

Author: Allora Development Team
Date: 2025-07-11
"""

import sys
import os
from datetime import datetime, timedelta
from decimal import Decimal
import random

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import Flask app and models
from app import app, db, User, Product, Order, OrderItem

def create_sample_orders():
    """Create sample orders with sustainable products"""
    
    print("🛒 Creating sample orders for sustainability testing...")
    
    with app.app_context():
        try:
            # Get existing users (create one if none exist)
            users = User.query.all()
            if not users:
                # Create a sample user
                sample_user = User(
                    username='eco_shopper',
                    email='<EMAIL>',
                    password='hashed_password',  # In production, use proper hashing
                    first_name='Eco',
                    last_name='Shopper'
                )
                db.session.add(sample_user)
                db.session.commit()
                users = [sample_user]
                print("✅ Created sample user")
            
            # Get sustainable products (sustainability_score >= 70)
            sustainable_products = Product.query.filter(
                Product.sustainability_score >= 70
            ).all()
            
            if not sustainable_products:
                print("❌ No sustainable products found in database")
                return False
            
            print(f"📦 Found {len(sustainable_products)} sustainable products")
            
            # Create sample orders over the last 30 days
            orders_created = 0
            for i in range(15):  # Create 15 sample orders
                # Random date within last 30 days
                days_ago = random.randint(1, 30)
                order_date = datetime.utcnow() - timedelta(days=days_ago)
                
                # Random user
                user = random.choice(users)
                
                # Create order
                order = Order(
                    user_id=user.id,
                    order_number=f'ORD-{datetime.utcnow().strftime("%Y%m%d")}-{i+1:04d}',
                    status='delivered',  # Set as delivered to count in sustainability metrics
                    total_amount=Decimal('0.00'),
                    subtotal=Decimal('0.00'),
                    payment_method='card',
                    payment_status='completed',
                    shipping_address='123 Eco Street, Green City, India',
                    created_at=order_date,
                    updated_at=order_date
                )
                db.session.add(order)
                db.session.flush()  # Get order ID
                
                # Add 1-3 random sustainable products to order
                num_items = random.randint(1, 3)
                total_amount = Decimal('0.00')
                
                selected_products = random.sample(sustainable_products, min(num_items, len(sustainable_products)))
                
                for product in selected_products:
                    quantity = random.randint(1, 2)
                    price = product.price or Decimal('999.00')
                    
                    order_item = OrderItem(
                        order_id=order.id,
                        product_id=product.id,
                        quantity=quantity,
                        unit_price=price,
                        total_price=price * quantity,
                        product_name=product.name,
                        product_image=product.image
                    )
                    db.session.add(order_item)
                    total_amount += Decimal(str(price)) * quantity
                
                order.total_amount = total_amount
                order.subtotal = total_amount
                orders_created += 1
            
            db.session.commit()
            print(f"✅ Created {orders_created} sample orders with sustainable products")
            
            # Display summary
            total_orders = Order.query.count()
            total_order_items = OrderItem.query.count()
            
            print(f"📊 Database Summary:")
            print(f"   Total Users: {User.query.count()}")
            print(f"   Total Products: {Product.query.count()}")
            print(f"   Sustainable Products: {len(sustainable_products)}")
            print(f"   Total Orders: {total_orders}")
            print(f"   Total Order Items: {total_order_items}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error creating sample orders: {e}")
            db.session.rollback()
            return False

def test_sustainability_metrics():
    """Test sustainability metrics calculation"""
    
    print("\n🌱 Testing sustainability metrics...")
    
    with app.app_context():
        try:
            # Import sustainability functions
            from sustainability_api import calculate_co2_savings, calculate_trees_planted_equivalent
            
            # Get recent orders
            recent_orders = Order.query.filter(
                Order.created_at >= datetime.utcnow() - timedelta(days=30)
            ).all()
            
            # Prepare orders data
            orders_data = []
            for order in recent_orders:
                order_items = []
                for item in order.items:
                    product = item.product
                    order_items.append({
                        'product_id': product.id,
                        'quantity': item.quantity,
                        'sustainability_score': product.sustainability_score or 0,
                        'carbon_footprint': getattr(product, 'carbon_footprint', 0),
                        'recyclable': getattr(product, 'recyclable', False),
                        'organic': getattr(product, 'organic', False),
                        'carbon_neutral': getattr(product, 'carbon_neutral', False)
                    })
                orders_data.append({'items': order_items})
            
            # Calculate metrics
            co2_saved = calculate_co2_savings(orders_data)
            trees_equivalent = calculate_trees_planted_equivalent(co2_saved)
            
            print(f"📊 Sustainability Metrics:")
            print(f"   Orders analyzed: {len(orders_data)}")
            print(f"   CO2 saved: {co2_saved} kg")
            print(f"   Trees equivalent: {trees_equivalent}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error testing sustainability metrics: {e}")
            return False

if __name__ == '__main__':
    print("🌱 Allora Sustainability Testing Setup")
    print("=" * 50)
    
    # Create sample orders
    if create_sample_orders():
        print("\n✅ Sample orders created successfully!")
        
        # Test metrics
        if test_sustainability_metrics():
            print("\n✅ Sustainability metrics tested successfully!")
            print("\n🎉 Sustainability system is ready for testing!")
            print("\nYou can now:")
            print("1. Visit http://localhost:3000 to see the sustainability dashboard")
            print("2. Test the API endpoints:")
            print("   - GET /api/sustainability/metrics")
            print("   - GET /api/sustainability/green-heroes")
            print("   - GET /api/sustainability/goals")
        else:
            print("\n⚠️ Metrics testing failed, but orders were created")
    else:
        print("\n❌ Failed to create sample orders")
        sys.exit(1)
