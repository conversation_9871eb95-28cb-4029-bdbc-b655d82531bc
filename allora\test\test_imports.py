#!/usr/bin/env python3
"""
Import Test for WebSocket and Webhook Files
==========================================

Tests that all working files can be imported without errors.
"""

def test_imports():
    """Test importing all working files"""
    results = {}
    
    # Test webhook_handlers
    try:
        import webhook_handlers
        results['webhook_handlers'] = "✅ SUCCESS"
    except Exception as e:
        results['webhook_handlers'] = f"❌ ERROR: {e}"
    
    # Test flask_socketio_manager
    try:
        import flask_socketio_manager
        results['flask_socketio_manager'] = "✅ SUCCESS"
    except Exception as e:
        results['flask_socketio_manager'] = f"❌ ERROR: {e}"
    
    # Test tracking_system
    try:
        import tracking_system
        results['tracking_system'] = "✅ SUCCESS"
    except Exception as e:
        results['tracking_system'] = f"❌ ERROR: {e}"
    
    # Test deprecated files (should not be imported)
    print("\n🔍 Testing Deprecated Files (should not be imported):")
    
    try:
        import websocket_routes
        results['websocket_routes'] = "⚠️  IMPORTED (should be deprecated)"
    except Exception as e:
        results['websocket_routes'] = f"✅ BLOCKED: {e}"
    
    try:
        import websocket_manager
        results['websocket_manager'] = "⚠️  IMPORTED (should be deprecated)"
    except Exception as e:
        results['websocket_manager'] = f"✅ BLOCKED: {e}"
    
    return results

def main():
    """Main test function"""
    print("🧪 WebSocket and Webhook Import Test")
    print("=" * 50)
    
    results = test_imports()
    
    print("\n📊 Import Test Results:")
    print("=" * 30)
    
    for module, result in results.items():
        print(f"{module}: {result}")
    
    # Count successes
    successes = sum(1 for result in results.values() if "✅ SUCCESS" in result)
    total_working = 3  # webhook_handlers, flask_socketio_manager, tracking_system
    
    print(f"\n📈 Working Files: {successes}/{total_working}")
    
    if successes == total_working:
        print("🎉 All working files import successfully!")
    else:
        print("⚠️  Some working files have import issues")

if __name__ == '__main__':
    main()
