#!/usr/bin/env python3
"""
Reindex products into Elasticsearch
"""

import sys
sys.path.append('.')

from app import app
from search_system.elasticsearch_manager import get_elasticsearch_manager
from search_system.elasticsearch_config import get_elasticsearch_client

def main():
    """Reindex all products into Elasticsearch"""
    with app.app_context():
        # Check Elasticsearch connection
        es = get_elasticsearch_client()
        if not es or not es.ping():
            print("❌ Elasticsearch not connected")
            return False
        
        print("✅ Elasticsearch connected")
        
        # Get manager and reindex
        manager = get_elasticsearch_manager()
        print("🔄 Reindexing products...")
        
        result = manager.reindex_products()
        
        if result:
            print("✅ Products reindexed successfully")
            
            # Check document count
            stats = es.indices.stats(index=manager.product_index)
            doc_count = stats['indices'][manager.product_index]['total']['docs']['count']
            print(f"📊 Documents in index: {doc_count}")
        else:
            print("❌ Failed to reindex products")
        
        return result

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
