#!/usr/bin/env python3
"""
Flask-SocketIO Server Runner
============================

Dedicated server runner for Flask-SocketIO with WebSocket support.
This ensures proper WebSocket functionality for real-time features.

Usage:
    python run_socketio_server.py

Features:
- Native WebSocket support
- Real-time communication
- Auto-reload in development
- Production-ready configuration

Author: Allora Development Team
Date: 2025-07-13
"""

import os
import sys
from datetime import datetime

# Configuration
HOST = os.getenv('HOST', '127.0.0.1')
PORT = int(os.getenv('PORT', 5000))
DEBUG = os.getenv('FLASK_ENV', 'production') == 'development'

def main():
    """Main server startup function"""
    print("🔌 Flask-SocketIO Server Startup")
    print("=" * 50)
    print(f"📅 Startup Time: {datetime.now().isoformat()}")
    print(f"🌐 Host: {HOST}")
    print(f"🔌 Port: {PORT}")
    print(f"🔧 Debug Mode: {DEBUG}")
    print(f"🐍 Python: {sys.version}")
    print("=" * 50)
    
    try:
        # Import the Flask app with SocketIO
        print("📦 Loading Flask application...")
        from app import app, socketio
        
        if not socketio:
            print("❌ SocketIO not initialized in app!")
            print("💡 Make sure Flask-SocketIO is properly installed:")
            print("   pip install flask-socketio")
            sys.exit(1)
        
        print("✅ Flask application loaded successfully")
        print("✅ SocketIO instance found and ready")
        
        # Initialize the application context
        with app.app_context():
            print("🔧 Initializing application context...")
            
            # Import and run initialization
            try:
                from app import initialize_payment_gateways, initialize_admin_user, initialize_oauth_providers
                
                print("💳 Initializing payment gateways...")
                initialize_payment_gateways()
                print("✅ Payment gateways initialized")
                
                print("👤 Initializing admin user...")
                initialize_admin_user()
                print("✅ Admin user initialized")
                
                print("🔐 Initializing OAuth providers...")
                initialize_oauth_providers()
                print("✅ OAuth providers initialized")
                
            except Exception as e:
                print(f"⚠️  Initialization warning: {e}")
                print("🔄 Continuing with server startup...")
        
        # Print route information
        route_count = len(list(app.url_map.iter_rules()))
        print(f"📊 Routes registered: {route_count}")
        
        # Print important URLs
        print("\n🌐 Server URLs:")
        print(f"   Main API: http://{HOST}:{PORT}/")
        print(f"   Health Check: http://{HOST}:{PORT}/api/health")
        print(f"   SocketIO Test: http://{HOST}:{PORT}/socketio-test")
        print(f"   WebSocket: ws://{HOST}:{PORT}/socket.io/")
        
        print("\n🔌 WebSocket Events Available:")
        print("   - connect/disconnect")
        print("   - inventory_update")
        print("   - price_update") 
        print("   - cart_update")
        print("   - order_update")
        print("   - notification")
        print("   - admin_notification")
        
        print("\n🚀 Starting Flask-SocketIO server...")
        print("=" * 50)
        
        # Start the SocketIO server
        socketio.run(
            app,
            host=HOST,
            port=PORT,
            debug=DEBUG,
            allow_unsafe_werkzeug=True,
            use_reloader=False,  # Disable reloader to prevent issues
            log_output=True
        )
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("\n💡 Troubleshooting:")
        print("1. Make sure you're in the correct directory")
        print("2. Install required dependencies:")
        print("   pip install flask flask-socketio redis")
        print("3. Check that app.py exists and is properly configured")
        sys.exit(1)
        
    except Exception as e:
        print(f"❌ Server startup failed: {e}")
        print("\n💡 Troubleshooting:")
        print("1. Check if port is already in use")
        print("2. Verify database connection")
        print("3. Check Redis server is running")
        print("4. Review application logs for details")
        sys.exit(1)

def check_dependencies():
    """Check if required dependencies are installed"""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        ('flask', 'Flask'),
        ('flask_socketio', 'Flask-SocketIO'),
        ('redis', 'Redis'),
        ('sqlalchemy', 'SQLAlchemy')
    ]
    
    missing_packages = []
    
    for package, name in required_packages:
        try:
            __import__(package)
            print(f"✅ {name} - Available")
        except ImportError:
            print(f"❌ {name} - Missing")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("💡 Install with:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ All dependencies available")
    return True

def test_socketio_import():
    """Test if SocketIO can be imported from app"""
    print("🧪 Testing SocketIO import...")
    
    try:
        from app import socketio
        if socketio:
            print("✅ SocketIO imported successfully")
            print(f"✅ SocketIO type: {type(socketio)}")
            return True
        else:
            print("❌ SocketIO is None")
            return False
    except ImportError as e:
        print(f"❌ SocketIO import failed: {e}")
        return False

if __name__ == '__main__':
    print("🔌 Flask-SocketIO Server")
    print("=" * 30)
    
    # Check dependencies first
    if not check_dependencies():
        sys.exit(1)
    
    # Test SocketIO import
    if not test_socketio_import():
        print("\n💡 SocketIO Issues:")
        print("1. Make sure Flask-SocketIO is installed:")
        print("   pip install flask-socketio")
        print("2. Check app.py has proper SocketIO initialization")
        print("3. Verify no import errors in app.py")
        sys.exit(1)
    
    # Start the server
    main()
