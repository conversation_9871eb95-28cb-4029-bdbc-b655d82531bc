#!/usr/bin/env python3
"""
Create Community Test Data
==========================

Creates sample community data for testing:
- Community posts with sustainability themes
- Product reviews
- Hashtags and engagement
- Featured eco-friendly sellers

Author: Allora Development Team
Date: 2025-07-11
"""

import sys
import os
from datetime import datetime, timedelta
import random

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import Flask app and models
from app import (
    app, db, User, Product, Seller, CommunityPost, PostComment, PostLike, 
    ProductReview, Hashtag, PostHashtag, CommunityStats
)

def create_community_posts():
    """Create sample community posts with sustainability themes"""
    print("📝 Creating community posts...")
    
    with app.app_context():
        try:
            # Get users
            users = User.query.limit(5).all()
            if not users:
                print("❌ No users found")
                return False
            
            # Sample sustainability-focused posts
            sample_posts = [
                {
                    'content': 'Just switched to these amazing bamboo water bottles! 🌱 The quality is incredible and I love knowing I\'m reducing plastic waste. #sustainability #zerowaste #ecofriendly',
                    'post_type': 'photo',
                    'image_url': 'https://images.unsplash.com/photo-1602143407151-7111542de6e8?w=400&h=400&fit=crop',
                    'hashtags': ['sustainability', 'zerowaste', 'ecofriendly', 'bamboo']
                },
                {
                    'content': 'My journey to zero waste started 6 months ago. Here\'s what I\'ve learned and the products that made the biggest difference! 🌍 #zerowaste #sustainability #greenjourney',
                    'post_type': 'text',
                    'hashtags': ['zerowaste', 'sustainability', 'greenjourney', 'eco']
                },
                {
                    'content': 'Review: This organic skincare set is absolutely amazing! Not only does it make my skin glow, but the packaging is 100% recyclable. Worth every penny! ⭐⭐⭐⭐⭐ #organicskincare #sustainable #beauty',
                    'post_type': 'review',
                    'image_url': 'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400&h=400&fit=crop',
                    'hashtags': ['organicskincare', 'sustainable', 'beauty', 'ecofriendly']
                },
                {
                    'content': 'Before and after: My home office transformation using only sustainable furniture! The wooden desk is made from reclaimed wood and it\'s absolutely beautiful 🪵✨ #sustainablehome #ecofurniture #green',
                    'post_type': 'photo',
                    'image_url': 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop',
                    'hashtags': ['sustainablehome', 'ecofurniture', 'green', 'sustainable']
                },
                {
                    'content': 'Coffee lovers! ☕ This fair trade coffee is not only delicious but supports sustainable farming practices. Every cup makes a difference! #fairtrade #sustainablecoffee #ethical',
                    'post_type': 'photo',
                    'image_url': 'https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=400&h=400&fit=crop',
                    'hashtags': ['fairtrade', 'sustainablecoffee', 'ethical', 'coffee']
                },
                {
                    'content': 'Plastic-free cleaning routine! 🧽 These eco-friendly cleaning products work better than conventional ones and come in biodegradable packaging. #plasticfree #ecoclean #sustainable',
                    'post_type': 'photo',
                    'image_url': 'https://images.unsplash.com/photo-*************-326f5e854473?w=400&h=400&fit=crop',
                    'hashtags': ['plasticfree', 'ecoclean', 'sustainable', 'green']
                },
                {
                    'content': 'Solar power bank review! 🔋☀️ Perfect for camping and outdoor adventures. Charges my phone multiple times and runs on clean energy! #solarpower #renewable #tech #sustainable',
                    'post_type': 'review',
                    'image_url': 'https://images.unsplash.com/photo-*************-d5365f9ff1c5?w=400&h=400&fit=crop',
                    'hashtags': ['solarpower', 'renewable', 'tech', 'sustainable']
                },
                {
                    'content': 'Hemp yoga mat appreciation post! 🧘‍♀️ It\'s incredibly durable, provides great grip, and is completely biodegradable. Perfect for my daily practice! #yoga #hemp #sustainable #wellness',
                    'post_type': 'photo',
                    'image_url': 'https://images.unsplash.com/photo-**********-0f2fcb009e0b?w=400&h=400&fit=crop',
                    'hashtags': ['yoga', 'hemp', 'sustainable', 'wellness']
                }
            ]
            
            posts_created = 0
            for i, post_data in enumerate(sample_posts):
                user = users[i % len(users)]
                
                # Create post
                post = CommunityPost(
                    user_id=user.id,
                    content=post_data['content'],
                    post_type=post_data['post_type'],
                    image_url=post_data.get('image_url'),
                    created_at=datetime.utcnow() - timedelta(days=random.randint(1, 14))
                )
                db.session.add(post)
                db.session.flush()  # Get the post ID
                
                # Create hashtags and link them
                for hashtag_name in post_data['hashtags']:
                    # Get or create hashtag
                    hashtag = Hashtag.query.filter_by(tag=hashtag_name).first()
                    if not hashtag:
                        hashtag = Hashtag(tag=hashtag_name, usage_count=1)
                        db.session.add(hashtag)
                        db.session.flush()
                    else:
                        hashtag.usage_count += 1
                    
                    # Link hashtag to post
                    post_hashtag = PostHashtag(post_id=post.id, hashtag_id=hashtag.id)
                    db.session.add(post_hashtag)
                
                # Add some likes and comments
                num_likes = random.randint(2, 15)
                for _ in range(num_likes):
                    liker = random.choice(users)
                    if not PostLike.query.filter_by(post_id=post.id, user_id=liker.id).first():
                        like = PostLike(post_id=post.id, user_id=liker.id)
                        db.session.add(like)
                
                # Add some comments
                num_comments = random.randint(1, 5)
                sample_comments = [
                    "This is amazing! Thanks for sharing! 🌱",
                    "I need to try this! Where did you get it?",
                    "Love seeing more sustainable choices! 💚",
                    "This looks incredible! Great review!",
                    "Thanks for the inspiration! 🌍"
                ]
                
                for _ in range(num_comments):
                    commenter = random.choice(users)
                    comment = PostComment(
                        post_id=post.id,
                        user_id=commenter.id,
                        content=random.choice(sample_comments),
                        created_at=datetime.utcnow() - timedelta(days=random.randint(0, 7))
                    )
                    db.session.add(comment)
                
                posts_created += 1
            
            db.session.commit()
            print(f"✅ Created {posts_created} community posts with engagement")
            return True
            
        except Exception as e:
            print(f"❌ Error creating community posts: {e}")
            db.session.rollback()
            return False

def create_product_reviews():
    """Create sample product reviews"""
    print("⭐ Creating product reviews...")
    
    with app.app_context():
        try:
            users = User.query.limit(5).all()
            products = Product.query.all()
            
            if not users or not products:
                print("❌ No users or products found")
                return False
            
            sample_reviews = [
                {
                    'rating': 5,
                    'title': 'Absolutely love this sustainable product!',
                    'comment': 'This product exceeded my expectations! The quality is outstanding and I love that it\'s made from sustainable materials. The packaging was minimal and recyclable. Highly recommend to anyone looking to make more eco-friendly choices!'
                },
                {
                    'rating': 5,
                    'title': 'Great quality and eco-friendly!',
                    'comment': 'I\'ve been using this for a few weeks now and I\'m impressed with both the quality and the environmental impact. It works exactly as described and feels good knowing I\'m supporting sustainable practices.'
                },
                {
                    'rating': 4,
                    'title': 'Good product, fast shipping',
                    'comment': 'Really happy with this purchase. The product is well-made and the sustainability aspect is a huge plus. Shipping was fast and packaging was minimal. Would definitely buy again!'
                },
                {
                    'rating': 5,
                    'title': 'Perfect for my sustainable lifestyle',
                    'comment': 'This fits perfectly into my zero-waste journey. The quality is excellent and it\'s clear that a lot of thought went into making this as environmentally friendly as possible. Love it!'
                },
                {
                    'rating': 4,
                    'title': 'Impressed with the sustainability focus',
                    'comment': 'Not only is this product functional and well-designed, but the company\'s commitment to sustainability really shows. From the materials to the packaging, everything is thoughtfully done.'
                }
            ]
            
            reviews_created = 0
            for i, review_data in enumerate(sample_reviews):
                user = users[i % len(users)]
                product = products[i % len(products)]
                
                # Check if review already exists
                existing_review = ProductReview.query.filter_by(
                    user_id=user.id, 
                    product_id=product.id
                ).first()
                
                if not existing_review:
                    review = ProductReview(
                        user_id=user.id,
                        product_id=product.id,
                        rating=review_data['rating'],
                        title=review_data['title'],
                        comment=review_data['comment'],
                        verified_purchase=True,
                        helpful_count=random.randint(1, 8),
                        created_at=datetime.utcnow() - timedelta(days=random.randint(1, 30))
                    )
                    db.session.add(review)
                    reviews_created += 1
            
            db.session.commit()
            print(f"✅ Created {reviews_created} product reviews")
            return True
            
        except Exception as e:
            print(f"❌ Error creating product reviews: {e}")
            db.session.rollback()
            return False

def update_community_stats():
    """Update community statistics"""
    print("📊 Updating community stats...")
    
    with app.app_context():
        try:
            # Get or create stats record
            stats = CommunityStats.query.first()
            if not stats:
                stats = CommunityStats()
                db.session.add(stats)
            
            # Update stats
            stats.total_members = User.query.count()
            stats.total_posts = CommunityPost.query.count()
            stats.posts_today = CommunityPost.query.filter(
                CommunityPost.created_at >= datetime.utcnow().date()
            ).count()
            stats.active_users_now = random.randint(15, 45)  # Simulated active users
            stats.updated_at = datetime.utcnow()
            
            db.session.commit()
            print(f"✅ Updated community stats: {stats.total_members} members, {stats.total_posts} posts")
            return True
            
        except Exception as e:
            print(f"❌ Error updating community stats: {e}")
            db.session.rollback()
            return False

def main():
    print("👥 COMMUNITY HIGHLIGHTS TEST DATA CREATOR")
    print("=" * 60)
    
    # Create community posts
    if not create_community_posts():
        print("❌ Failed to create community posts")
        return False
    
    # Create product reviews
    if not create_product_reviews():
        print("❌ Failed to create product reviews")
        return False
    
    # Update community stats
    if not update_community_stats():
        print("❌ Failed to update community stats")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 COMMUNITY TEST DATA CREATION COMPLETE!")
    print("=" * 60)
    
    print("\n✅ What was created:")
    print("   • Community posts with sustainability themes")
    print("   • Hashtags and post engagement (likes, comments)")
    print("   • Product reviews with detailed feedback")
    print("   • Community statistics")
    
    print("\n🚀 Now you can test:")
    print("   • Recent Community Posts: Real user-generated content")
    print("   • Sustainability Stories: Posts with eco hashtags")
    print("   • Featured Eco Brands: High sustainability score sellers")
    print("   • Recent Reviews: Quality product reviews")
    
    print("\n🌐 Visit http://localhost:3000 to see the Community Highlights in action!")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
