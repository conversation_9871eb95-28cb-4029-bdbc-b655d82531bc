#!/usr/bin/env python3
"""
Migration script to add shopping cart and checkout features to the database.
This script adds missing columns and tables for the comprehensive shopping cart system.
"""

import sys
import os
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from sqlalchemy import text

def column_exists(connection, table_name, column_name):
    """Check if a column exists in a table"""
    try:
        cursor = connection.cursor()
        cursor.execute(f"""
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'allora_db' 
            AND TABLE_NAME = '{table_name}' 
            AND COLUMN_NAME = '{column_name}'
        """)
        result = cursor.fetchone()
        cursor.close()
        return result[0] > 0
    except Error as e:
        print(f"Error checking column existence: {e}")
        return False

def table_exists(connection, table_name):
    """Check if a table exists"""
    try:
        cursor = connection.cursor()
        cursor.execute(f"""
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = 'allora_db' 
            AND TABLE_NAME = '{table_name}'
        """)
        result = cursor.fetchone()
        cursor.close()
        return result[0] > 0
    except Error as e:
        print(f"Error checking table existence: {e}")
        return False

def migrate_cart_item_table(connection):
    """Add missing columns to cart_item table"""
    print("Migrating cart_item table...")
    
    cursor = connection.cursor()
    
    try:
        # Add guest_session_id column if it doesn't exist
        if not column_exists(connection, 'cart_item', 'guest_session_id'):
            print("Adding guest_session_id column to cart_item table...")
            cursor.execute("""
                ALTER TABLE cart_item 
                ADD COLUMN guest_session_id VARCHAR(100) NULL
            """)
            print("✓ Added guest_session_id column")
        else:
            print("✓ guest_session_id column already exists")
        
        # Add created_at column if it doesn't exist
        if not column_exists(connection, 'cart_item', 'created_at'):
            print("Adding created_at column to cart_item table...")
            cursor.execute("""
                ALTER TABLE cart_item 
                ADD COLUMN created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
            """)
            print("✓ Added created_at column")
        else:
            print("✓ created_at column already exists")
        
        # Modify user_id to allow NULL for guest carts
        print("Modifying user_id column to allow NULL...")
        cursor.execute("""
            ALTER TABLE cart_item 
            MODIFY COLUMN user_id INT NULL
        """)
        print("✓ Modified user_id column to allow NULL")
        
        connection.commit()
        print("✓ cart_item table migration completed")
        
    except Error as e:
        print(f"Error migrating cart_item table: {e}")
        connection.rollback()
        raise
    finally:
        cursor.close()

def create_new_tables(connection):
    """Create new tables for shopping cart features"""
    print("Creating new tables...")
    
    cursor = connection.cursor()
    
    try:
        # Create guest_session table
        if not table_exists(connection, 'guest_session'):
            print("Creating guest_session table...")
            cursor.execute("""
                CREATE TABLE guest_session (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    session_id VARCHAR(100) NOT NULL UNIQUE,
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    last_activity DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT TRUE
                )
            """)
            print("✓ Created guest_session table")
        else:
            print("✓ guest_session table already exists")
        
        # Create abandoned_cart table
        if not table_exists(connection, 'abandoned_cart'):
            print("Creating abandoned_cart table...")
            cursor.execute("""
                CREATE TABLE abandoned_cart (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NULL,
                    guest_session_id VARCHAR(100) NULL,
                    recovery_token VARCHAR(100) NOT NULL UNIQUE,
                    cart_data JSON NOT NULL,
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    recovered_at DATETIME NULL,
                    is_recovered BOOLEAN DEFAULT FALSE,
                    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE
                )
            """)
            print("✓ Created abandoned_cart table")
        else:
            print("✓ abandoned_cart table already exists")
        
        # Create email_notification table
        if not table_exists(connection, 'email_notification'):
            print("Creating email_notification table...")
            cursor.execute("""
                CREATE TABLE email_notification (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NULL,
                    order_id INT NULL,
                    email_type VARCHAR(50) NOT NULL,
                    recipient_email VARCHAR(120) NOT NULL,
                    subject VARCHAR(200) NOT NULL,
                    body TEXT NOT NULL,
                    sent_at DATETIME NULL,
                    is_sent BOOLEAN DEFAULT FALSE,
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE
                )
            """)
            print("✓ Created email_notification table")
        else:
            print("✓ email_notification table already exists")
        
        connection.commit()
        print("✓ New tables creation completed")
        
    except Error as e:
        print(f"Error creating new tables: {e}")
        connection.rollback()
        raise
    finally:
        cursor.close()

def main():
    """Main migration function"""
    print("Starting shopping cart features migration...")
    print("=" * 50)
    
    # Get database connection
    connection = get_db_connection()
    if not connection:
        print("Failed to connect to database. Exiting.")
        sys.exit(1)
    
    try:
        # Migrate existing tables
        migrate_cart_item_table(connection)
        
        # Create new tables
        create_new_tables(connection)
        
        print("=" * 50)
        print("✅ Migration completed successfully!")
        print("The database now supports:")
        print("- Guest cart functionality")
        print("- Cart abandonment recovery")
        print("- Email notifications")
        print("- Enhanced cart management")
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        sys.exit(1)
    finally:
        connection.close()

if __name__ == "__main__":
    main()
