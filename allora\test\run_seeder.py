#!/usr/bin/env python3
"""
Quick runner script for the database seeder
===========================================

This script provides a simple way to run the database seeder with common options.
"""

import os
import sys
import subprocess

def install_requirements():
    """Install required packages if not available"""
    try:
        import faker
        print("✅ Faker is already installed")
    except ImportError:
        print("📦 Installing faker...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "faker"])
        print("✅ Faker installed successfully")

def run_seeder(clear_data=True, num_products=100):
    """Run the database seeder"""
    print("🌱 Starting Allora Database Seeder")
    print("=" * 50)
    
    # Install requirements
    install_requirements()
    
    # Build command
    cmd = [sys.executable, "seed_database.py"]
    
    if clear_data:
        cmd.append("--clear")
    
    cmd.extend(["--products", str(num_products)])
    
    print(f"🚀 Running command: {' '.join(cmd)}")
    print()
    
    # Run the seeder
    try:
        result = subprocess.run(cmd, check=True, cwd=os.path.dirname(os.path.abspath(__file__)))
        print()
        print("🎉 Database seeding completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running seeder: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⚠️  Seeding interrupted by user")
        return False

def main():
    """Main function"""
    print("Allora Database Seeder Runner")
    print("============================")
    print()
    
    # Ask user for options
    clear_data = input("Clear existing data? (y/N): ").lower().startswith('y')
    
    try:
        num_products = int(input("Number of products to create (default 100): ") or "100")
    except ValueError:
        num_products = 100
    
    print()
    print(f"Configuration:")
    print(f"  - Clear existing data: {clear_data}")
    print(f"  - Number of products: {num_products}")
    print()
    
    confirm = input("Proceed with seeding? (Y/n): ").lower()
    if confirm and not confirm.startswith('y'):
        print("❌ Seeding cancelled")
        return
    
    # Run the seeder
    success = run_seeder(clear_data=clear_data, num_products=num_products)
    
    if success:
        print()
        print("🎯 Next Steps:")
        print("  1. Start your Flask application")
        print("  2. Visit the frontend to see the seeded data")
        print("  3. Login with admin credentials:")
        print("     - Username: admin")
        print("     - Password: admin123")
        print()
        print("  4. Or login as manager:")
        print("     - Username: manager") 
        print("     - Password: manager123")

if __name__ == "__main__":
    main()
