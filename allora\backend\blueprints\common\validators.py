"""
Request Validation Utilities
============================

Common validation functions for API requests.
"""

from flask import request
from typing import Dict, List, Any, Optional, Tuple
import logging

from .response_wrapper import validation_error_response, error_response
from .exceptions import ValidationError

logger = logging.getLogger(__name__)

def validate_json() -> Tuple[bool, Optional[Dict]]:
    """
    Validate that request contains valid JSON.
    
    Returns:
        Tuple of (is_valid, json_data_or_error_response)
    """
    if not request.is_json:
        return False, error_response(
            message="Request must contain valid JSON",
            status_code=400,
            error_code="INVALID_JSON"
        )
    
    try:
        data = request.get_json()
        if data is None:
            return False, error_response(
                message="Request body cannot be empty",
                status_code=400,
                error_code="EMPTY_BODY"
            )
        return True, data
    except Exception as e:
        logger.error(f"JSON validation error: {e}")
        return False, error_response(
            message="Invalid JSON format",
            status_code=400,
            error_code="MALFORMED_JSON"
        )

def validate_required_fields(data: Dict, required_fields: List[str]) -> Tuple[bool, Optional[tuple]]:
    """
    Validate that required fields are present in request data.
    
    Args:
        data: Request data dictionary
        required_fields: List of required field names
    
    Returns:
        Tuple of (is_valid, error_response_or_none)
    """
    missing_fields = []
    
    for field in required_fields:
        if field not in data or data[field] is None or data[field] == "":
            missing_fields.append(field)
    
    if missing_fields:
        return False, validation_error_response(
            errors={"missing_fields": missing_fields},
            message=f"Missing required fields: {', '.join(missing_fields)}"
        )
    
    return True, None

def validate_pagination(
    page: Optional[int] = None,
    per_page: Optional[int] = None,
    max_per_page: int = 100
) -> Tuple[int, int]:
    """
    Validate and normalize pagination parameters.
    
    Args:
        page: Page number from request
        per_page: Items per page from request
        max_per_page: Maximum allowed items per page
    
    Returns:
        Tuple of (normalized_page, normalized_per_page)
    """
    # Normalize page
    if page is None or page < 1:
        page = 1
    
    # Normalize per_page
    if per_page is None or per_page < 1:
        per_page = 20  # Default
    elif per_page > max_per_page:
        per_page = max_per_page
    
    return page, per_page

def validate_email(email: str) -> bool:
    """
    Validate email format.
    
    Args:
        email: Email address to validate
    
    Returns:
        True if valid, False otherwise
    """
    import re
    
    if not email or not isinstance(email, str):
        return False
    
    # Basic email regex pattern
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email.strip()) is not None

def validate_phone(phone: str) -> bool:
    """
    Validate phone number format.
    
    Args:
        phone: Phone number to validate
    
    Returns:
        True if valid, False otherwise
    """
    import re
    
    if not phone or not isinstance(phone, str):
        return False
    
    # Remove all non-digit characters
    digits_only = re.sub(r'\D', '', phone)
    
    # Check if it's a valid length (10-15 digits)
    return 10 <= len(digits_only) <= 15

def validate_password_strength(password: str) -> Tuple[bool, List[str]]:
    """
    Validate password strength.
    
    Args:
        password: Password to validate
    
    Returns:
        Tuple of (is_valid, list_of_errors)
    """
    errors = []
    
    if not password or not isinstance(password, str):
        errors.append("Password is required")
        return False, errors
    
    if len(password) < 8:
        errors.append("Password must be at least 8 characters long")
    
    if not any(c.isupper() for c in password):
        errors.append("Password must contain at least one uppercase letter")
    
    if not any(c.islower() for c in password):
        errors.append("Password must contain at least one lowercase letter")
    
    if not any(c.isdigit() for c in password):
        errors.append("Password must contain at least one number")
    
    # Check for special characters
    import re
    if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        errors.append("Password must contain at least one special character")
    
    return len(errors) == 0, errors

def validate_price(price: Any) -> Tuple[bool, Optional[float]]:
    """
    Validate and normalize price value.
    
    Args:
        price: Price value to validate
    
    Returns:
        Tuple of (is_valid, normalized_price_or_none)
    """
    try:
        price_float = float(price)
        if price_float < 0:
            return False, None
        return True, round(price_float, 2)
    except (ValueError, TypeError):
        return False, None

def validate_quantity(quantity: Any) -> Tuple[bool, Optional[int]]:
    """
    Validate and normalize quantity value.
    
    Args:
        quantity: Quantity value to validate
    
    Returns:
        Tuple of (is_valid, normalized_quantity_or_none)
    """
    try:
        quantity_int = int(quantity)
        if quantity_int < 0:
            return False, None
        return True, quantity_int
    except (ValueError, TypeError):
        return False, None

def validate_sort_params(
    sort_by: Optional[str],
    sort_order: Optional[str],
    allowed_sort_fields: List[str]
) -> Tuple[str, str]:
    """
    Validate and normalize sorting parameters.
    
    Args:
        sort_by: Field to sort by
        sort_order: Sort order (asc/desc)
        allowed_sort_fields: List of allowed sort fields
    
    Returns:
        Tuple of (normalized_sort_by, normalized_sort_order)
    """
    # Validate sort_by
    if not sort_by or sort_by not in allowed_sort_fields:
        sort_by = allowed_sort_fields[0] if allowed_sort_fields else 'id'
    
    # Validate sort_order
    if sort_order and sort_order.lower() in ['asc', 'desc']:
        sort_order = sort_order.lower()
    else:
        sort_order = 'asc'
    
    return sort_by, sort_order
