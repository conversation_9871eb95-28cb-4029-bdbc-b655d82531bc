#!/usr/bin/env python3
"""
Safe Backend Startup Script
===========================

This script starts the Allora backend with proper error handling
and initialization checks to avoid hanging issues.
"""

import os
import sys
import time
from datetime import datetime

def check_dependencies():
    """Check if all required dependencies are available"""
    print("🔍 Checking dependencies...")
    
    required_modules = [
        'flask', 'flask_sqlalchemy', 'flask_cors', 'flask_bcrypt', 
        'flask_jwt_extended', 'mysql.connector', 'redis', 'elasticsearch'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module.replace('-', '_'))
            print(f"  ✅ {module}")
        except ImportError:
            missing_modules.append(module)
            print(f"  ❌ {module}")
    
    if missing_modules:
        print(f"\n❌ Missing modules: {', '.join(missing_modules)}")
        print("Install with: pip install " + " ".join(missing_modules))
        return False
    
    print("✅ All dependencies available")
    return True

def check_services():
    """Check if required services are running"""
    print("\n🔍 Checking external services...")
    
    # Check MySQL
    try:
        import mysql.connector
        conn = mysql.connector.connect(
            host='localhost',
            user='root',
            password='Parul.2001',
            database='allora_db'
        )
        conn.close()
        print("  ✅ MySQL database")
    except Exception as e:
        print(f"  ❌ MySQL database: {e}")
        return False
    
    # Check Elasticsearch
    try:
        import requests
        response = requests.get('http://localhost:9200', timeout=5)
        if response.status_code == 200:
            print("  ✅ Elasticsearch")
        else:
            print(f"  ❌ Elasticsearch: Status {response.status_code}")
            return False
    except Exception as e:
        print(f"  ⚠️  Elasticsearch: {e} (will continue without search)")
    
    # Check Redis (optional)
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("  ✅ Redis")
    except Exception as e:
        print(f"  ⚠️  Redis: {e} (will continue without caching)")
    
    print("✅ Core services available")
    return True

def safe_import_app():
    """Safely import the Flask app with error handling"""
    print("\n🚀 Importing Flask application...")
    
    try:
        # Import the app
        from app import app, db
        print("✅ Flask app imported successfully")
        return app, db
    except Exception as e:
        print(f"❌ Failed to import Flask app: {e}")
        return None, None

def safe_initialize_database(app, db):
    """Safely initialize the database"""
    print("\n🗄️  Initializing database...")
    
    try:
        with app.app_context():
            # Create tables
            db.create_all()
            print("✅ Database tables created/verified")
            
            # Try to initialize other components safely
            try:
                from app import initialize_payment_gateways
                initialize_payment_gateways()
                print("✅ Payment gateways initialized")
            except Exception as e:
                print(f"⚠️  Payment gateways: {e}")
            
            try:
                from app import initialize_admin_user
                initialize_admin_user()
                print("✅ Admin user initialized")
            except Exception as e:
                print(f"⚠️  Admin user: {e}")
            
            try:
                from app import initialize_oauth_providers
                initialize_oauth_providers()
                print("✅ OAuth providers initialized")
            except Exception as e:
                print(f"⚠️  OAuth providers: {e}")
        
        return True
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False

def start_server(app):
    """Start the Flask server"""
    print("\n🌐 Starting Flask server...")
    print("=" * 50)
    print(f"📅 Server starting at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🌐 Server URL: http://localhost:5000")
    print("📊 API Health Check: http://localhost:5000/api/health")
    print("📋 API Documentation: Check BACKEND_FEATURES_COMPREHENSIVE.md")
    print("=" * 50)
    
    try:
        # Print some route information
        route_count = len(list(app.url_map.iter_rules()))
        print(f"📍 Total API routes registered: {route_count}")
        
        # Start the server
        app.run(
            debug=False,
            host='0.0.0.0',
            port=5000,
            threaded=True,
            use_reloader=False  # Disable reloader to avoid issues
        )
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")

def main():
    """Main startup function"""
    print("🚀 ALLORA E-COMMERCE BACKEND STARTUP")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Check services
    if not check_services():
        print("\n⚠️  Some services are not available, but continuing...")
    
    # Import app
    app, db = safe_import_app()
    if not app:
        sys.exit(1)
    
    # Initialize database
    if not safe_initialize_database(app, db):
        print("\n⚠️  Database initialization had issues, but continuing...")
    
    # Start server
    start_server(app)

if __name__ == "__main__":
    main()
