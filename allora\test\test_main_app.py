#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the main app
try:
    from app import app
    print("✅ Successfully imported main app")
    
    # Check if routes are registered
    print(f"📊 Total routes registered: {len(list(app.url_map.iter_rules()))}")
    
    # List first 10 routes
    print("\n🔍 First 10 routes:")
    for i, rule in enumerate(app.url_map.iter_rules()):
        if i < 10:
            print(f"  {rule.rule} -> {rule.endpoint} [{', '.join(rule.methods)}]")
    
    # Check for specific routes
    routes_to_check = ['/', '/api/products', '/api/signup']
    print(f"\n🎯 Checking specific routes:")
    for route in routes_to_check:
        found = False
        for rule in app.url_map.iter_rules():
            if rule.rule == route:
                print(f"  ✅ {route} -> {rule.endpoint} [{', '.join(rule.methods)}]")
                found = True
                break
        if not found:
            print(f"  ❌ {route} -> NOT FOUND")
    
    # Test the app context
    print(f"\n🧪 Testing app context...")
    with app.app_context():
        print("  ✅ App context works")
        
        # Try to create a test client
        client = app.test_client()
        print("  ✅ Test client created")
        
        # Test the root route
        try:
            response = client.get('/')
            print(f"  📡 Root route test: Status {response.status_code}")
            if response.status_code == 200:
                print(f"  📄 Response: {response.get_json()}")
            else:
                print(f"  ❌ Response data: {response.data}")
        except Exception as e:
            print(f"  ❌ Root route test failed: {e}")
            
except Exception as e:
    print(f"❌ Failed to import main app: {e}")
    import traceback
    traceback.print_exc()
