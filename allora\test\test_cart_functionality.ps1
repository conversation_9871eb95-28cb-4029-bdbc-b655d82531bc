# Test Cart Functionality
# =======================

$baseUrl = "http://localhost:5000"

Write-Host "Testing Cart Functionality..." -ForegroundColor Cyan
Write-Host "=============================" -ForegroundColor Cyan

# Login to get fresh token
Write-Host "`nLogging in..." -ForegroundColor Yellow
$loginData = @{
    email = "<EMAIL>"
    password = "TestPass123!"
} | ConvertTo-Json

try {
    $response = Invoke-WebRequest -Uri "$baseUrl/api/login" -Method POST -Body $loginData -ContentType "application/json"
    $loginResponse = $response.Content | ConvertFrom-Json
    $authToken = $loginResponse.token
    Write-Host "Login successful" -ForegroundColor Green
} catch {
    Write-Host "Login failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

$headers = @{
    "Authorization" = "Bearer $authToken"
    "Content-Type" = "application/json"
}

# Test 1: Get Products (to find a valid product ID)
Write-Host "`n1. Getting Products..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/api/products" -Method GET
    $products = $response.Content | ConvertFrom-Json
    if ($products.Count -gt 0) {
        $productId = $products[0].id
        Write-Host "Found product ID: $productId" -ForegroundColor Green
    } else {
        Write-Host "No products found" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "Failed to get products: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 2: Add to Cart
Write-Host "`n2. Adding Product to Cart..." -ForegroundColor Yellow
$cartData = @{
    product_id = $productId
    quantity = 2
} | ConvertTo-Json

try {
    $response = Invoke-WebRequest -Uri "$baseUrl/api/cart/add" -Method POST -Body $cartData -Headers $headers
    Write-Host "Add to Cart Status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor White
} catch {
    Write-Host "Add to Cart Failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $errorStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorStream)
        $errorContent = $reader.ReadToEnd()
        Write-Host "Error Details: $errorContent" -ForegroundColor Red
    }
}

# Test 3: Get Cart Contents
Write-Host "`n3. Getting Cart Contents..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/api/cart" -Method GET -Headers $headers
    Write-Host "Get Cart Status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Cart Contents: $($response.Content)" -ForegroundColor White
} catch {
    Write-Host "Get Cart Failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nCart Functionality Testing Complete!" -ForegroundColor Cyan
