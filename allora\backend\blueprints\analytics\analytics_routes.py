"""
Analytics Routes
================

Analytics and reporting endpoints with consistent response format.
"""

from flask import request
from datetime import datetime, timedelta
import logging

from ..common.versioning import create_versioned_blueprint
from ..common.response_wrapper import (
    success_response, error_response, validation_error_response
)
from ..common.auth_decorators import jwt_required_v2, admin_required_v2, rate_limit_v2
from ..common.validators import validate_pagination

logger = logging.getLogger(__name__)

# Create versioned blueprint
analytics_bp = create_versioned_blueprint('analytics', __name__, url_prefix='/analytics')

@analytics_bp.route('/sales/overview', methods=['GET'])
@admin_required_v2()
def get_sales_overview(user, admin_user):
    """
    Get sales overview analytics.
    
    GET /api/v1/analytics/sales/overview
    """
    try:
        # Get query parameters
        period = request.args.get('period', '30d')  # 7d, 30d, 90d, 1y
        
        # Calculate date range
        end_date = datetime.utcnow()
        if period == '7d':
            start_date = end_date - timedelta(days=7)
        elif period == '30d':
            start_date = end_date - timedelta(days=30)
        elif period == '90d':
            start_date = end_date - timedelta(days=90)
        elif period == '1y':
            start_date = end_date - timedelta(days=365)
        else:
            start_date = end_date - timedelta(days=30)
        
        from app import db, Sales, Order, OrderItem, Product
        
        # Total sales in period
        total_sales = db.session.query(
            db.func.sum(Sales.total_amount)
        ).filter(
            Sales.created_at >= start_date,
            Sales.created_at <= end_date
        ).scalar() or 0
        
        # Total orders in period
        total_orders = Order.query.filter(
            Order.created_at >= start_date,
            Order.created_at <= end_date,
            Order.status.in_(['completed', 'shipped', 'delivered'])
        ).count()
        
        # Average order value
        avg_order_value = float(total_sales) / total_orders if total_orders > 0 else 0
        
        # Top selling products
        top_products = db.session.query(
            Product.id,
            Product.name,
            db.func.sum(Sales.quantity).label('total_sold'),
            db.func.sum(Sales.total_amount).label('total_revenue')
        ).join(
            Sales, Product.id == Sales.product_id
        ).filter(
            Sales.created_at >= start_date,
            Sales.created_at <= end_date
        ).group_by(
            Product.id, Product.name
        ).order_by(
            db.desc('total_sold')
        ).limit(10).all()
        
        top_products_data = []
        for product_id, name, total_sold, total_revenue in top_products:
            top_products_data.append({
                "product_id": product_id,
                "name": name,
                "total_sold": int(total_sold),
                "total_revenue": float(total_revenue)
            })
        
        # Daily sales trend
        daily_sales = db.session.query(
            db.func.date(Sales.created_at).label('date'),
            db.func.sum(Sales.total_amount).label('daily_total'),
            db.func.count(Sales.id).label('daily_orders')
        ).filter(
            Sales.created_at >= start_date,
            Sales.created_at <= end_date
        ).group_by(
            db.func.date(Sales.created_at)
        ).order_by('date').all()
        
        daily_trend = []
        for date, daily_total, daily_orders in daily_sales:
            daily_trend.append({
                "date": date.isoformat(),
                "sales": float(daily_total),
                "orders": int(daily_orders)
            })
        
        overview_data = {
            "period": period,
            "date_range": {
                "start": start_date.isoformat(),
                "end": end_date.isoformat()
            },
            "metrics": {
                "total_sales": float(total_sales),
                "total_orders": total_orders,
                "average_order_value": round(avg_order_value, 2)
            },
            "top_products": top_products_data,
            "daily_trend": daily_trend
        }
        
        return success_response(
            data=overview_data,
            message="Sales overview retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get sales overview error: {e}")
        return error_response(
            message="Failed to retrieve sales overview",
            status_code=500,
            error_code="SALES_OVERVIEW_FAILED"
        )

@analytics_bp.route('/user-behavior/summary', methods=['GET'])
@admin_required_v2()
def get_user_behavior_summary(user, admin_user):
    """
    Get user behavior analytics summary.
    
    GET /api/v1/analytics/user-behavior/summary
    """
    try:
        period = request.args.get('period', '30d')
        
        # Calculate date range
        end_date = datetime.utcnow()
        if period == '7d':
            start_date = end_date - timedelta(days=7)
        elif period == '30d':
            start_date = end_date - timedelta(days=30)
        elif period == '90d':
            start_date = end_date - timedelta(days=90)
        else:
            start_date = end_date - timedelta(days=30)
        
        from app import db, UserInteractionLog, User
        
        # Total active users
        active_users = db.session.query(
            db.func.count(db.distinct(UserInteractionLog.user_id))
        ).filter(
            UserInteractionLog.created_at >= start_date,
            UserInteractionLog.created_at <= end_date
        ).scalar() or 0
        
        # Most popular actions
        popular_actions = db.session.query(
            UserInteractionLog.action,
            db.func.count(UserInteractionLog.id).label('count')
        ).filter(
            UserInteractionLog.created_at >= start_date,
            UserInteractionLog.created_at <= end_date
        ).group_by(
            UserInteractionLog.action
        ).order_by(
            db.desc('count')
        ).limit(10).all()
        
        actions_data = []
        for action, count in popular_actions:
            actions_data.append({
                "action": action,
                "count": int(count)
            })
        
        # Page views by page
        page_views = db.session.query(
            UserInteractionLog.page,
            db.func.count(UserInteractionLog.id).label('views')
        ).filter(
            UserInteractionLog.created_at >= start_date,
            UserInteractionLog.created_at <= end_date,
            UserInteractionLog.action == 'page_view'
        ).group_by(
            UserInteractionLog.page
        ).order_by(
            db.desc('views')
        ).limit(10).all()
        
        pages_data = []
        for page, views in page_views:
            pages_data.append({
                "page": page,
                "views": int(views)
            })
        
        behavior_data = {
            "period": period,
            "date_range": {
                "start": start_date.isoformat(),
                "end": end_date.isoformat()
            },
            "metrics": {
                "active_users": active_users,
                "total_interactions": sum(action['count'] for action in actions_data)
            },
            "popular_actions": actions_data,
            "popular_pages": pages_data
        }
        
        return success_response(
            data=behavior_data,
            message="User behavior summary retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get user behavior summary error: {e}")
        return error_response(
            message="Failed to retrieve user behavior summary",
            status_code=500,
            error_code="USER_BEHAVIOR_SUMMARY_FAILED"
        )

@analytics_bp.route('/search/analytics', methods=['GET'])
@admin_required_v2()
def get_search_analytics(user, admin_user):
    """
    Get search analytics data.
    
    GET /api/v1/analytics/search/analytics
    """
    try:
        period = request.args.get('period', '30d')
        
        # Calculate date range
        end_date = datetime.utcnow()
        if period == '7d':
            start_date = end_date - timedelta(days=7)
        elif period == '30d':
            start_date = end_date - timedelta(days=30)
        elif period == '90d':
            start_date = end_date - timedelta(days=90)
        else:
            start_date = end_date - timedelta(days=30)
        
        from app import db, SearchAnalytics
        
        # Total searches
        total_searches = SearchAnalytics.query.filter(
            SearchAnalytics.created_at >= start_date,
            SearchAnalytics.created_at <= end_date
        ).count()
        
        # Top search queries
        top_queries = db.session.query(
            SearchAnalytics.query,
            db.func.count(SearchAnalytics.id).label('count'),
            db.func.avg(SearchAnalytics.results_count).label('avg_results')
        ).filter(
            SearchAnalytics.created_at >= start_date,
            SearchAnalytics.created_at <= end_date
        ).group_by(
            SearchAnalytics.query
        ).order_by(
            db.desc('count')
        ).limit(20).all()
        
        queries_data = []
        for query, count, avg_results in top_queries:
            queries_data.append({
                "query": query,
                "search_count": int(count),
                "average_results": round(float(avg_results or 0), 1)
            })
        
        # Zero result searches
        zero_result_searches = db.session.query(
            SearchAnalytics.query,
            db.func.count(SearchAnalytics.id).label('count')
        ).filter(
            SearchAnalytics.created_at >= start_date,
            SearchAnalytics.created_at <= end_date,
            SearchAnalytics.results_count == 0
        ).group_by(
            SearchAnalytics.query
        ).order_by(
            db.desc('count')
        ).limit(10).all()
        
        zero_results_data = []
        for query, count in zero_result_searches:
            zero_results_data.append({
                "query": query,
                "count": int(count)
            })
        
        search_data = {
            "period": period,
            "date_range": {
                "start": start_date.isoformat(),
                "end": end_date.isoformat()
            },
            "metrics": {
                "total_searches": total_searches,
                "zero_result_searches": len(zero_results_data)
            },
            "top_queries": queries_data,
            "zero_result_queries": zero_results_data
        }
        
        return success_response(
            data=search_data,
            message="Search analytics retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get search analytics error: {e}")
        return error_response(
            message="Failed to retrieve search analytics",
            status_code=500,
            error_code="SEARCH_ANALYTICS_FAILED"
        )

@analytics_bp.route('/inventory/trends', methods=['GET'])
@admin_required_v2()
def get_inventory_trends(user, admin_user):
    """
    Get inventory and stock trend analytics.
    
    GET /api/v1/analytics/inventory/trends
    """
    try:
        from app import db, Product, Sales
        
        # Low stock products (less than 10 items)
        low_stock_products = Product.query.filter(
            Product.stock_quantity < 10,
            Product.stock_quantity > 0,
            Product.is_active == True
        ).order_by(Product.stock_quantity.asc()).limit(20).all()
        
        low_stock_data = []
        for product in low_stock_products:
            low_stock_data.append({
                "id": product.id,
                "name": product.name,
                "current_stock": product.stock_quantity,
                "category": product.category,
                "price": float(product.price)
            })
        
        # Out of stock products
        out_of_stock_count = Product.query.filter(
            Product.stock_quantity == 0,
            Product.is_active == True
        ).count()
        
        # Fast moving products (high sales velocity)
        fast_moving = db.session.query(
            Product.id,
            Product.name,
            Product.stock_quantity,
            db.func.sum(Sales.quantity).label('total_sold')
        ).join(
            Sales, Product.id == Sales.product_id
        ).filter(
            Sales.created_at >= datetime.utcnow() - timedelta(days=30)
        ).group_by(
            Product.id, Product.name, Product.stock_quantity
        ).order_by(
            db.desc('total_sold')
        ).limit(20).all()
        
        fast_moving_data = []
        for product_id, name, stock, total_sold in fast_moving:
            fast_moving_data.append({
                "product_id": product_id,
                "name": name,
                "current_stock": stock,
                "sold_last_30_days": int(total_sold),
                "velocity": round(float(total_sold) / 30, 2)  # items per day
            })
        
        inventory_data = {
            "metrics": {
                "low_stock_count": len(low_stock_data),
                "out_of_stock_count": out_of_stock_count,
                "total_active_products": Product.query.filter_by(is_active=True).count()
            },
            "low_stock_products": low_stock_data,
            "fast_moving_products": fast_moving_data
        }
        
        return success_response(
            data=inventory_data,
            message="Inventory trends retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get inventory trends error: {e}")
        return error_response(
            message="Failed to retrieve inventory trends",
            status_code=500,
            error_code="INVENTORY_TRENDS_FAILED"
        )
