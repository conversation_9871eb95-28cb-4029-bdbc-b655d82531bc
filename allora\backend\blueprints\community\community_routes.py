"""
Community Routes
================

Community and social features endpoints with consistent response format.
"""

from flask import request
from datetime import datetime
import logging

from ..common.versioning import create_versioned_blueprint
from ..common.response_wrapper import (
    success_response, error_response, paginated_response, 
    not_found_response, validation_error_response
)
from ..common.auth_decorators import jwt_required_v2, optional_auth, rate_limit_v2, validate_content_type
from ..common.validators import (
    validate_json, validate_required_fields, validate_pagination
)

logger = logging.getLogger(__name__)

# Create versioned blueprint
community_bp = create_versioned_blueprint('community', __name__, url_prefix='/community')

@community_bp.route('/posts', methods=['GET'])
@optional_auth()
def get_posts(user):
    """
    Get community posts with pagination and filtering.
    
    GET /api/v1/community/posts
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        hashtag = request.args.get('hashtag')
        user_id = request.args.get('user_id', type=int)
        
        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=50)
        
        from app import CommunityPost, User, PostLike, PostComment, Hashtag, PostHashtag
        
        # Build query
        query = CommunityPost.query.filter_by(is_active=True)
        
        if hashtag:
            query = query.join(PostHashtag).join(Hashtag).filter(
                Hashtag.name.ilike(f'%{hashtag}%')
            )
        
        if user_id:
            query = query.filter_by(user_id=user_id)
        
        query = query.order_by(CommunityPost.created_at.desc())
        
        # Execute paginated query
        paginated_posts = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        posts_data = []
        for post in paginated_posts.items:
            # Get post author
            author = User.query.get(post.user_id)
            
            # Get likes count and user's like status
            likes_count = PostLike.query.filter_by(post_id=post.id).count()
            user_liked = False
            if user:
                user_liked = PostLike.query.filter_by(
                    post_id=post.id, 
                    user_id=user.id
                ).first() is not None
            
            # Get comments count
            comments_count = PostComment.query.filter_by(post_id=post.id).count()
            
            # Get hashtags
            hashtags = []
            post_hashtags = PostHashtag.query.filter_by(post_id=post.id).all()
            for ph in post_hashtags:
                hashtag_obj = Hashtag.query.get(ph.hashtag_id)
                if hashtag_obj:
                    hashtags.append(hashtag_obj.name)
            
            post_data = {
                "id": post.id,
                "content": post.content,
                "image_url": post.image_url,
                "author": {
                    "id": author.id if author else None,
                    "name": f"{author.first_name} {author.last_name}" if author else "Unknown User",
                    "avatar": getattr(author, 'avatar_url', None) if author else None
                },
                "likes_count": likes_count,
                "comments_count": comments_count,
                "user_liked": user_liked,
                "hashtags": hashtags,
                "created_at": post.created_at.isoformat() if post.created_at else None,
                "updated_at": post.updated_at.isoformat() if post.updated_at else None
            }
            posts_data.append(post_data)
        
        return paginated_response(
            data=posts_data,
            page=page,
            per_page=per_page,
            total=paginated_posts.total,
            message="Community posts retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get posts error: {e}")
        return error_response(
            message="Failed to retrieve community posts",
            status_code=500,
            error_code="POSTS_FETCH_FAILED"
        )

@community_bp.route('/posts', methods=['POST'])
@jwt_required_v2()
@validate_content_type()
@rate_limit_v2(limit=10, window=3600, per='user')  # 10 posts per hour
def create_post(user):
    """
    Create a new community post.
    
    POST /api/v1/community/posts
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data
        
        # Validate required fields
        required_fields = ['content']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp
        
        content = data['content'].strip()
        image_url = data.get('image_url')
        hashtags = data.get('hashtags', [])
        
        # Validate content length
        if len(content) < 10:
            return validation_error_response(
                errors={"content": ["Content must be at least 10 characters long"]},
                message="Content too short"
            )
        
        if len(content) > 2000:
            return validation_error_response(
                errors={"content": ["Content must be less than 2000 characters"]},
                message="Content too long"
            )
        
        from app import db, CommunityPost, Hashtag, PostHashtag
        
        # Create new post
        new_post = CommunityPost(
            user_id=user.id,
            content=content,
            image_url=image_url,
            is_active=True,
            created_at=datetime.utcnow()
        )
        
        db.session.add(new_post)
        db.session.flush()  # Get post ID
        
        # Process hashtags
        processed_hashtags = []
        if hashtags and isinstance(hashtags, list):
            for hashtag_name in hashtags[:10]:  # Limit to 10 hashtags
                hashtag_name = hashtag_name.strip().lower()
                if hashtag_name and len(hashtag_name) <= 50:
                    # Find or create hashtag
                    hashtag = Hashtag.query.filter_by(name=hashtag_name).first()
                    if not hashtag:
                        hashtag = Hashtag(
                            name=hashtag_name,
                            created_at=datetime.utcnow()
                        )
                        db.session.add(hashtag)
                        db.session.flush()
                    
                    # Link hashtag to post
                    post_hashtag = PostHashtag(
                        post_id=new_post.id,
                        hashtag_id=hashtag.id
                    )
                    db.session.add(post_hashtag)
                    processed_hashtags.append(hashtag_name)
        
        db.session.commit()
        
        post_data = {
            "id": new_post.id,
            "content": new_post.content,
            "image_url": new_post.image_url,
            "author": {
                "id": user.id,
                "name": f"{user.first_name} {user.last_name}",
                "avatar": getattr(user, 'avatar_url', None)
            },
            "likes_count": 0,
            "comments_count": 0,
            "user_liked": False,
            "hashtags": processed_hashtags,
            "created_at": new_post.created_at.isoformat()
        }
        
        return success_response(
            data=post_data,
            message="Post created successfully",
            status_code=201
        )
        
    except Exception as e:
        logger.error(f"Create post error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to create post",
            status_code=500,
            error_code="POST_CREATION_FAILED"
        )

@community_bp.route('/posts/<int:post_id>/like', methods=['POST'])
@jwt_required_v2()
@rate_limit_v2(limit=100, window=3600, per='user')  # 100 likes per hour
def like_post(user, post_id):
    """
    Like or unlike a community post.
    
    POST /api/v1/community/posts/{post_id}/like
    """
    try:
        from app import db, CommunityPost, PostLike
        
        # Check if post exists
        post = CommunityPost.query.filter_by(id=post_id, is_active=True).first()
        if not post:
            return not_found_response("Post", post_id)
        
        # Check if user already liked the post
        existing_like = PostLike.query.filter_by(
            post_id=post_id,
            user_id=user.id
        ).first()
        
        if existing_like:
            # Unlike the post
            db.session.delete(existing_like)
            action = "unliked"
        else:
            # Like the post
            new_like = PostLike(
                post_id=post_id,
                user_id=user.id,
                created_at=datetime.utcnow()
            )
            db.session.add(new_like)
            action = "liked"
        
        db.session.commit()
        
        # Get updated likes count
        likes_count = PostLike.query.filter_by(post_id=post_id).count()
        
        return success_response(
            data={
                "post_id": post_id,
                "action": action,
                "likes_count": likes_count,
                "user_liked": action == "liked"
            },
            message=f"Post {action} successfully"
        )
        
    except Exception as e:
        logger.error(f"Like post error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to process like",
            status_code=500,
            error_code="LIKE_PROCESSING_FAILED"
        )

@community_bp.route('/posts/<int:post_id>/comments', methods=['GET'])
@optional_auth()
def get_post_comments(user, post_id):
    """
    Get comments for a specific post.
    
    GET /api/v1/community/posts/{post_id}/comments
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=50)
        
        from app import CommunityPost, PostComment, User
        
        # Check if post exists
        post = CommunityPost.query.filter_by(id=post_id, is_active=True).first()
        if not post:
            return not_found_response("Post", post_id)
        
        # Get comments with pagination
        query = PostComment.query.filter_by(post_id=post_id).order_by(
            PostComment.created_at.asc()
        )
        
        paginated_comments = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        comments_data = []
        for comment in paginated_comments.items:
            author = User.query.get(comment.user_id)
            
            comment_data = {
                "id": comment.id,
                "content": comment.content,
                "author": {
                    "id": author.id if author else None,
                    "name": f"{author.first_name} {author.last_name}" if author else "Unknown User",
                    "avatar": getattr(author, 'avatar_url', None) if author else None
                },
                "created_at": comment.created_at.isoformat() if comment.created_at else None
            }
            comments_data.append(comment_data)
        
        return paginated_response(
            data=comments_data,
            page=page,
            per_page=per_page,
            total=paginated_comments.total,
            message="Comments retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get comments error: {e}")
        return error_response(
            message="Failed to retrieve comments",
            status_code=500,
            error_code="COMMENTS_FETCH_FAILED"
        )

@community_bp.route('/posts/<int:post_id>/comments', methods=['POST'])
@jwt_required_v2()
@validate_content_type()
@rate_limit_v2(limit=30, window=3600, per='user')  # 30 comments per hour
def add_comment(user, post_id):
    """
    Add a comment to a post.
    
    POST /api/v1/community/posts/{post_id}/comments
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data
        
        # Validate required fields
        required_fields = ['content']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp
        
        content = data['content'].strip()
        
        # Validate content
        if len(content) < 1:
            return validation_error_response(
                errors={"content": ["Comment cannot be empty"]},
                message="Empty comment"
            )
        
        if len(content) > 500:
            return validation_error_response(
                errors={"content": ["Comment must be less than 500 characters"]},
                message="Comment too long"
            )
        
        from app import db, CommunityPost, PostComment
        
        # Check if post exists
        post = CommunityPost.query.filter_by(id=post_id, is_active=True).first()
        if not post:
            return not_found_response("Post", post_id)
        
        # Create new comment
        new_comment = PostComment(
            post_id=post_id,
            user_id=user.id,
            content=content,
            created_at=datetime.utcnow()
        )
        
        db.session.add(new_comment)
        db.session.commit()
        
        comment_data = {
            "id": new_comment.id,
            "content": new_comment.content,
            "author": {
                "id": user.id,
                "name": f"{user.first_name} {user.last_name}",
                "avatar": getattr(user, 'avatar_url', None)
            },
            "created_at": new_comment.created_at.isoformat()
        }
        
        return success_response(
            data=comment_data,
            message="Comment added successfully",
            status_code=201
        )
        
    except Exception as e:
        logger.error(f"Add comment error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to add comment",
            status_code=500,
            error_code="COMMENT_ADD_FAILED"
        )
