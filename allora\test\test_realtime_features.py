#!/usr/bin/env python3
"""
Real-time Features Testing Script
=================================

Tests actual Flask-SocketIO real-time broadcasting functionality
by triggering backend events and monitoring WebSocket responses.

Author: Allora Development Team
Date: 2025-07-13
"""

import socketio
import time
import threading
import requests
import json
from datetime import datetime

class RealTimeFeatureTester:
    """Tests real-time broadcasting features"""
    
    def __init__(self, server_url='http://localhost:5000'):
        self.server_url = server_url
        self.client = None
        self.connected = False
        self.real_time_events = []
        
    def setup_client(self):
        """Setup SocketIO client for real-time monitoring"""
        print("🔌 Setting up real-time monitoring client...")
        
        self.client = socketio.Client()
        
        @self.client.event
        def connect():
            self.connected = True
            print("✅ Real-time monitor connected!")
            
        @self.client.event
        def disconnect():
            self.connected = False
            print("❌ Real-time monitor disconnected")
            
        @self.client.event
        def inventory_update(data):
            event = {
                'type': 'inventory_update',
                'data': data,
                'timestamp': datetime.now().isoformat()
            }
            self.real_time_events.append(event)
            print(f"📦 REAL-TIME: Inventory update received - Product {data.get('product_id')}: {data.get('new_quantity')} units")
            
        @self.client.event
        def price_update(data):
            event = {
                'type': 'price_update',
                'data': data,
                'timestamp': datetime.now().isoformat()
            }
            self.real_time_events.append(event)
            print(f"💰 REAL-TIME: Price update received - Product {data.get('product_id')}: ${data.get('new_price')}")
            
        @self.client.event
        def cart_update(data):
            event = {
                'type': 'cart_update',
                'data': data,
                'timestamp': datetime.now().isoformat()
            }
            self.real_time_events.append(event)
            print(f"🛒 REAL-TIME: Cart update received - {data}")
            
        @self.client.event
        def notification(data):
            event = {
                'type': 'notification',
                'data': data,
                'timestamp': datetime.now().isoformat()
            }
            self.real_time_events.append(event)
            print(f"🔔 REAL-TIME: Notification received - {data}")
            
        @self.client.event
        def admin_notification(data):
            event = {
                'type': 'admin_notification',
                'data': data,
                'timestamp': datetime.now().isoformat()
            }
            self.real_time_events.append(event)
            print(f"👑 REAL-TIME: Admin notification received - {data}")
    
    def connect_monitor(self):
        """Connect the real-time monitor"""
        try:
            self.client.connect(self.server_url, wait_timeout=10)

            # Wait a moment for connection to stabilize
            time.sleep(1)

            # Subscribe to all events to ensure we receive them
            if self.connected:
                self.client.emit('subscribe', {'events': ['inventory', 'prices', 'orders', 'notifications', 'admin']})
                time.sleep(1)  # Wait for subscription to process

            return self.connected
        except Exception as e:
            print(f"❌ Failed to connect monitor: {e}")
            return False
    
    def test_inventory_broadcasting(self):
        """Test inventory update broadcasting"""
        print("\n🧪 Testing Inventory Broadcasting...")
        
        if not self.connected:
            print("❌ Monitor not connected")
            return False
        
        try:
            # Import and trigger inventory update directly
            from flask_socketio_manager import broadcast_inventory_update
            
            print("📦 Triggering inventory update...")
            broadcast_inventory_update(
                product_id=123,
                new_quantity=50,
                old_quantity=75
            )
            
            # Wait for broadcast (longer wait)
            time.sleep(3)

            # Check if event received
            inventory_events = [e for e in self.real_time_events if e['type'] == 'inventory_update']

            if inventory_events:
                print("✅ Inventory broadcasting test PASSED")
                print(f"   Event data: {inventory_events[0]['data']}")
                return True
            else:
                print("❌ Inventory broadcasting test FAILED - No events received")
                print(f"   Total events received: {len(self.real_time_events)}")
                print(f"   Event types: {[e['type'] for e in self.real_time_events]}")
                return False
                
        except Exception as e:
            print(f"❌ Inventory broadcasting test FAILED: {e}")
            return False
    
    def test_price_broadcasting(self):
        """Test price update broadcasting"""
        print("\n🧪 Testing Price Broadcasting...")
        
        if not self.connected:
            print("❌ Monitor not connected")
            return False
        
        try:
            # Import and trigger price update directly
            from flask_socketio_manager import broadcast_price_update
            
            print("💰 Triggering price update...")
            broadcast_price_update(
                product_id=456,
                new_price=29.99,
                old_price=34.99
            )
            
            # Wait for broadcast
            time.sleep(2)
            
            # Check if event received
            price_events = [e for e in self.real_time_events if e['type'] == 'price_update']
            
            if price_events:
                print("✅ Price broadcasting test PASSED")
                return True
            else:
                print("❌ Price broadcasting test FAILED - No events received")
                return False
                
        except Exception as e:
            print(f"❌ Price broadcasting test FAILED: {e}")
            return False
    
    def test_cart_broadcasting(self):
        """Test cart update broadcasting"""
        print("\n🧪 Testing Cart Broadcasting...")
        
        if not self.connected:
            print("❌ Monitor not connected")
            return False
        
        try:
            # Import and trigger cart update directly
            from flask_socketio_manager import send_cart_update
            
            print("🛒 Triggering cart update...")
            send_cart_update(
                user_id="test_user_123",
                cart_data={
                    "items": 3,
                    "total": 89.97,
                    "updated_at": datetime.now().isoformat()
                }
            )
            
            # Wait for broadcast
            time.sleep(2)
            
            # Check if event received
            cart_events = [e for e in self.real_time_events if e['type'] == 'cart_update']
            
            if cart_events:
                print("✅ Cart broadcasting test PASSED")
                return True
            else:
                print("❌ Cart broadcasting test FAILED - No events received")
                return False
                
        except Exception as e:
            print(f"❌ Cart broadcasting test FAILED: {e}")
            return False
    
    def test_notification_broadcasting(self):
        """Test notification broadcasting"""
        print("\n🧪 Testing Notification Broadcasting...")
        
        if not self.connected:
            print("❌ Monitor not connected")
            return False
        
        try:
            # Import and trigger notification directly
            from flask_socketio_manager import send_notification
            
            print("🔔 Triggering notification...")
            send_notification(
                user_id="test_user_123",
                notification_data={
                    "title": "Test Notification",
                    "message": "This is a test notification from the real-time system",
                    "type": "info"
                }
            )
            
            # Wait for broadcast
            time.sleep(2)
            
            # Check if event received
            notification_events = [e for e in self.real_time_events if e['type'] == 'notification']
            
            if notification_events:
                print("✅ Notification broadcasting test PASSED")
                return True
            else:
                print("❌ Notification broadcasting test FAILED - No events received")
                return False
                
        except Exception as e:
            print(f"❌ Notification broadcasting test FAILED: {e}")
            return False
    
    def test_admin_broadcasting(self):
        """Test admin notification broadcasting"""
        print("\n🧪 Testing Admin Broadcasting...")
        
        if not self.connected:
            print("❌ Monitor not connected")
            return False
        
        try:
            # Import and trigger admin notification directly
            from flask_socketio_manager import socketio_manager
            
            print("👑 Triggering admin notification...")
            socketio_manager.broadcast_to_admins({
                "type": "system_alert",
                "message": "Test admin notification from real-time system",
                "priority": "normal",
                "timestamp": datetime.now().isoformat()
            })
            
            # Wait for broadcast
            time.sleep(2)
            
            # Check if event received
            admin_events = [e for e in self.real_time_events if e['type'] == 'admin_notification']
            
            if admin_events:
                print("✅ Admin broadcasting test PASSED")
                return True
            else:
                print("❌ Admin broadcasting test FAILED - No events received")
                return False
                
        except Exception as e:
            print(f"❌ Admin broadcasting test FAILED: {e}")
            return False
    
    def test_connection_stats(self):
        """Test connection statistics"""
        print("\n🧪 Testing Connection Statistics...")
        
        try:
            from flask_socketio_manager import socketio_manager
            
            stats = socketio_manager.get_connection_stats()
            print(f"📊 Connection Stats: {stats}")
            
            if isinstance(stats, dict) and 'total_connections' in stats:
                print("✅ Connection statistics test PASSED")
                return True
            else:
                print("❌ Connection statistics test FAILED")
                return False
                
        except Exception as e:
            print(f"❌ Connection statistics test FAILED: {e}")
            return False
    
    def disconnect_monitor(self):
        """Disconnect the monitor"""
        if self.client and self.connected:
            self.client.disconnect()

def run_realtime_tests():
    """Run comprehensive real-time feature tests"""
    print("🚀 Flask-SocketIO Real-time Features Test Suite")
    print("=" * 60)
    
    # Create tester
    tester = RealTimeFeatureTester()
    tester.setup_client()
    
    # Connect monitor
    if not tester.connect_monitor():
        print("❌ Failed to connect real-time monitor")
        print("💡 Make sure the SocketIO server is running:")
        print("   python run_socketio_server.py")
        return
    
    # Test results
    results = {}
    
    # Run tests
    results['inventory_broadcasting'] = tester.test_inventory_broadcasting()
    results['price_broadcasting'] = tester.test_price_broadcasting()
    results['cart_broadcasting'] = tester.test_cart_broadcasting()
    results['notification_broadcasting'] = tester.test_notification_broadcasting()
    results['admin_broadcasting'] = tester.test_admin_broadcasting()
    results['connection_stats'] = tester.test_connection_stats()
    
    # Disconnect
    tester.disconnect_monitor()
    
    # Print results
    print("\n" + "=" * 60)
    print("📊 REAL-TIME FEATURES TEST RESULTS")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    print(f"\n📊 Real-time Events Captured: {len(tester.real_time_events)}")
    for event in tester.real_time_events:
        print(f"   - {event['type']}: {event['timestamp']}")
    
    # Final assessment
    if passed == total:
        print("\n🎉 ALL REAL-TIME FEATURES WORKING PERFECTLY!")
        print("✅ Your Flask-SocketIO system is production-ready!")
    elif passed >= total * 0.8:
        print("\n✅ Most real-time features working - Minor issues detected")
    else:
        print("\n⚠️  Some real-time features need attention")
    
    return results

if __name__ == '__main__':
    run_realtime_tests()
