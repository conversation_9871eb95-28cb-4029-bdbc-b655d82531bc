#!/usr/bin/env python3
"""
Search Analytics Table Fix Script
=================================

This script fixes the search_analytics table by resolving conflicts between
different model definitions and ensuring data consistency.

Issues Found:
1. Conflicting model definitions (app.py vs search_analytics_tracker.py)
2. Missing columns (created_at, search_query, filters_applied, guest_session_id)
3. Type mismatches (INTEGER vs VARCHAR for IDs)
4. Missing foreign key constraints
5. Missing indexes for performance

Usage:
    python fix_search_analytics_table.py [--backup] [--apply]
"""

import os
import sys
import argparse
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from sqlalchemy import text

def backup_search_analytics_table():
    """Create a backup of the current search_analytics table"""
    print("💾 CREATING BACKUP")
    print("-" * 30)
    
    with app.app_context():
        try:
            backup_table_name = f"search_analytics_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            with db.engine.connect() as connection:
                # Check if table has data
                result = connection.execute(text("SELECT COUNT(*) FROM search_analytics"))
                row_count = result.fetchone()[0]
                
                if row_count > 0:
                    # Create backup table
                    backup_sql = f"CREATE TABLE {backup_table_name} AS SELECT * FROM search_analytics"
                    connection.execute(text(backup_sql))
                    connection.commit()
                    
                    print(f"✅ Backup created: {backup_table_name} ({row_count:,} rows)")
                    return backup_table_name
                else:
                    print("ℹ️  Table is empty, no backup needed")
                    return None
                    
        except Exception as e:
            print(f"❌ Error creating backup: {e}")
            return None

def fix_search_analytics_structure():
    """Fix the search_analytics table structure"""
    print("\n🔧 FIXING TABLE STRUCTURE")
    print("-" * 40)
    
    with app.app_context():
        try:
            with db.engine.connect() as connection:
                # Drop the existing tables (since they have structural conflicts)
                print("🗑️  Dropping existing tables...")
                connection.execute(text("DROP TABLE IF EXISTS search_conversions"))
                connection.execute(text("DROP TABLE IF EXISTS search_clicks"))
                connection.execute(text("DROP TABLE IF EXISTS search_analytics"))

                # Create the new table with correct structure (matching app.py model)
                create_table_sql = """
                CREATE TABLE search_analytics (
                    id INTEGER AUTO_INCREMENT PRIMARY KEY,
                    user_id INTEGER NULL,
                    guest_session_id VARCHAR(100) NULL,
                    search_query VARCHAR(500) NOT NULL,
                    search_type VARCHAR(50) NOT NULL DEFAULT 'text',
                    results_count INTEGER NOT NULL DEFAULT 0,
                    filters_applied JSON NULL,
                    clicked_results JSON NULL,
                    session_id VARCHAR(100) NULL,
                    ip_address VARCHAR(45) NULL,
                    user_agent VARCHAR(500) NULL,
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    
                    -- Add foreign key constraint
                    CONSTRAINT fk_search_analytics_user_id 
                        FOREIGN KEY (user_id) REFERENCES users(id) 
                        ON DELETE SET NULL ON UPDATE CASCADE,
                    
                    -- Add indexes for performance
                    INDEX idx_search_analytics_user_id (user_id),
                    INDEX idx_search_analytics_session_id (session_id),
                    INDEX idx_search_analytics_search_query (search_query(255)),
                    INDEX idx_search_analytics_search_type (search_type),
                    INDEX idx_search_analytics_created_at (created_at),
                    INDEX idx_search_analytics_guest_session (guest_session_id)
                )
                """
                
                print("🏗️  Creating new search_analytics table...")
                connection.execute(text(create_table_sql))
                
                # Also create the additional tables used by search_analytics_tracker.py
                print("🏗️  Creating search_clicks table...")
                create_clicks_table_sql = """
                CREATE TABLE search_clicks (
                    id VARCHAR(36) PRIMARY KEY,
                    search_analytics_id INTEGER NOT NULL,
                    product_id INTEGER NOT NULL,
                    position INTEGER NOT NULL,
                    timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    user_id INTEGER NULL,
                    session_id VARCHAR(64) NOT NULL,
                    
                    CONSTRAINT fk_search_clicks_search_analytics 
                        FOREIGN KEY (search_analytics_id) REFERENCES search_analytics(id) 
                        ON DELETE CASCADE ON UPDATE CASCADE,
                    CONSTRAINT fk_search_clicks_user_id 
                        FOREIGN KEY (user_id) REFERENCES users(id) 
                        ON DELETE SET NULL ON UPDATE CASCADE,
                    CONSTRAINT fk_search_clicks_product_id 
                        FOREIGN KEY (product_id) REFERENCES products(id) 
                        ON DELETE CASCADE ON UPDATE CASCADE,
                    
                    INDEX idx_search_clicks_search_analytics (search_analytics_id),
                    INDEX idx_search_clicks_product_id (product_id),
                    INDEX idx_search_clicks_user_id (user_id),
                    INDEX idx_search_clicks_timestamp (timestamp)
                )
                """
                connection.execute(text(create_clicks_table_sql))
                
                print("🏗️  Creating search_conversions table...")
                create_conversions_table_sql = """
                CREATE TABLE search_conversions (
                    id VARCHAR(36) PRIMARY KEY,
                    search_analytics_id INTEGER NOT NULL,
                    product_id INTEGER NOT NULL,
                    conversion_type VARCHAR(20) NOT NULL,
                    conversion_value FLOAT NULL,
                    timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    user_id INTEGER NULL,
                    session_id VARCHAR(64) NOT NULL,
                    
                    CONSTRAINT fk_search_conversions_search_analytics 
                        FOREIGN KEY (search_analytics_id) REFERENCES search_analytics(id) 
                        ON DELETE CASCADE ON UPDATE CASCADE,
                    CONSTRAINT fk_search_conversions_user_id 
                        FOREIGN KEY (user_id) REFERENCES users(id) 
                        ON DELETE SET NULL ON UPDATE CASCADE,
                    CONSTRAINT fk_search_conversions_product_id 
                        FOREIGN KEY (product_id) REFERENCES products(id) 
                        ON DELETE CASCADE ON UPDATE CASCADE,
                    
                    INDEX idx_search_conversions_search_analytics (search_analytics_id),
                    INDEX idx_search_conversions_product_id (product_id),
                    INDEX idx_search_conversions_user_id (user_id),
                    INDEX idx_search_conversions_type (conversion_type),
                    INDEX idx_search_conversions_timestamp (timestamp)
                )
                """
                connection.execute(text(create_conversions_table_sql))
                
                connection.commit()
                print("✅ Table structure fixed successfully!")
                
                return True
                
        except Exception as e:
            print(f"❌ Error fixing table structure: {e}")
            return False

def update_search_analytics_tracker():
    """Update the search analytics tracker to use the correct table structure"""
    print("\n🔄 UPDATING SEARCH ANALYTICS TRACKER")
    print("-" * 45)
    
    # Create a new compatible tracker model
    tracker_update_code = '''
# Updated SearchAnalyticsModel to be compatible with app.py model
class SearchAnalyticsModel(Base):
    """Database model for search analytics - compatible with app.py"""
    __tablename__ = 'search_analytics'

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, nullable=True)  # Foreign key to users.id
    guest_session_id = Column(String(100), nullable=True)
    search_query = Column(String(500), nullable=False)  # Renamed from 'query'
    search_type = Column(String(50), nullable=False, default='text')
    results_count = Column(Integer, nullable=False, default=0)
    filters_applied = Column(JSON, nullable=True)  # Renamed from 'filters'
    clicked_results = Column(JSON, nullable=True)
    session_id = Column(String(100), nullable=True)
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(String(500), nullable=True)
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    
    # Additional fields for advanced analytics (optional)
    response_time_ms = Column(Float, nullable=True)
    elasticsearch_time_ms = Column(Float, nullable=True)
    conversion_events = Column(JSON, nullable=True)
'''
    
    print("📝 Updated model definition:")
    print(tracker_update_code)
    
    print("\n💡 MANUAL UPDATE REQUIRED:")
    print("1. Update search_system/search_analytics_tracker.py")
    print("2. Replace SearchAnalyticsModel with the code above")
    print("3. Update track_search() method to use correct column names:")
    print("   - query → search_query")
    print("   - filters → filters_applied")
    print("   - timestamp → created_at")
    print("4. Update SearchClickModel and SearchConversionModel to reference search_analytics.id")

def verify_fix():
    """Verify that the fix was successful"""
    print("\n✅ VERIFYING FIX")
    print("-" * 20)
    
    with app.app_context():
        try:
            from sqlalchemy import inspect
            inspector = inspect(db.engine)
            
            # Check if table exists with correct structure
            if 'search_analytics' not in inspector.get_table_names():
                print("❌ search_analytics table not found!")
                return False
            
            columns = inspector.get_columns('search_analytics')
            foreign_keys = inspector.get_foreign_keys('search_analytics')
            indexes = inspector.get_indexes('search_analytics')
            
            print(f"✅ Table exists with {len(columns)} columns")
            print(f"✅ {len(foreign_keys)} foreign key(s) configured")
            print(f"✅ {len(indexes)} index(es) created")
            
            # Check for required columns
            column_names = [col['name'] for col in columns]
            required_columns = ['id', 'user_id', 'guest_session_id', 'search_query', 
                              'search_type', 'results_count', 'filters_applied', 
                              'clicked_results', 'session_id', 'created_at']
            
            missing_columns = set(required_columns) - set(column_names)
            if missing_columns:
                print(f"❌ Missing columns: {missing_columns}")
                return False
            
            print("✅ All required columns present")
            
            # Test table access
            with db.engine.connect() as connection:
                result = connection.execute(text("SELECT COUNT(*) FROM search_analytics"))
                row_count = result.fetchone()[0]
                print(f"✅ Table accessible, contains {row_count} rows")
            
            return True
            
        except Exception as e:
            print(f"❌ Verification failed: {e}")
            return False

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Fix search_analytics table issues')
    parser.add_argument('--backup', action='store_true', help='Create backup before fixing')
    parser.add_argument('--apply', action='store_true', help='Apply the fixes')
    
    args = parser.parse_args()
    
    print("🔧 SEARCH ANALYTICS TABLE FIX")
    print("=" * 50)
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if not args.apply:
        print("\n⚠️  DRY RUN MODE - Use --apply to make actual changes")
        print("\nIssues that will be fixed:")
        print("1. ✅ Resolve conflicting model definitions")
        print("2. ✅ Add missing columns (created_at, search_query, etc.)")
        print("3. ✅ Fix data type mismatches (INTEGER vs VARCHAR)")
        print("4. ✅ Add foreign key constraints")
        print("5. ✅ Add performance indexes")
        print("6. ✅ Create supporting tables (search_clicks, search_conversions)")
        print("\nRun with --apply to execute the fixes")
        return True
    
    success = True
    
    # Step 1: Backup if requested
    if args.backup:
        backup_name = backup_search_analytics_table()
        if backup_name is None and args.backup:
            print("⚠️  Backup failed, but continuing...")
    
    # Step 2: Fix table structure
    if not fix_search_analytics_structure():
        success = False
    
    # Step 3: Update tracker code (manual step)
    update_search_analytics_tracker()
    
    # Step 4: Verify fix
    if success:
        success = verify_fix()
    
    if success:
        print(f"\n🎉 SUCCESS: search_analytics table fixed!")
        print("\n📋 NEXT STEPS:")
        print("1. Update search_analytics_tracker.py with the new model definition")
        print("2. Test search functionality")
        print("3. Monitor for any remaining issues")
    else:
        print(f"\n❌ FAILED: Some issues remain")
    
    print(f"\n⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
