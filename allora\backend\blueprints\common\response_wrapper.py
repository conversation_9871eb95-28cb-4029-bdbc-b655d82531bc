"""
Response Wrapper System
=======================

Consistent response format wrapper for all API endpoints.
Implements standardized response structure across the entire API.
"""

from flask import jsonify
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

def success_response(
    data: Any = None,
    message: str = "Success",
    status_code: int = 200,
    meta: Optional[Dict] = None
) -> tuple:
    """
    Create a standardized success response.
    
    Args:
        data: Response data (can be dict, list, or any serializable type)
        message: Success message
        status_code: HTTP status code
        meta: Additional metadata (pagination, etc.)
    
    Returns:
        Tuple of (response, status_code)
    """
    response = {
        "success": True,
        "message": message,
        "data": data,
        "timestamp": datetime.utcnow().isoformat()
    }
    
    if meta:
        response["meta"] = meta
    
    return jsonify(response), status_code

def error_response(
    message: str,
    status_code: int = 400,
    error_code: Optional[str] = None,
    details: Optional[Dict] = None
) -> tuple:
    """
    Create a standardized error response.
    
    Args:
        message: Error message
        status_code: HTTP status code
        error_code: Application-specific error code
        details: Additional error details
    
    Returns:
        Tuple of (response, status_code)
    """
    response = {
        "success": False,
        "message": message,
        "timestamp": datetime.utcnow().isoformat()
    }
    
    if error_code:
        response["error_code"] = error_code
    
    if details:
        response["details"] = details
    
    # Log error for monitoring
    logger.error(f"API Error: {message} (Status: {status_code})", extra={
        "error_code": error_code,
        "details": details
    })
    
    return jsonify(response), status_code

def paginated_response(
    data: List[Any],
    page: int,
    per_page: int,
    total: int,
    message: str = "Success"
) -> tuple:
    """
    Create a standardized paginated response.
    
    Args:
        data: List of items for current page
        page: Current page number
        per_page: Items per page
        total: Total number of items
        message: Success message
    
    Returns:
        Tuple of (response, status_code)
    """
    total_pages = (total + per_page - 1) // per_page  # Ceiling division
    
    meta = {
        "pagination": {
            "page": page,
            "per_page": per_page,
            "total": total,
            "total_pages": total_pages,
            "has_next": page < total_pages,
            "has_prev": page > 1
        }
    }
    
    return success_response(
        data=data,
        message=message,
        meta=meta
    )

def validation_error_response(
    errors: Union[Dict, List],
    message: str = "Validation failed"
) -> tuple:
    """
    Create a standardized validation error response.
    
    Args:
        errors: Validation errors (from marshmallow, etc.)
        message: Error message
    
    Returns:
        Tuple of (response, status_code)
    """
    return error_response(
        message=message,
        status_code=422,
        error_code="VALIDATION_ERROR",
        details={"validation_errors": errors}
    )

def not_found_response(
    resource: str = "Resource",
    resource_id: Optional[Union[int, str]] = None
) -> tuple:
    """
    Create a standardized not found response.
    
    Args:
        resource: Name of the resource that wasn't found
        resource_id: ID of the resource that wasn't found
    
    Returns:
        Tuple of (response, status_code)
    """
    if resource_id:
        message = f"{resource} with ID '{resource_id}' not found"
    else:
        message = f"{resource} not found"
    
    return error_response(
        message=message,
        status_code=404,
        error_code="NOT_FOUND"
    )

def unauthorized_response(
    message: str = "Authentication required"
) -> tuple:
    """
    Create a standardized unauthorized response.
    
    Args:
        message: Error message
    
    Returns:
        Tuple of (response, status_code)
    """
    return error_response(
        message=message,
        status_code=401,
        error_code="UNAUTHORIZED"
    )

def forbidden_response(
    message: str = "Access forbidden"
) -> tuple:
    """
    Create a standardized forbidden response.
    
    Args:
        message: Error message
    
    Returns:
        Tuple of (response, status_code)
    """
    return error_response(
        message=message,
        status_code=403,
        error_code="FORBIDDEN"
    )

def rate_limit_response(
    message: str = "Rate limit exceeded"
) -> tuple:
    """
    Create a standardized rate limit response.
    
    Args:
        message: Error message
    
    Returns:
        Tuple of (response, status_code)
    """
    return error_response(
        message=message,
        status_code=429,
        error_code="RATE_LIMIT_EXCEEDED"
    )

def server_error_response(
    message: str = "Internal server error"
) -> tuple:
    """
    Create a standardized server error response.
    
    Args:
        message: Error message
    
    Returns:
        Tuple of (response, status_code)
    """
    return error_response(
        message=message,
        status_code=500,
        error_code="INTERNAL_SERVER_ERROR"
    )
