<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Allora Backend Features Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card i {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #667eea;
        }

        .stat-card h3 {
            font-size: 2rem;
            margin-bottom: 5px;
            color: #333;
        }

        .stat-card p {
            color: #666;
            font-size: 1rem;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .feature-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            position: relative;
        }

        .feature-header h3 {
            font-size: 1.5rem;
            margin-bottom: 5px;
        }

        .feature-header p {
            opacity: 0.9;
            font-size: 0.9rem;
        }

        .status-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-complete {
            background: #4CAF50;
            color: white;
        }

        .status-partial {
            background: #FF9800;
            color: white;
        }

        .status-planned {
            background: #9E9E9E;
            color: white;
        }

        .feature-content {
            padding: 25px;
        }

        .feature-list {
            list-style: none;
            margin-bottom: 20px;
        }

        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-list li i {
            color: #4CAF50;
            margin-right: 10px;
            width: 20px;
        }

        .api-endpoints {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }

        .api-endpoints h4 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1rem;
        }

        .endpoint {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
        }

        .method {
            padding: 2px 8px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            margin-right: 10px;
            min-width: 50px;
            text-align: center;
        }

        .method.get { background: #4CAF50; }
        .method.post { background: #2196F3; }
        .method.put { background: #FF9800; }
        .method.delete { background: #f44336; }

        .database-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .database-section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2rem;
            text-align: center;
        }

        .db-tables {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .table-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .table-card h4 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        .table-fields {
            font-size: 0.9rem;
            color: #666;
            line-height: 1.6;
        }

        .integration-guide {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .integration-guide h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2rem;
            text-align: center;
        }

        .code-example {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        .tech-stack {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .tech-stack h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2rem;
            text-align: center;
        }

        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .tech-item {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            transition: transform 0.3s ease;
        }

        .tech-item:hover {
            transform: translateY(-3px);
        }

        .tech-item i {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 10px;
        }

        .footer {
            text-align: center;
            color: white;
            padding: 30px;
            margin-top: 40px;
        }

        @media (max-width: 768px) {
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-rocket"></i> Allora Backend Features</h1>
            <p>Comprehensive E-commerce Platform with Advanced ML & AI Capabilities</p>
        </div>

        <!-- Statistics Overview -->
        <div class="stats-grid">
            <div class="stat-card">
                <i class="fas fa-code"></i>
                <h3>187+</h3>
                <p>API Endpoints</p>
            </div>
            <div class="stat-card">
                <i class="fas fa-database"></i>
                <h3>89</h3>
                <p>Database Models</p>
            </div>
            <div class="stat-card">
                <i class="fas fa-cogs"></i>
                <h3>15</h3>
                <p>Core Features</p>
            </div>
            <div class="stat-card">
                <i class="fas fa-shield-alt"></i>
                <h3>100%</h3>
                <p>Secure & Scalable</p>
            </div>
        </div>

        <!-- Technology Stack -->
        <div class="tech-stack">
            <h2><i class="fas fa-layer-group"></i> Technology Stack</h2>
            <div class="tech-grid">
                <div class="tech-item">
                    <i class="fab fa-python"></i>
                    <h4>Flask</h4>
                    <p>Web Framework</p>
                </div>
                <div class="tech-item">
                    <i class="fas fa-database"></i>
                    <h4>PostgreSQL</h4>
                    <p>Primary Database</p>
                </div>
                <div class="tech-item">
                    <i class="fas fa-search"></i>
                    <h4>Elasticsearch</h4>
                    <p>Search Engine</p>
                </div>
                <div class="tech-item">
                    <i class="fas fa-memory"></i>
                    <h4>Redis</h4>
                    <p>Caching & Sessions</p>
                </div>
                <div class="tech-item">
                    <i class="fas fa-brain"></i>
                    <h4>TensorFlow</h4>
                    <p>ML & AI</p>
                </div>
                <div class="tech-item">
                    <i class="fas fa-plug"></i>
                    <h4>SocketIO</h4>
                    <p>Real-time Features</p>
                </div>
            </div>
        </div>

        <!-- Core Features -->
        <div class="features-grid">
            <!-- Authentication & User Management -->
            <div class="feature-card">
                <div class="feature-header">
                    <h3><i class="fas fa-users"></i> Authentication & User Management</h3>
                    <p>Complete user lifecycle management</p>
                    <span class="status-badge status-complete">Complete</span>
                </div>
                <div class="feature-content">
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> JWT Authentication</li>
                        <li><i class="fas fa-check"></i> OAuth Integration (Google, Facebook, GitHub)</li>
                        <li><i class="fas fa-check"></i> Phone/OTP Authentication</li>
                        <li><i class="fas fa-check"></i> User Profiles & Preferences</li>
                        <li><i class="fas fa-check"></i> Role-based Access Control</li>
                    </ul>
                    <div class="api-endpoints">
                        <h4>Key Endpoints:</h4>
                        <div class="endpoint">
                            <span class="method post">POST</span>
                            <span>/api/signup</span>
                        </div>
                        <div class="endpoint">
                            <span class="method post">POST</span>
                            <span>/api/login</span>
                        </div>
                        <div class="endpoint">
                            <span class="method post">POST</span>
                            <span>/api/oauth/google</span>
                        </div>
                        <div class="endpoint">
                            <span class="method post">POST</span>
                            <span>/api/auth/send-otp</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Product Management -->
            <div class="feature-card">
                <div class="feature-header">
                    <h3><i class="fas fa-box"></i> Product Management</h3>
                    <p>Advanced product catalog with variants</p>
                    <span class="status-badge status-complete">Complete</span>
                </div>
                <div class="feature-content">
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Product CRUD Operations</li>
                        <li><i class="fas fa-check"></i> Product Variants (Size, Color)</li>
                        <li><i class="fas fa-check"></i> Image Gallery Management</li>
                        <li><i class="fas fa-check"></i> Inventory Tracking</li>
                        <li><i class="fas fa-check"></i> Sustainability Scoring</li>
                    </ul>
                    <div class="api-endpoints">
                        <h4>Key Endpoints:</h4>
                        <div class="endpoint">
                            <span class="method get">GET</span>
                            <span>/api/products</span>
                        </div>
                        <div class="endpoint">
                            <span class="method get">GET</span>
                            <span>/api/products/{id}</span>
                        </div>
                        <div class="endpoint">
                            <span class="method get">GET</span>
                            <span>/api/categories</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Advanced Search System -->
            <div class="feature-card">
                <div class="feature-header">
                    <h3><i class="fas fa-search"></i> Advanced Search System</h3>
                    <p>Elasticsearch-powered search with analytics</p>
                    <span class="status-badge status-complete">Complete</span>
                </div>
                <div class="feature-content">
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Full-text Search</li>
                        <li><i class="fas fa-check"></i> Faceted Search & Filters</li>
                        <li><i class="fas fa-check"></i> Autocomplete & Suggestions</li>
                        <li><i class="fas fa-check"></i> Visual Search (AI-powered)</li>
                        <li><i class="fas fa-check"></i> Search Analytics & Tracking</li>
                    </ul>
                    <div class="api-endpoints">
                        <h4>Key Endpoints:</h4>
                        <div class="endpoint">
                            <span class="method get">GET</span>
                            <span>/api/search</span>
                        </div>
                        <div class="endpoint">
                            <span class="method post">POST</span>
                            <span>/api/visual_search</span>
                        </div>
                        <div class="endpoint">
                            <span class="method get">GET</span>
                            <span>/api/search/suggestions</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Multi-vendor Marketplace -->
            <div class="feature-card">
                <div class="feature-header">
                    <h3><i class="fas fa-store"></i> Multi-vendor Marketplace</h3>
                    <p>Complete seller management system</p>
                    <span class="status-badge status-complete">Complete</span>
                </div>
                <div class="feature-content">
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Seller Registration & Verification</li>
                        <li><i class="fas fa-check"></i> Seller Dashboard & Analytics</li>
                        <li><i class="fas fa-check"></i> Commission Management</li>
                        <li><i class="fas fa-check"></i> Payout System</li>
                        <li><i class="fas fa-check"></i> Store Customization</li>
                    </ul>
                    <div class="api-endpoints">
                        <h4>Key Endpoints:</h4>
                        <div class="endpoint">
                            <span class="method post">POST</span>
                            <span>/api/seller/register</span>
                        </div>
                        <div class="endpoint">
                            <span class="method get">GET</span>
                            <span>/api/seller/dashboard</span>
                        </div>
                        <div class="endpoint">
                            <span class="method get">GET</span>
                            <span>/api/seller/earnings</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Management -->
            <div class="feature-card">
                <div class="feature-header">
                    <h3><i class="fas fa-shopping-cart"></i> Order Management</h3>
                    <p>Complete order lifecycle management</p>
                    <span class="status-badge status-complete">Complete</span>
                </div>
                <div class="feature-content">
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Order Creation & Processing</li>
                        <li><i class="fas fa-check"></i> Guest Checkout Support</li>
                        <li><i class="fas fa-check"></i> Order Status Tracking</li>
                        <li><i class="fas fa-check"></i> Multi-vendor Order Splitting</li>
                        <li><i class="fas fa-check"></i> Order Analytics</li>
                    </ul>
                    <div class="api-endpoints">
                        <h4>Key Endpoints:</h4>
                        <div class="endpoint">
                            <span class="method post">POST</span>
                            <span>/api/orders</span>
                        </div>
                        <div class="endpoint">
                            <span class="method get">GET</span>
                            <span>/api/orders/{id}</span>
                        </div>
                        <div class="endpoint">
                            <span class="method put">PUT</span>
                            <span>/api/admin/orders/{id}/status</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ML Recommendation System -->
            <div class="feature-card">
                <div class="feature-header">
                    <h3><i class="fas fa-brain"></i> ML Recommendation System</h3>
                    <p>AI-powered personalized recommendations</p>
                    <span class="status-badge status-complete">Complete</span>
                </div>
                <div class="feature-content">
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Collaborative Filtering</li>
                        <li><i class="fas fa-check"></i> Content-based Filtering</li>
                        <li><i class="fas fa-check"></i> User Behavior Tracking</li>
                        <li><i class="fas fa-check"></i> Real-time Personalization</li>
                        <li><i class="fas fa-check"></i> A/B Testing Framework</li>
                    </ul>
                    <div class="api-endpoints">
                        <h4>Key Endpoints:</h4>
                        <div class="endpoint">
                            <span class="method get">GET</span>
                            <span>/api/recommendations/personalized</span>
                        </div>
                        <div class="endpoint">
                            <span class="method get">GET</span>
                            <span>/api/recommendations/similar/{id}</span>
                        </div>
                        <div class="endpoint">
                            <span class="method get">GET</span>
                            <span>/api/recommendations/trending</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Fulfillment -->
            <div class="feature-card">
                <div class="feature-header">
                    <h3><i class="fas fa-truck"></i> Order Fulfillment</h3>
                    <p>Advanced shipping & logistics management</p>
                    <span class="status-badge status-complete">Complete</span>
                </div>
                <div class="feature-content">
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Multi-carrier Integration</li>
                        <li><i class="fas fa-check"></i> Rate Calculation Engine</li>
                        <li><i class="fas fa-check"></i> Shipment Tracking</li>
                        <li><i class="fas fa-check"></i> Pickup Scheduling</li>
                        <li><i class="fas fa-check"></i> Delivery Analytics</li>
                    </ul>
                    <div class="api-endpoints">
                        <h4>Key Endpoints:</h4>
                        <div class="endpoint">
                            <span class="method post">POST</span>
                            <span>/api/fulfillment/rates</span>
                        </div>
                        <div class="endpoint">
                            <span class="method post">POST</span>
                            <span>/api/fulfillment/shipments</span>
                        </div>
                        <div class="endpoint">
                            <span class="method get">GET</span>
                            <span>/api/tracking/metrics</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- RMA System -->
            <div class="feature-card">
                <div class="feature-header">
                    <h3><i class="fas fa-undo"></i> RMA System</h3>
                    <p>Return Merchandise Authorization</p>
                    <span class="status-badge status-complete">Complete</span>
                </div>
                <div class="feature-content">
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Return Request Management</li>
                        <li><i class="fas fa-check"></i> Approval Workflow</li>
                        <li><i class="fas fa-check"></i> Return Shipping Labels</li>
                        <li><i class="fas fa-check"></i> Refund Processing</li>
                        <li><i class="fas fa-check"></i> RMA Analytics</li>
                    </ul>
                    <div class="api-endpoints">
                        <h4>Key Endpoints:</h4>
                        <div class="endpoint">
                            <span class="method post">POST</span>
                            <span>/api/rma/create</span>
                        </div>
                        <div class="endpoint">
                            <span class="method get">GET</span>
                            <span>/api/rma/{rma_number}</span>
                        </div>
                        <div class="endpoint">
                            <span class="method put">PUT</span>
                            <span>/api/rma/admin/{rma_number}/approve</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Database Architecture -->
        <div class="database-section">
            <h2><i class="fas fa-database"></i> Database Architecture</h2>
            <p style="text-align: center; margin-bottom: 30px; color: #666;">
                Comprehensive database schema with 89 models supporting all business operations
            </p>
            <div class="db-tables">
                <div class="table-card">
                    <h4>User Management</h4>
                    <div class="table-fields">
                        <strong>User:</strong> id, username, email, password, profile_picture, preferences<br>
                        <strong>UserOAuth:</strong> provider_id, provider_user_id, access_token<br>
                        <strong>UserAddress:</strong> address_type, full_name, city, state, postal_code<br>
                        <strong>UserSession:</strong> session_id, start_time, page_views, duration
                    </div>
                </div>

                <div class="table-card">
                    <h4>Product Catalog</h4>
                    <div class="table-fields">
                        <strong>Product:</strong> name, price, image, sustainability_score, stock_quantity<br>
                        <strong>Category:</strong> name, slug, parent_id, level, is_featured<br>
                        <strong>ProductVariant:</strong> variant_type, variant_value, price_adjustment<br>
                        <strong>ProductImage:</strong> image_url, alt_text, is_primary, display_order
                    </div>
                </div>

                <div class="table-card">
                    <h4>Order System</h4>
                    <div class="table-fields">
                        <strong>Order:</strong> order_number, status, total_amount, shipping_address<br>
                        <strong>OrderItem:</strong> product_id, quantity, unit_price, total_price<br>
                        <strong>CartItem:</strong> user_id, product_id, quantity, guest_session_id<br>
                        <strong>Invoice:</strong> invoice_number, billing_address, tax_amount
                    </div>
                </div>

                <div class="table-card">
                    <h4>Seller Management</h4>
                    <div class="table-fields">
                        <strong>Seller:</strong> business_name, email, verification_status, commission_rate<br>
                        <strong>SellerStore:</strong> store_name, store_slug, store_description, store_logo<br>
                        <strong>SellerCommission:</strong> order_amount, commission_rate, seller_earnings<br>
                        <strong>SellerPayout:</strong> amount, payout_method, bank_account, status
                    </div>
                </div>

                <div class="table-card">
                    <h4>Shipping & Fulfillment</h4>
                    <div class="table-fields">
                        <strong>Shipment:</strong> tracking_number, carrier_id, status, weight_kg<br>
                        <strong>ShippingCarrier:</strong> name, code, api_endpoint, supported_services<br>
                        <strong>TrackingEvent:</strong> status, description, location, timestamp<br>
                        <strong>FulfillmentRule:</strong> conditions, actions, priority, is_active
                    </div>
                </div>

                <div class="table-card">
                    <h4>RMA System</h4>
                    <div class="table-fields">
                        <strong>RMARequest:</strong> rma_number, rma_type, status, customer_reason<br>
                        <strong>RMAItem:</strong> product_id, quantity, return_reason, condition<br>
                        <strong>RMATimeline:</strong> event_type, event_description, actor_type<br>
                        <strong>ReturnShipment:</strong> tracking_number, carrier, origin_address
                    </div>
                </div>

                <div class="table-card">
                    <h4>ML & Analytics</h4>
                    <div class="table-fields">
                        <strong>UserInteractionLog:</strong> interaction_type, product_id, session_id<br>
                        <strong>UserBehaviorProfile:</strong> category_preferences, engagement_score<br>
                        <strong>SearchAnalytics:</strong> search_query, results_count, response_time_ms<br>
                        <strong>SearchClick:</strong> product_id, position, timestamp, session_id
                    </div>
                </div>

                <div class="table-card">
                    <h4>Community Features</h4>
                    <div class="table-fields">
                        <strong>CommunityPost:</strong> content, post_type, likes_count, comments_count<br>
                        <strong>PostComment:</strong> content, parent_comment_id, created_at<br>
                        <strong>PostLike:</strong> post_id, user_id, created_at<br>
                        <strong>Hashtag:</strong> tag, usage_count, created_at
                    </div>
                </div>

                <div class="table-card">
                    <h4>Payment System</h4>
                    <div class="table-fields">
                        <strong>PaymentTransaction:</strong> transaction_id, gateway_id, amount, status<br>
                        <strong>PaymentGateway:</strong> name, api_endpoint, supported_currencies<br>
                        <strong>Refund:</strong> refund_id, refund_type, refund_amount, status<br>
                        <strong>PaymentMethod:</strong> payment_type, card_last_four, is_default
                    </div>
                </div>
            </div>
        </div>

        <!-- Frontend Integration Guide -->
        <div class="integration-guide">
            <h2><i class="fas fa-code"></i> Frontend Integration Guide</h2>
            <p style="text-align: center; margin-bottom: 30px; color: #666;">
                How to integrate these backend features with your frontend application
            </p>

            <h3><i class="fas fa-user-shield"></i> Authentication Integration</h3>
            <div class="code-example">
// User Registration
const registerUser = async (userData) => {
    const response = await fetch('/api/signup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(userData)
    });
    return response.json();
};

// OAuth Login
const googleLogin = async () => {
    window.location.href = '/api/oauth/google/authorize';
};

// JWT Token Management
const setAuthToken = (token) => {
    localStorage.setItem('authToken', token);
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
};
            </div>

            <h3><i class="fas fa-search"></i> Search Integration</h3>
            <div class="code-example">
// Advanced Search with Filters
const searchProducts = async (query, filters = {}) => {
    const params = new URLSearchParams({
        q: query,
        ...filters,
        page: 1,
        per_page: 20
    });

    const response = await fetch(`/api/search?${params}`);
    return response.json();
};

// Autocomplete Suggestions
const getSearchSuggestions = async (query) => {
    const response = await fetch(`/api/search/suggestions?q=${query}`);
    return response.json();
};

// Visual Search
const visualSearch = async (imageFile) => {
    const formData = new FormData();
    formData.append('image', imageFile);

    const response = await fetch('/api/visual_search', {
        method: 'POST',
        body: formData
    });
    return response.json();
};
            </div>

            <h3><i class="fas fa-shopping-cart"></i> E-commerce Integration</h3>
            <div class="code-example">
// Add to Cart
const addToCart = async (productId, quantity = 1) => {
    const response = await fetch('/api/cart/add', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ product_id: productId, quantity })
    });
    return response.json();
};

// Create Order
const createOrder = async (orderData) => {
    const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify(orderData)
    });
    return response.json();
};

// Get Personalized Recommendations
const getRecommendations = async (userId) => {
    const response = await fetch(`/api/recommendations/personalized?user_id=${userId}`, {
        headers: { 'Authorization': `Bearer ${localStorage.getItem('authToken')}` }
    });
    return response.json();
};
            </div>
        </div>

        <!-- Additional Features Grid -->
        <div class="features-grid">
            <!-- Sustainability Features -->
            <div class="feature-card">
                <div class="feature-header">
                    <h3><i class="fas fa-leaf"></i> Sustainability Features</h3>
                    <p>Environmental impact tracking</p>
                    <span class="status-badge status-complete">Complete</span>
                </div>
                <div class="feature-content">
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Carbon Footprint Calculation</li>
                        <li><i class="fas fa-check"></i> Sustainability Scoring</li>
                        <li><i class="fas fa-check"></i> Green Product Recommendations</li>
                        <li><i class="fas fa-check"></i> Impact Analytics</li>
                        <li><i class="fas fa-check"></i> Eco-friendly Badges</li>
                    </ul>
                    <div class="api-endpoints">
                        <h4>Key Endpoints:</h4>
                        <div class="endpoint">
                            <span class="method get">GET</span>
                            <span>/api/sustainability/metrics</span>
                        </div>
                        <div class="endpoint">
                            <span class="method get">GET</span>
                            <span>/api/sustainability/green-heroes</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Community Features -->
            <div class="feature-card">
                <div class="feature-header">
                    <h3><i class="fas fa-users-cog"></i> Community Features</h3>
                    <p>Social commerce capabilities</p>
                    <span class="status-badge status-complete">Complete</span>
                </div>
                <div class="feature-content">
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Community Posts & Stories</li>
                        <li><i class="fas fa-check"></i> Product Reviews & Ratings</li>
                        <li><i class="fas fa-check"></i> Hashtag System</li>
                        <li><i class="fas fa-check"></i> User-generated Content</li>
                        <li><i class="fas fa-check"></i> Community Analytics</li>
                    </ul>
                    <div class="api-endpoints">
                        <h4>Key Endpoints:</h4>
                        <div class="endpoint">
                            <span class="method get">GET</span>
                            <span>/api/community_posts</span>
                        </div>
                        <div class="endpoint">
                            <span class="method post">POST</span>
                            <span>/api/community_posts</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Inventory Management -->
            <div class="feature-card">
                <div class="feature-header">
                    <h3><i class="fas fa-warehouse"></i> Inventory Management</h3>
                    <p>Multi-channel inventory synchronization</p>
                    <span class="status-badge status-complete">Complete</span>
                </div>
                <div class="feature-content">
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Real-time Inventory Sync</li>
                        <li><i class="fas fa-check"></i> Multi-channel Integration</li>
                        <li><i class="fas fa-check"></i> Stock Alerts & Notifications</li>
                        <li><i class="fas fa-check"></i> Inventory Prediction (ML)</li>
                        <li><i class="fas fa-check"></i> Conflict Resolution</li>
                    </ul>
                    <div class="api-endpoints">
                        <h4>Key Endpoints:</h4>
                        <div class="endpoint">
                            <span class="method post">POST</span>
                            <span>/api/inventory/update</span>
                        </div>
                        <div class="endpoint">
                            <span class="method get">GET</span>
                            <span>/api/admin/inventory</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Admin Dashboard -->
            <div class="feature-card">
                <div class="feature-header">
                    <h3><i class="fas fa-tachometer-alt"></i> Admin Dashboard</h3>
                    <p>Comprehensive admin management</p>
                    <span class="status-badge status-complete">Complete</span>
                </div>
                <div class="feature-content">
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> User Management</li>
                        <li><i class="fas fa-check"></i> Seller Approval & Management</li>
                        <li><i class="fas fa-check"></i> Order Management</li>
                        <li><i class="fas fa-check"></i> Analytics & Reports</li>
                        <li><i class="fas fa-check"></i> System Configuration</li>
                    </ul>
                    <div class="api-endpoints">
                        <h4>Key Endpoints:</h4>
                        <div class="endpoint">
                            <span class="method get">GET</span>
                            <span>/api/admin/users</span>
                        </div>
                        <div class="endpoint">
                            <span class="method get">GET</span>
                            <span>/api/admin/sellers</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Management -->
            <div class="feature-card">
                <div class="feature-header">
                    <h3><i class="fas fa-file-alt"></i> Content Management</h3>
                    <p>Dynamic content management system</p>
                    <span class="status-badge status-complete">Complete</span>
                </div>
                <div class="feature-content">
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Page Content Management</li>
                        <li><i class="fas fa-check"></i> SEO Optimization</li>
                        <li><i class="fas fa-check"></i> Dynamic Sitemap</li>
                        <li><i class="fas fa-check"></i> Newsletter Management</li>
                        <li><i class="fas fa-check"></i> Email Templates</li>
                    </ul>
                    <div class="api-endpoints">
                        <h4>Key Endpoints:</h4>
                        <div class="endpoint">
                            <span class="method get">GET</span>
                            <span>/api/content</span>
                        </div>
                        <div class="endpoint">
                            <span class="method post">POST</span>
                            <span>/api/admin/content</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security & Compliance -->
            <div class="feature-card">
                <div class="feature-header">
                    <h3><i class="fas fa-shield-alt"></i> Security & Compliance</h3>
                    <p>GDPR, CCPA compliance & security</p>
                    <span class="status-badge status-complete">Complete</span>
                </div>
                <div class="feature-content">
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Cookie Consent Management</li>
                        <li><i class="fas fa-check"></i> Data Export (GDPR)</li>
                        <li><i class="fas fa-check"></i> Audit Logging</li>
                        <li><i class="fas fa-check"></i> Rate Limiting</li>
                        <li><i class="fas fa-check"></i> Input Validation</li>
                    </ul>
                    <div class="api-endpoints">
                        <h4>Key Endpoints:</h4>
                        <div class="endpoint">
                            <span class="method get">GET</span>
                            <span>/api/cookie-consent</span>
                        </div>
                        <div class="endpoint">
                            <span class="method post">POST</span>
                            <span>/api/data-export</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Implementation Status Summary -->
        <div class="database-section">
            <h2><i class="fas fa-chart-line"></i> Implementation Status</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <i class="fas fa-check-circle" style="color: #4CAF50;"></i>
                    <h3>15/15</h3>
                    <p>Core Features Complete</p>
                </div>
                <div class="stat-card">
                    <i class="fas fa-database" style="color: #2196F3;"></i>
                    <h3>89/89</h3>
                    <p>Database Models</p>
                </div>
                <div class="stat-card">
                    <i class="fas fa-code" style="color: #FF9800;"></i>
                    <h3>187+</h3>
                    <p>API Endpoints</p>
                </div>
                <div class="stat-card">
                    <i class="fas fa-rocket" style="color: #9C27B0;"></i>
                    <h3>100%</h3>
                    <p>Production Ready</p>
                </div>
            </div>
        </div>

        <div class="footer">
            <h3>🚀 Ready for Production</h3>
            <p>The Allora backend is a comprehensive, scalable, and feature-rich e-commerce platform</p>
            <p>Built with modern technologies and best practices for enterprise-grade applications</p>
            <p style="margin-top: 20px; opacity: 0.8;">
                <i class="fas fa-calendar"></i> Last Updated: July 2025 |
                <i class="fas fa-code-branch"></i> Version: 2.0.0 |
                <i class="fas fa-users"></i> Allora Development Team
            </p>
        </div>
    </div>

    <script>
        // Add some interactive functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Animate cards on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // Observe all cards
            document.querySelectorAll('.feature-card, .stat-card, .table-card').forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });

            // Add click handlers for endpoints
            document.querySelectorAll('.endpoint').forEach(endpoint => {
                endpoint.addEventListener('click', function() {
                    const method = this.querySelector('.method').textContent;
                    const path = this.querySelector('span:last-child').textContent;
                    console.log(`API Endpoint: ${method} ${path}`);

                    // You could add functionality to copy to clipboard or show more details
                    navigator.clipboard.writeText(`${method} ${path}`).then(() => {
                        // Show a brief success message
                        const originalText = this.style.backgroundColor;
                        this.style.backgroundColor = '#4CAF50';
                        this.style.color = 'white';
                        setTimeout(() => {
                            this.style.backgroundColor = originalText;
                            this.style.color = '';
                        }, 1000);
                    });
                });
            });
        });
    </script>
</body>
</html>
