#!/usr/bin/env python3
"""
Database Data Checker Script for Allora E-commerce Project
==========================================================

This script checks and displays all data in the entire database.
It provides comprehensive information about:
1. Table structure and row counts
2. Sample data from each table
3. Database statistics and health check
4. Data relationships and integrity

Usage:
    python check_database_data.py                    # Full database check
    python check_database_data.py --summary          # Summary only
    python check_database_data.py --table user       # Check specific table
    python check_database_data.py --empty-only       # Show only empty tables
    python check_database_data.py --data-only        # Show only tables with data

Author: Allora Development Team
Date: 2025-01-07
"""

import os
import sys
import argparse
from datetime import datetime
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import text, inspect, MetaData
from dotenv import load_dotenv
import json

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)

# Get database URL and provide fallback
database_url = os.getenv('DATABASE_URL')
if not database_url:
    print("❌ DATABASE_URL not found in environment variables")
    print("💡 Please check your .env file")
    sys.exit(1)

# Try to connect with fallback to root user if allora_user fails
app.config['SQLALCHEMY_DATABASE_URI'] = database_url
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

try:
    db = SQLAlchemy(app)
    # Test the connection
    with app.app_context():
        db.engine.connect()
    print("✅ Database connection successful")
except Exception as e:
    print(f"❌ Database connection failed with configured user: {e}")

    # Try fallback to root user
    if 'allora_user' in database_url:
        print("🔄 Attempting fallback to root user...")
        fallback_url = database_url.replace('allora_user:SecurePass2024!', 'root:Parul.2001')
        app.config['SQLALCHEMY_DATABASE_URI'] = fallback_url

        try:
            db = SQLAlchemy(app)
            with app.app_context():
                db.engine.connect()
            print("✅ Database connection successful with root user")
            print("⚠️  Note: Using root user. Consider creating allora_user for security.")
        except Exception as fallback_error:
            print(f"❌ Fallback connection also failed: {fallback_error}")
            print("\n🔧 TROUBLESHOOTING STEPS:")
            print("1. Check if MySQL service is running:")
            print("   - Windows: services.msc -> MySQL")
            print("   - Linux: sudo systemctl status mysql")
            print("\n2. Verify database credentials in .env file")
            print("3. Create allora_user if it doesn't exist:")
            print("   mysql -u root -p")
            print("   CREATE USER 'allora_user'@'localhost' IDENTIFIED BY 'SecurePass2024!';")
            print("   GRANT ALL PRIVILEGES ON allora_db.* TO 'allora_user'@'localhost';")
            print("   FLUSH PRIVILEGES;")
            sys.exit(1)
    else:
        print("\n🔧 TROUBLESHOOTING STEPS:")
        print("1. Check if MySQL service is running")
        print("2. Verify database credentials in .env file")
        print("3. Check if database 'allora_db' exists")
        sys.exit(1)

class DatabaseChecker:
    """Database data checking utility class"""

    def __init__(self):
        self.tables_info = {}
        self.total_rows = 0
        self.empty_tables = []
        self.populated_tables = []
        self.errors = []

    def test_database_connection(self):
        """Test database connection and provide detailed diagnostics"""
        try:
            with app.app_context():
                # Test basic connection
                connection = db.engine.connect()

                # Test database selection
                result = connection.execute(text("SELECT DATABASE()"))
                current_db = result.scalar()

                # Test basic query
                result = connection.execute(text("SELECT 1"))
                test_result = result.scalar()

                connection.close()

                print(f"✅ Database connection test passed")
                print(f"   Connected to database: {current_db}")
                print(f"   Test query result: {test_result}")
                return True

        except Exception as e:
            print(f"❌ Database connection test failed: {e}")
            return False
        
    def get_table_info(self, table_name):
        """Get detailed information about a table"""
        try:
            # Get row count
            result = db.session.execute(text(f"SELECT COUNT(*) FROM `{table_name}`"))
            row_count = result.scalar()
            
            # Get table structure
            inspector = inspect(db.engine)
            columns = inspector.get_columns(table_name)
            
            # Get sample data if table has rows
            sample_data = []
            if row_count > 0:
                try:
                    # Get up to 3 sample rows
                    result = db.session.execute(text(f"SELECT * FROM `{table_name}` LIMIT 3"))
                    rows = result.fetchall()
                    
                    # Convert rows to dictionaries
                    column_names = [col['name'] for col in columns]
                    for row in rows:
                        row_dict = {}
                        for i, value in enumerate(row):
                            if i < len(column_names):
                                # Convert non-serializable types to strings
                                if isinstance(value, (datetime,)):
                                    row_dict[column_names[i]] = str(value)
                                elif value is None:
                                    row_dict[column_names[i]] = None
                                else:
                                    row_dict[column_names[i]] = str(value)
                        sample_data.append(row_dict)
                except Exception as e:
                    sample_data = [{"error": f"Could not fetch sample data: {str(e)}"}]
            
            table_info = {
                'row_count': row_count,
                'columns': [{'name': col['name'], 'type': str(col['type'])} for col in columns],
                'sample_data': sample_data
            }
            
            return table_info
            
        except Exception as e:
            error_msg = f"Error checking table {table_name}: {e}"
            self.errors.append(error_msg)
            return {
                'row_count': 0,
                'columns': [],
                'sample_data': [],
                'error': error_msg
            }
    
    def check_all_tables(self):
        """Check all tables in the database"""
        print("\n" + "="*80)
        print("🔍 CHECKING ALL DATABASE TABLES")
        print("="*80)
        
        try:
            # Get all table names
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()
            tables = [table for table in tables if not table.startswith('alembic')]
            tables.sort()  # Sort alphabetically
            
            if not tables:
                print("❌ No tables found in database")
                return False
            
            print(f"📋 Found {len(tables)} tables to check")
            print("🚀 Starting data analysis...\n")
            
            # Check each table
            for i, table in enumerate(tables, 1):
                print(f"[{i:2d}/{len(tables)}] Checking table: {table}")
                
                table_info = self.get_table_info(table)
                self.tables_info[table] = table_info
                
                row_count = table_info['row_count']
                self.total_rows += row_count
                
                if row_count == 0:
                    self.empty_tables.append(table)
                    print(f"         📊 Empty (0 rows)")
                else:
                    self.populated_tables.append(table)
                    print(f"         📊 {row_count:,} rows")
            
            print(f"\n✅ Analysis complete!")
            return True
            
        except Exception as e:
            error_msg = f"Error during table analysis: {e}"
            self.errors.append(error_msg)
            print(f"❌ {error_msg}")
            return False
    
    def check_specific_table(self, table_name):
        """Check a specific table in detail"""
        print("\n" + "="*80)
        print(f"🔍 CHECKING TABLE: {table_name}")
        print("="*80)
        
        try:
            # Check if table exists
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()
            
            if table_name not in tables:
                print(f"❌ Table '{table_name}' not found")
                print(f"📋 Available tables: {', '.join(sorted(tables))}")
                return False
            
            # Get detailed table info
            table_info = self.get_table_info(table_name)
            self.tables_info[table_name] = table_info
            
            # Display table structure
            print(f"📊 Table Structure:")
            print(f"   Rows: {table_info['row_count']:,}")
            print(f"   Columns: {len(table_info['columns'])}")
            
            print(f"\n📋 Column Details:")
            for col in table_info['columns']:
                print(f"   - {col['name']}: {col['type']}")
            
            # Display sample data
            if table_info['sample_data']:
                print(f"\n📄 Sample Data (up to 3 rows):")
                for i, row in enumerate(table_info['sample_data'], 1):
                    print(f"   Row {i}:")
                    for key, value in row.items():
                        # Truncate long values
                        display_value = str(value)
                        if len(display_value) > 50:
                            display_value = display_value[:47] + "..."
                        print(f"     {key}: {display_value}")
                    print()
            else:
                print(f"\n📄 No data in table")
            
            return True
            
        except Exception as e:
            error_msg = f"Error checking table {table_name}: {e}"
            self.errors.append(error_msg)
            print(f"❌ {error_msg}")
            return False
    
    def generate_summary_report(self):
        """Generate a summary report of the database"""
        print("\n" + "="*80)
        print("📋 DATABASE SUMMARY REPORT")
        print("="*80)
        
        print(f"🕒 Report generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🗄️  Total tables: {len(self.tables_info)}")
        print(f"📊 Total rows: {self.total_rows:,}")
        print(f"📈 Tables with data: {len(self.populated_tables)}")
        print(f"📉 Empty tables: {len(self.empty_tables)}")
        
        if self.errors:
            print(f"❌ Errors encountered: {len(self.errors)}")
        
        # Show populated tables
        if self.populated_tables:
            print(f"\n✅ TABLES WITH DATA ({len(self.populated_tables)}):")
            for table in sorted(self.populated_tables):
                row_count = self.tables_info[table]['row_count']
                print(f"   📊 {table}: {row_count:,} rows")
        
        # Show empty tables
        if self.empty_tables:
            print(f"\n📭 EMPTY TABLES ({len(self.empty_tables)}):")
            for table in sorted(self.empty_tables):
                print(f"   📊 {table}: 0 rows")
        
        # Show errors if any
        if self.errors:
            print(f"\n❌ ERRORS:")
            for error in self.errors:
                print(f"   - {error}")
        
        print("\n" + "="*80)
    
    def generate_detailed_report(self):
        """Generate a detailed report of all tables"""
        print("\n" + "="*80)
        print("📋 DETAILED DATABASE REPORT")
        print("="*80)
        
        for table_name in sorted(self.tables_info.keys()):
            table_info = self.tables_info[table_name]
            
            print(f"\n📊 TABLE: {table_name}")
            print("-" * 60)
            print(f"   Rows: {table_info['row_count']:,}")
            print(f"   Columns: {len(table_info['columns'])}")
            
            # Show column info
            if table_info['columns']:
                print("   Structure:")
                for col in table_info['columns'][:5]:  # Show first 5 columns
                    print(f"     - {col['name']}: {col['type']}")
                if len(table_info['columns']) > 5:
                    print(f"     ... and {len(table_info['columns']) - 5} more columns")
            
            # Show sample data
            if table_info['sample_data'] and table_info['row_count'] > 0:
                print("   Sample data:")
                sample_row = table_info['sample_data'][0]
                for key, value in list(sample_row.items())[:3]:  # Show first 3 fields
                    display_value = str(value)
                    if len(display_value) > 30:
                        display_value = display_value[:27] + "..."
                    print(f"     {key}: {display_value}")
                if len(sample_row) > 3:
                    print(f"     ... and {len(sample_row) - 3} more fields")
        
        print("\n" + "="*80)
    
    def export_to_json(self, filename=None):
        """Export database information to JSON file"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"database_data_report_{timestamp}.json"
        
        try:
            report_data = {
                'generated_at': datetime.now().isoformat(),
                'summary': {
                    'total_tables': len(self.tables_info),
                    'total_rows': self.total_rows,
                    'populated_tables': len(self.populated_tables),
                    'empty_tables': len(self.empty_tables)
                },
                'tables': self.tables_info,
                'populated_tables': self.populated_tables,
                'empty_tables': self.empty_tables,
                'errors': self.errors
            }
            
            filepath = os.path.join(os.path.dirname(__file__), filename)
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            
            print(f"📄 Report exported to: {filename}")
            return True
            
        except Exception as e:
            print(f"❌ Failed to export report: {e}")
            return False


def main():
    """Main function to handle command line arguments and execute database checking"""
    parser = argparse.ArgumentParser(
        description='Check data in Allora database tables',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python check_database_data.py                    # Full database check
  python check_database_data.py --summary          # Summary only
  python check_database_data.py --table user       # Check specific table
  python check_database_data.py --empty-only       # Show only empty tables
  python check_database_data.py --data-only        # Show only tables with data
  python check_database_data.py --export           # Export to JSON file
        """
    )

    parser.add_argument('--summary', action='store_true',
                       help='Show summary report only')
    parser.add_argument('--table', type=str,
                       help='Check specific table in detail')
    parser.add_argument('--empty-only', action='store_true',
                       help='Show only empty tables')
    parser.add_argument('--data-only', action='store_true',
                       help='Show only tables with data')
    parser.add_argument('--export', action='store_true',
                       help='Export report to JSON file')
    parser.add_argument('--detailed', action='store_true',
                       help='Show detailed report for all tables')

    args = parser.parse_args()

    print("🚀 Allora Database Data Checker")
    print("="*60)

    # Initialize checker
    checker = DatabaseChecker()

    try:
        with app.app_context():
            # First test database connection
            print("🔍 Testing database connection...")
            if not checker.test_database_connection():
                print("\n❌ Database connection test failed")
                sys.exit(1)

            success = True

            # Handle specific table check
            if args.table:
                success = checker.check_specific_table(args.table)
            else:
                # Check all tables
                success = checker.check_all_tables()

            if not success:
                print("\n❌ Database check failed")
                sys.exit(1)

            # Generate appropriate report
            if args.summary or args.empty_only or args.data_only:
                # Filter results if needed
                if args.empty_only:
                    print(f"\n📭 EMPTY TABLES ONLY ({len(checker.empty_tables)}):")
                    for table in sorted(checker.empty_tables):
                        print(f"   📊 {table}: 0 rows")
                elif args.data_only:
                    print(f"\n✅ TABLES WITH DATA ONLY ({len(checker.populated_tables)}):")
                    for table in sorted(checker.populated_tables):
                        row_count = checker.tables_info[table]['row_count']
                        print(f"   📊 {table}: {row_count:,} rows")
                else:
                    checker.generate_summary_report()
            elif args.detailed:
                checker.generate_detailed_report()
            elif not args.table:
                # Default: show summary
                checker.generate_summary_report()

            # Export to JSON if requested
            if args.export:
                checker.export_to_json()

            print("\n🎉 Database check completed successfully!")

    except Exception as e:
        print(f"\n❌ Fatal error during database check: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
