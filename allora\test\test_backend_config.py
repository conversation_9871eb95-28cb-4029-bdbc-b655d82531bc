#!/usr/bin/env python3
"""
Backend Configuration Test Script
=================================
Tests all backend configurations and dependencies
"""

import os
import sys
import importlib.util

def test_environment_variables():
    """Test critical environment variables"""
    print("🔍 Testing Environment Variables...")
    
    required_vars = [
        'DATABASE_URL',
        'SECRET_KEY', 
        'JWT_SECRET_KEY',
        'FLASK_ENV'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
        else:
            print(f"✅ {var}: {'*' * 10}")  # Hide actual values
    
    if missing_vars:
        print(f"❌ Missing environment variables: {missing_vars}")
        return False
    
    print("✅ All critical environment variables present")
    return True

def test_database_connection():
    """Test database connection"""
    print("\n🔍 Testing Database Connection...")
    
    try:
        from app import app, db
        with app.app_context():
            # Test connection
            result = db.engine.execute('SELECT 1').fetchone()
            if result:
                print("✅ Database connection successful")
                return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def test_model_imports():
    """Test model imports"""
    print("\n🔍 Testing Model Imports...")
    
    try:
        from app import (
            User, Product, Order, OrderItem, CartItem, 
            Wishlist, Seller, AdminUser, PaymentTransaction
        )
        print("✅ Core models imported successfully")
        return True
    except Exception as e:
        print(f"❌ Model import failed: {e}")
        return False

def test_database_tables():
    """Test database table creation"""
    print("\n🔍 Testing Database Tables...")
    
    try:
        from app import app, db
        with app.app_context():
            db.create_all()
            print("✅ Database tables created/verified")
            return True
    except Exception as e:
        print(f"❌ Database table creation failed: {e}")
        return False

def test_basic_queries():
    """Test basic database queries"""
    print("\n🔍 Testing Basic Database Queries...")
    
    try:
        from app import app, db, User, Product
        with app.app_context():
            user_count = User.query.count()
            product_count = Product.query.count()
            print(f"✅ Database queries working - Users: {user_count}, Products: {product_count}")
            return True
    except Exception as e:
        print(f"❌ Database queries failed: {e}")
        return False

def test_dependencies():
    """Test critical dependencies"""
    print("\n🔍 Testing Dependencies...")
    
    critical_deps = [
        'flask',
        'flask_sqlalchemy', 
        'flask_cors',
        'flask_bcrypt',
        'flask_jwt_extended',
        'mysql.connector',
        'redis',
        'elasticsearch'
    ]
    
    missing_deps = []
    for dep in critical_deps:
        try:
            if dep == 'mysql.connector':
                import mysql.connector
            elif dep == 'flask_sqlalchemy':
                import flask_sqlalchemy
            elif dep == 'flask_cors':
                import flask_cors
            elif dep == 'flask_bcrypt':
                import flask_bcrypt
            elif dep == 'flask_jwt_extended':
                import flask_jwt_extended
            else:
                __import__(dep)
            print(f"✅ {dep}")
        except ImportError:
            missing_deps.append(dep)
            print(f"❌ {dep}")
    
    if missing_deps:
        print(f"❌ Missing dependencies: {missing_deps}")
        return False
    
    print("✅ All critical dependencies available")
    return True

def test_file_structure():
    """Test critical file structure"""
    print("\n🔍 Testing File Structure...")
    
    critical_files = [
        'app.py',
        'config.py',
        'requirements.txt',
        'run_with_waitress.py',
        '.env'
    ]
    
    missing_files = []
    for file in critical_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            missing_files.append(file)
            print(f"❌ {file}")
    
    if missing_files:
        print(f"❌ Missing critical files: {missing_files}")
        return False
    
    print("✅ All critical files present")
    return True

def main():
    """Run all tests"""
    print("🚀 ALLORA BACKEND CONFIGURATION TEST")
    print("=" * 50)
    
    tests = [
        test_file_structure,
        test_environment_variables,
        test_dependencies,
        test_model_imports,
        test_database_connection,
        test_database_tables,
        test_basic_queries
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
    
    print(f"\n📊 TEST RESULTS")
    print(f"=" * 30)
    print(f"Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED - Backend is properly configured!")
        return True
    else:
        print("⚠️  Some tests failed - Backend needs attention")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
