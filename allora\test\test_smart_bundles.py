#!/usr/bin/env python3
"""
Test script for Smart Bundles feature
"""
import requests
import json
import sys

# Configuration
BASE_URL = "http://localhost:5000"
TEST_USER = {
    "username": "testuser",
    "email": "<EMAIL>", 
    "password": "testpass123"
}

def test_smart_bundles():
    """Test the smart bundles functionality"""
    print("🧪 Testing Smart Bundles Feature")
    print("=" * 50)
    
    # Step 1: Register/Login user
    print("1. Registering test user...")
    try:
        response = requests.post(f"{BASE_URL}/api/register", json=TEST_USER)
        if response.status_code == 201:
            print("✅ User registered successfully")
        elif response.status_code == 400 and "already exists" in response.text:
            print("ℹ️  User already exists, proceeding with login")
        else:
            print(f"❌ Registration failed: {response.text}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend server. Is it running on port 5000?")
        return False
    
    # Login to get token
    print("2. Logging in...")
    try:
        response = requests.post(f"{BASE_URL}/api/login", json={
            "username": TEST_USER["username"],
            "password": TEST_USER["password"]
        })
        if response.status_code == 200:
            token = response.json().get('token')
            print("✅ Login successful")
        else:
            print(f"❌ Login failed: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Login error: {str(e)}")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Step 2: Get available products
    print("3. Fetching available products...")
    try:
        response = requests.get(f"{BASE_URL}/api/products", headers=headers)
        if response.status_code == 200:
            products = response.json()
            print(f"✅ Found {len(products)} products")
            
            # Show first few products
            print("   Sample products:")
            for i, product in enumerate(products[:5]):
                print(f"   - {product['id']}: {product['name']} (₹{product['price']})")
        else:
            print(f"❌ Failed to fetch products: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Products fetch error: {str(e)}")
        return False
    
    # Step 3: Add some items to cart
    print("4. Adding items to cart...")
    test_items = [
        {"product_id": 1, "quantity": 1},  # Eco-Friendly Bamboo T-Shirt
        {"product_id": 2, "quantity": 1},  # Organic Cotton Slim Jeans
    ]
    
    for item in test_items:
        try:
            response = requests.post(f"{BASE_URL}/api/cart", json=item, headers=headers)
            if response.status_code == 201:
                product_name = next((p['name'] for p in products if p['id'] == item['product_id']), f"Product {item['product_id']}")
                print(f"✅ Added {product_name} to cart")
            else:
                print(f"❌ Failed to add product {item['product_id']}: {response.text}")
        except Exception as e:
            print(f"❌ Cart add error: {str(e)}")
    
    # Step 4: Test smart bundles endpoint
    print("5. Testing smart bundles...")
    try:
        response = requests.get(f"{BASE_URL}/api/cart/smart-bundles", headers=headers)
        if response.status_code == 200:
            bundles_data = response.json()
            bundles = bundles_data.get('bundles', [])
            message = bundles_data.get('message', '')
            
            print(f"✅ Smart bundles API working! {message}")
            print(f"   Found {len(bundles)} bundle(s)")
            
            for i, bundle in enumerate(bundles, 1):
                print(f"\n   Bundle {i}: {bundle['title']}")
                print(f"   Description: {bundle['description']}")
                print(f"   Trigger Product: {bundle['trigger_product']['name']}")
                print(f"   Suggested Products ({len(bundle['suggested_products'])}):")
                for product in bundle['suggested_products']:
                    print(f"     - {product['name']} (₹{product['price']})")
                print(f"   Original Price: ₹{bundle['original_price']}")
                print(f"   Discounted Price: ₹{bundle['discounted_price']}")
                print(f"   Savings: ₹{bundle['savings']} ({bundle['bundle_discount_percentage']}% off)")
            
            return True
        else:
            print(f"❌ Smart bundles failed: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Smart bundles error: {str(e)}")
        return False

def test_add_bundle():
    """Test adding a bundle to cart"""
    print("\n6. Testing add bundle to cart...")
    # This would require getting a bundle first, then testing the add functionality
    # For now, we'll just test the endpoint exists
    try:
        response = requests.post(f"{BASE_URL}/api/cart/add-bundle", 
                               json={"product_ids": []}, 
                               headers={"Authorization": "Bearer fake_token"})
        # We expect this to fail with 401 (unauthorized) rather than 404 (not found)
        if response.status_code == 401:
            print("✅ Add bundle endpoint exists (returned 401 as expected)")
            return True
        else:
            print(f"ℹ️  Add bundle endpoint response: {response.status_code}")
            return True
    except Exception as e:
        print(f"❌ Add bundle test error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Smart Bundles Test Suite")
    print("Make sure the backend server is running on http://localhost:5000")
    print()
    
    success = test_smart_bundles()
    if success:
        test_add_bundle()
        print("\n🎉 Smart Bundles feature is working correctly!")
    else:
        print("\n❌ Smart Bundles feature has issues that need to be fixed.")
        sys.exit(1)
