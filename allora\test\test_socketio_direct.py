#!/usr/bin/env python3
"""
Direct Flask-SocketIO Testing Script
===================================

Tests Flask-SocketIO functionality directly using Python client
without relying on HTML pages or browsers.

Author: Allora Development Team
Date: 2025-07-13
"""

import socketio
import time
import threading
import json
from datetime import datetime

class SocketIOTester:
    """Direct SocketIO testing class"""
    
    def __init__(self, server_url='http://localhost:5000'):
        self.server_url = server_url
        self.client = None
        self.connected = False
        self.messages_received = []
        self.connection_events = []
        
    def create_client(self):
        """Create SocketIO client"""
        print("🔌 Creating SocketIO client...")
        
        self.client = socketio.Client(
            logger=True,
            engineio_logger=True
        )
        
        # Register event handlers
        self.register_handlers()
        
    def register_handlers(self):
        """Register SocketIO event handlers"""
        
        @self.client.event
        def connect():
            self.connected = True
            event = {
                'type': 'connect',
                'timestamp': datetime.now().isoformat(),
                'message': 'Connected to server'
            }
            self.connection_events.append(event)
            print("✅ Connected to Flask-SocketIO server!")
            
        @self.client.event
        def disconnect():
            self.connected = False
            event = {
                'type': 'disconnect',
                'timestamp': datetime.now().isoformat(),
                'message': 'Disconnected from server'
            }
            self.connection_events.append(event)
            print("❌ Disconnected from server")
            
        @self.client.event
        def connect_error(data):
            event = {
                'type': 'connect_error',
                'timestamp': datetime.now().isoformat(),
                'message': f'Connection error: {data}'
            }
            self.connection_events.append(event)
            print(f"❌ Connection error: {data}")
            
        @self.client.event
        def connection_established(data):
            message = {
                'event': 'connection_established',
                'data': data,
                'timestamp': datetime.now().isoformat()
            }
            self.messages_received.append(message)
            print(f"🎉 Connection established: {data}")
            
        @self.client.event
        def pong(data):
            message = {
                'event': 'pong',
                'data': data,
                'timestamp': datetime.now().isoformat()
            }
            self.messages_received.append(message)
            print(f"🏓 Pong received: {data}")
            
        @self.client.event
        def inventory_update(data):
            message = {
                'event': 'inventory_update',
                'data': data,
                'timestamp': datetime.now().isoformat()
            }
            self.messages_received.append(message)
            print(f"📦 Inventory update: {data}")
            
        @self.client.event
        def price_update(data):
            message = {
                'event': 'price_update',
                'data': data,
                'timestamp': datetime.now().isoformat()
            }
            self.messages_received.append(message)
            print(f"💰 Price update: {data}")
            
        @self.client.event
        def cart_update(data):
            message = {
                'event': 'cart_update',
                'data': data,
                'timestamp': datetime.now().isoformat()
            }
            self.messages_received.append(message)
            print(f"🛒 Cart update: {data}")
            
        @self.client.event
        def notification(data):
            message = {
                'event': 'notification',
                'data': data,
                'timestamp': datetime.now().isoformat()
            }
            self.messages_received.append(message)
            print(f"🔔 Notification: {data}")
            
        @self.client.event
        def admin_notification(data):
            message = {
                'event': 'admin_notification',
                'data': data,
                'timestamp': datetime.now().isoformat()
            }
            self.messages_received.append(message)
            print(f"👑 Admin notification: {data}")
            
        @self.client.event
        def subscribed(data):
            message = {
                'event': 'subscribed',
                'data': data,
                'timestamp': datetime.now().isoformat()
            }
            self.messages_received.append(message)
            print(f"📡 Subscribed: {data}")
    
    def test_connection(self, timeout=10):
        """Test basic connection"""
        print(f"🔍 Testing connection to {self.server_url}...")
        
        try:
            self.client.connect(self.server_url, wait_timeout=timeout)
            
            if self.connected:
                print("✅ Connection test PASSED")
                return True
            else:
                print("❌ Connection test FAILED - Not connected")
                return False
                
        except Exception as e:
            print(f"❌ Connection test FAILED: {e}")
            return False
    
    def test_ping_pong(self):
        """Test ping/pong functionality"""
        print("🔍 Testing ping/pong...")
        
        if not self.connected:
            print("❌ Not connected - skipping ping test")
            return False
        
        try:
            # Send ping
            self.client.emit('ping')
            print("🏓 Ping sent")
            
            # Wait for pong
            time.sleep(2)
            
            # Check if pong received
            pong_received = any(msg['event'] == 'pong' for msg in self.messages_received)
            
            if pong_received:
                print("✅ Ping/pong test PASSED")
                return True
            else:
                print("❌ Ping/pong test FAILED - No pong received")
                return False
                
        except Exception as e:
            print(f"❌ Ping/pong test FAILED: {e}")
            return False
    
    def test_subscription(self):
        """Test event subscription"""
        print("🔍 Testing event subscription...")
        
        if not self.connected:
            print("❌ Not connected - skipping subscription test")
            return False
        
        try:
            # Subscribe to events
            events = ['inventory', 'prices', 'orders']
            self.client.emit('subscribe', {'events': events})
            print(f"📡 Subscription request sent for: {events}")
            
            # Wait for confirmation
            time.sleep(2)
            
            # Check if subscription confirmed
            sub_confirmed = any(msg['event'] == 'subscribed' for msg in self.messages_received)
            
            if sub_confirmed:
                print("✅ Subscription test PASSED")
                return True
            else:
                print("❌ Subscription test FAILED - No confirmation received")
                return False
                
        except Exception as e:
            print(f"❌ Subscription test FAILED: {e}")
            return False
    
    def test_heartbeat(self):
        """Test heartbeat functionality"""
        print("🔍 Testing heartbeat...")
        
        if not self.connected:
            print("❌ Not connected - skipping heartbeat test")
            return False
        
        try:
            # Send heartbeat
            self.client.emit('heartbeat')
            print("💓 Heartbeat sent")
            
            # Wait for acknowledgment
            time.sleep(2)
            
            # Check if heartbeat acknowledged
            heartbeat_ack = any('heartbeat' in str(msg) for msg in self.messages_received)
            
            if heartbeat_ack:
                print("✅ Heartbeat test PASSED")
                return True
            else:
                print("⚠️  Heartbeat test - No specific ack (may be normal)")
                return True  # Heartbeat might not have specific response
                
        except Exception as e:
            print(f"❌ Heartbeat test FAILED: {e}")
            return False
    
    def test_authentication(self, user_id="test_user", is_admin=False):
        """Test authentication with user credentials"""
        print(f"🔍 Testing authentication (user: {user_id}, admin: {is_admin})...")
        
        # Disconnect if connected
        if self.connected:
            self.client.disconnect()
            time.sleep(1)
        
        try:
            # Connect with authentication
            auth_data = {
                'user_id': user_id,
                'is_admin': is_admin
            }
            
            self.client.connect(self.server_url, auth=auth_data, wait_timeout=10)
            
            if self.connected:
                print("✅ Authentication test PASSED")
                return True
            else:
                print("❌ Authentication test FAILED")
                return False
                
        except Exception as e:
            print(f"❌ Authentication test FAILED: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from server"""
        if self.client and self.connected:
            self.client.disconnect()
            print("🔌 Disconnected from server")
    
    def get_stats(self):
        """Get connection statistics"""
        return {
            'connected': self.connected,
            'messages_received': len(self.messages_received),
            'connection_events': len(self.connection_events),
            'server_url': self.server_url
        }

def run_comprehensive_test():
    """Run comprehensive SocketIO tests"""
    print("🧪 Flask-SocketIO Direct Testing Suite")
    print("=" * 50)
    
    # Create tester
    tester = SocketIOTester()
    tester.create_client()
    
    # Test results
    results = {}
    
    # Test 1: Basic Connection
    results['connection'] = tester.test_connection()
    
    if results['connection']:
        # Test 2: Ping/Pong
        results['ping_pong'] = tester.test_ping_pong()
        
        # Test 3: Event Subscription
        results['subscription'] = tester.test_subscription()
        
        # Test 4: Heartbeat
        results['heartbeat'] = tester.test_heartbeat()
        
        # Test 5: Authentication
        results['authentication'] = tester.test_authentication("test_user_123", False)
        
        # Test 6: Admin Authentication
        results['admin_auth'] = tester.test_authentication("admin_user_123", True)
    else:
        print("⚠️  Skipping other tests due to connection failure")
        results.update({
            'ping_pong': False,
            'subscription': False,
            'heartbeat': False,
            'authentication': False,
            'admin_auth': False
        })
    
    # Get final stats
    stats = tester.get_stats()
    
    # Disconnect
    tester.disconnect()
    
    # Print results
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    print(f"\n📊 Connection Statistics:")
    print(f"Messages Received: {stats['messages_received']}")
    print(f"Connection Events: {stats['connection_events']}")
    print(f"Server URL: {stats['server_url']}")
    
    # Final assessment
    if passed == total:
        print("\n🎉 ALL TESTS PASSED - Flask-SocketIO is working perfectly!")
    elif passed >= total * 0.8:
        print("\n✅ Most tests passed - Minor issues detected")
    elif passed >= total * 0.5:
        print("\n⚠️  Some tests passed - Significant issues detected")
    else:
        print("\n❌ Most tests failed - Major issues detected")
    
    return results, stats

if __name__ == '__main__':
    # Check if socketio-client is available
    try:
        import socketio
        print("✅ python-socketio client available")
    except ImportError:
        print("❌ python-socketio not installed")
        print("💡 Install with: pip install python-socketio")
        exit(1)
    
    # Run tests
    results, stats = run_comprehensive_test()
