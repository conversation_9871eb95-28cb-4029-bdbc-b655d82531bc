"""
Common Blueprint Utilities
==========================

Shared utilities, decorators, and response formatters for all blueprints.
"""

from .response_wrapper import (
    success_response,
    error_response,
    paginated_response,
    validation_error_response
)

from .auth_decorators import (
    jwt_required_v2,
    admin_required_v2,
    seller_required_v2,
    optional_auth
)

from .validators import (
    validate_json,
    validate_pagination,
    validate_required_fields
)

from .exceptions import (
    APIException,
    ValidationError,
    AuthenticationError,
    AuthorizationError,
    NotFoundError
)

__all__ = [
    # Response utilities
    'success_response',
    'error_response', 
    'paginated_response',
    'validation_error_response',
    
    # Authentication decorators
    'jwt_required_v2',
    'admin_required_v2',
    'seller_required_v2',
    'optional_auth',
    
    # Validators
    'validate_json',
    'validate_pagination',
    'validate_required_fields',
    
    # Exceptions
    'APIException',
    'ValidationError',
    'AuthenticationError',
    'AuthorizationError',
    'NotFoundError'
]
