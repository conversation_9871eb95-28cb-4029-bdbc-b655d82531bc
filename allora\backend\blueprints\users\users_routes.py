"""
Users Routes
============

User management endpoints with consistent response format and URL patterns.
"""

from flask import request
from datetime import datetime
import logging

from ..common.versioning import create_versioned_blueprint
from ..common.response_wrapper import (
    success_response, error_response, paginated_response, 
    not_found_response, validation_error_response
)
from ..common.auth_decorators import jwt_required_v2, rate_limit_v2, validate_content_type
from ..common.validators import (
    validate_json, validate_required_fields, validate_pagination,
    validate_email, validate_phone
)

logger = logging.getLogger(__name__)

# Create versioned blueprint
users_bp = create_versioned_blueprint('users', __name__, url_prefix='/users')

@users_bp.route('/profile', methods=['GET'])
@jwt_required_v2()
@rate_limit_v2(limit=60, window=60, per='user')
def get_profile(user):
    """
    Get user profile information.

    GET /api/v1/users/profile
    """
    try:
        from app import db, Address, Order, Wishlist

        # Get user addresses
        addresses = Address.query.filter_by(user_id=user.id).all()
        addresses_data = []
        for address in addresses:
            addresses_data.append({
                "id": address.id,
                "type": getattr(address, 'address_type', 'shipping'),
                "street": address.street,
                "city": address.city,
                "state": address.state,
                "postal_code": address.postal_code,
                "country": address.country,
                "is_default": getattr(address, 'is_default', False),
                "created_at": address.created_at.isoformat() if address.created_at else None
            })

        # Get user statistics
        total_orders = Order.query.filter_by(user_id=user.id).count()
        total_wishlist_items = Wishlist.query.filter_by(user_id=user.id).count()

        # Calculate total spent
        total_spent = db.session.query(
            db.func.sum(Order.total_amount)
        ).filter(
            Order.user_id == user.id,
            Order.status.in_(['completed', 'delivered'])
        ).scalar() or 0.0

        profile_data = {
            "id": user.id,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "phone": getattr(user, 'phone', None),
            "date_of_birth": user.date_of_birth.isoformat() if getattr(user, 'date_of_birth', None) else None,
            "gender": getattr(user, 'gender', None),
            "profile_image": getattr(user, 'profile_image', None),
            "is_active": user.is_active,
            "email_verified": getattr(user, 'email_verified', False),
            "phone_verified": getattr(user, 'phone_verified', False),
            "addresses": addresses_data,
            "statistics": {
                "total_orders": total_orders,
                "total_spent": float(total_spent),
                "wishlist_items": total_wishlist_items
            },
            "preferences": {
                "newsletter_subscribed": getattr(user, 'newsletter_subscribed', True),
                "sms_notifications": getattr(user, 'sms_notifications', True),
                "email_notifications": getattr(user, 'email_notifications', True)
            },
            "email_verified": getattr(user, 'email_verified', False),
            "phone_verified": getattr(user, 'phone_verified', False),

            "created_at": user.created_at.isoformat() if user.created_at else None,
            "last_login": user.last_login.isoformat() if getattr(user, 'last_login', None) else None
        }
        
        return success_response(
            data=profile_data,
            message="Profile retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get profile error: {e}")
        return error_response(
            message="Failed to retrieve profile",
            status_code=500,
            error_code="PROFILE_FETCH_FAILED"
        )

@users_bp.route('/profile', methods=['PUT'])
@jwt_required_v2()
@validate_content_type()
@rate_limit_v2(limit=10, window=300, per='user')  # 10 updates per 5 minutes
def update_profile(user):
    """
    Update user profile information.

    PUT /api/v1/users/profile
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        from app import db

        # Fields that can be updated
        updatable_fields = {
            'first_name': str,
            'last_name': str,
            'phone': str,
            'date_of_birth': str,
            'gender': str,
            'profile_image': str,
            'newsletter_subscribed': bool,
            'sms_notifications': bool,
            'email_notifications': bool
        }

        updated_fields = []

        for field, field_type in updatable_fields.items():
            if field in data:
                value = data[field]

                # Validate field type
                if value is not None and not isinstance(value, field_type):
                    if field_type == str:
                        value = str(value).strip()
                    elif field_type == bool:
                        value = bool(value)

                # Special validation for specific fields
                if field == 'phone' and value:
                    if not validate_phone(value):
                        return validation_error_response(
                            errors={"phone": ["Invalid phone number format"]},
                            message="Invalid phone number"
                        )

                if field == 'date_of_birth' and value:
                    try:
                        # Validate date format
                        datetime.strptime(value, '%Y-%m-%d')
                    except ValueError:
                        return validation_error_response(
                            errors={"date_of_birth": ["Invalid date format. Use YYYY-MM-DD"]},
                            message="Invalid date format"
                        )

                if field == 'gender' and value:
                    if value not in ['male', 'female', 'other', 'prefer_not_to_say']:
                        return validation_error_response(
                            errors={"gender": ["Invalid gender value"]},
                            message="Invalid gender"
                        )

                # Update user field
                setattr(user, field, value)
                updated_fields.append(field)

        if updated_fields:
            user.updated_at = datetime.utcnow()
            db.session.commit()

            return success_response(
                data={
                    "updated_fields": updated_fields,
                    "profile": {
                        "id": user.id,
                        "first_name": user.first_name,
                        "last_name": user.last_name,
                        "phone": getattr(user, 'phone', None),
                        "date_of_birth": user.date_of_birth.isoformat() if getattr(user, 'date_of_birth', None) else None,
                        "gender": getattr(user, 'gender', None),
                        "profile_image": getattr(user, 'profile_image', None),
                        "updated_at": user.updated_at.isoformat()
                    }
                },
                message="Profile updated successfully"
            )
        else:
            return success_response(
                data={"updated_fields": []},
                message="No changes made to profile"
            )

    except Exception as e:
        logger.error(f"Update profile error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to update profile",
            status_code=500,
            error_code="PROFILE_UPDATE_FAILED"
        )

@users_bp.route('/profile', methods=['PUT'])
@jwt_required_v2()
@validate_content_type()
def update_profile(user):
    """
    Update user profile information.
    
    PUT /api/v1/users/profile
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data
        
        from app import db
        
        # Update allowed fields
        allowed_fields = ['first_name', 'last_name', 'phone', 'date_of_birth', 'gender']
        updated_fields = []
        
        for field in allowed_fields:
            if field in data:
                if field == 'phone' and data[field]:
                    if not validate_phone(data[field]):
                        return validation_error_response(
                            errors={"phone": ["Invalid phone number format"]},
                            message="Invalid phone number"
                        )
                
                if field == 'date_of_birth' and data[field]:
                    try:
                        from datetime import datetime
                        datetime.fromisoformat(data[field].replace('Z', '+00:00'))
                    except ValueError:
                        return validation_error_response(
                            errors={"date_of_birth": ["Invalid date format. Use ISO format (YYYY-MM-DD)"]},
                            message="Invalid date format"
                        )
                
                setattr(user, field, data[field])
                updated_fields.append(field)
        
        if updated_fields:
            user.updated_at = datetime.utcnow()
            db.session.commit()
            
            return success_response(
                data={
                    "updated_fields": updated_fields,
                    "profile": {
                        "id": user.id,
                        "first_name": user.first_name,
                        "last_name": user.last_name,
                        "phone": getattr(user, 'phone', None),
                        "date_of_birth": user.date_of_birth.isoformat() if getattr(user, 'date_of_birth', None) else None,
                        "gender": getattr(user, 'gender', None),
                        "updated_at": user.updated_at.isoformat()
                    }
                },
                message="Profile updated successfully"
            )
        else:
            return error_response(
                message="No valid fields provided for update",
                status_code=400,
                error_code="NO_FIELDS_TO_UPDATE"
            )
        
    except Exception as e:
        logger.error(f"Update profile error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to update profile",
            status_code=500,
            error_code="PROFILE_UPDATE_FAILED"
        )

@users_bp.route('/addresses', methods=['GET'])
@jwt_required_v2()
def get_addresses(user):
    """
    Get user addresses.
    
    GET /api/v1/users/addresses
    """
    try:
        from app import UserAddress
        
        addresses = UserAddress.query.filter_by(user_id=user.id).order_by(
            UserAddress.is_default.desc(),
            UserAddress.created_at.desc()
        ).all()
        
        addresses_data = []
        for addr in addresses:
            addresses_data.append({
                "id": addr.id,
                "type": addr.address_type,
                "name": getattr(addr, 'name', None),
                "street": addr.street,
                "city": addr.city,
                "state": addr.state,
                "postal_code": addr.postal_code,
                "country": addr.country,
                "is_default": addr.is_default,
                "created_at": addr.created_at.isoformat() if addr.created_at else None
            })
        
        return success_response(
            data={"addresses": addresses_data},
            message="Addresses retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get addresses error: {e}")
        return error_response(
            message="Failed to retrieve addresses",
            status_code=500,
            error_code="ADDRESSES_FETCH_FAILED"
        )

@users_bp.route('/addresses', methods=['POST'])
@jwt_required_v2()
@validate_content_type()
def add_address(user):
    """
    Add new user address.
    
    POST /api/v1/users/addresses
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data
        
        # Validate required fields
        required_fields = ['street', 'city', 'state', 'postal_code', 'country']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp
        
        from app import db, UserAddress
        
        # Check if this should be the default address
        is_default = data.get('is_default', False)
        
        # If setting as default, unset other default addresses
        if is_default:
            UserAddress.query.filter_by(user_id=user.id, is_default=True).update(
                {'is_default': False}
            )
        
        # Create new address
        new_address = UserAddress(
            user_id=user.id,
            address_type=data.get('type', 'shipping'),
            name=data.get('name'),
            street=data['street'],
            city=data['city'],
            state=data['state'],
            postal_code=data['postal_code'],
            country=data['country'],
            is_default=is_default,
            created_at=datetime.utcnow()
        )
        
        db.session.add(new_address)
        db.session.commit()
        
        address_data = {
            "id": new_address.id,
            "type": new_address.address_type,
            "name": new_address.name,
            "street": new_address.street,
            "city": new_address.city,
            "state": new_address.state,
            "postal_code": new_address.postal_code,
            "country": new_address.country,
            "is_default": new_address.is_default,
            "created_at": new_address.created_at.isoformat()
        }
        
        return success_response(
            data=address_data,
            message="Address added successfully",
            status_code=201
        )
        
    except Exception as e:
        logger.error(f"Add address error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to add address",
            status_code=500,
            error_code="ADDRESS_ADD_FAILED"
        )

@users_bp.route('/wishlist', methods=['GET'])
@jwt_required_v2()
def get_wishlist(user):
    """
    Get user wishlist items.
    
    GET /api/v1/users/wishlist
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=50)
        
        from app import Wishlist, Product, ProductImage
        
        # Get wishlist items with pagination
        query = Wishlist.query.filter_by(user_id=user.id).order_by(
            Wishlist.created_at.desc()
        )
        
        paginated_wishlist = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        wishlist_data = []
        for item in paginated_wishlist.items:
            product = Product.query.get(item.product_id)
            if product and product.is_active:
                wishlist_data.append({
                    "id": item.id,
                    "product": {
                        "id": product.id,
                        "name": product.name,
                        "price": float(product.price),
                        "category": product.category,
                        "image_url": product.images[0].image_url if product.images else None,
                        "in_stock": product.stock_quantity > 0,
                        "stock_quantity": product.stock_quantity
                    },
                    "added_at": item.created_at.isoformat() if item.created_at else None
                })
        
        return paginated_response(
            data=wishlist_data,
            page=page,
            per_page=per_page,
            total=paginated_wishlist.total,
            message="Wishlist retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get wishlist error: {e}")
        return error_response(
            message="Failed to retrieve wishlist",
            status_code=500,
            error_code="WISHLIST_FETCH_FAILED"
        )

@users_bp.route('/wishlist', methods=['POST'])
@jwt_required_v2()
@validate_content_type()
@rate_limit_v2(limit=30, window=60, per='user')
def add_to_wishlist(user):
    """
    Add item to user wishlist.

    POST /api/v1/users/wishlist
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['product_id']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        product_id = data['product_id']

        from app import db, Product, Wishlist

        # Check if product exists and is active
        product = Product.query.filter_by(id=product_id, is_active=True).first()
        if not product:
            return not_found_response("Product", product_id)

        # Check if item already in wishlist
        existing_item = Wishlist.query.filter_by(
            user_id=user.id,
            product_id=product_id
        ).first()

        if existing_item:
            return error_response(
                message="Product is already in your wishlist",
                status_code=409,
                error_code="WISHLIST_ITEM_EXISTS"
            )

        # Add to wishlist
        wishlist_item = Wishlist(
            user_id=user.id,
            product_id=product_id,
            created_at=datetime.utcnow()
        )

        db.session.add(wishlist_item)
        db.session.commit()

        # Return wishlist item data
        item_data = {
            "id": wishlist_item.id,
            "product": {
                "id": product.id,
                "name": product.name,
                "price": float(product.price),
                "category": product.category,
                "in_stock": product.stock_quantity > 0
            },
            "added_at": wishlist_item.created_at.isoformat()
        }

        return success_response(
            data=item_data,
            message="Product added to wishlist successfully",
            status_code=201
        )

    except Exception as e:
        logger.error(f"Add to wishlist error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to add product to wishlist",
            status_code=500,
            error_code="WISHLIST_ADD_FAILED"
        )

@users_bp.route('/wishlist/<int:item_id>', methods=['DELETE'])
@jwt_required_v2()
@rate_limit_v2(limit=60, window=60, per='user')
def remove_from_wishlist(user, item_id):
    """
    Remove item from user wishlist.

    DELETE /api/v1/users/wishlist/{item_id}
    """
    try:
        from app import db, Wishlist

        # Get wishlist item
        wishlist_item = Wishlist.query.filter_by(
            id=item_id,
            user_id=user.id
        ).first()

        if not wishlist_item:
            return not_found_response("Wishlist item", item_id)

        # Remove from wishlist
        db.session.delete(wishlist_item)
        db.session.commit()

        return success_response(
            data={"wishlist_item_id": item_id},
            message="Product removed from wishlist successfully"
        )

    except Exception as e:
        logger.error(f"Remove from wishlist error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to remove product from wishlist",
            status_code=500,
            error_code="WISHLIST_REMOVE_FAILED"
        )

@users_bp.route('/wishlist/<int:item_id>/move-to-cart', methods=['POST'])
@jwt_required_v2()
@rate_limit_v2(limit=30, window=60, per='user')
def move_wishlist_to_cart(user, item_id):
    """
    Move wishlist item to shopping cart.

    POST /api/v1/users/wishlist/{item_id}/move-to-cart
    """
    try:
        from app import db, Wishlist, Product, Cart, CartItem

        # Get wishlist item
        wishlist_item = Wishlist.query.filter_by(
            id=item_id,
            user_id=user.id
        ).first()

        if not wishlist_item:
            return not_found_response("Wishlist item", item_id)

        # Get product
        product = Product.query.get(wishlist_item.product_id)
        if not product or not product.is_active:
            return error_response(
                message="Product is no longer available",
                status_code=400,
                error_code="PRODUCT_UNAVAILABLE"
            )

        # Check stock
        if product.stock_quantity < 1:
            return error_response(
                message="Product is out of stock",
                status_code=400,
                error_code="OUT_OF_STOCK"
            )

        # Get or create cart
        cart = Cart.query.filter_by(user_id=user.id, status='active').first()
        if not cart:
            cart = Cart(
                user_id=user.id,
                status='active',
                created_at=datetime.utcnow()
            )
            db.session.add(cart)
            db.session.flush()

        # Check if item already in cart
        existing_cart_item = CartItem.query.filter_by(
            cart_id=cart.id,
            product_id=product.id
        ).first()

        if existing_cart_item:
            # Update quantity
            if product.stock_quantity >= existing_cart_item.quantity + 1:
                existing_cart_item.quantity += 1
                existing_cart_item.updated_at = datetime.utcnow()
            else:
                return error_response(
                    message="Cannot add more items to cart - insufficient stock",
                    status_code=400,
                    error_code="INSUFFICIENT_STOCK"
                )
        else:
            # Add new cart item
            cart_item = CartItem(
                cart_id=cart.id,
                product_id=product.id,
                quantity=1,
                created_at=datetime.utcnow()
            )
            db.session.add(cart_item)

        # Remove from wishlist
        db.session.delete(wishlist_item)

        # Update cart timestamp
        cart.updated_at = datetime.utcnow()

        db.session.commit()

        return success_response(
            data={
                "product_id": product.id,
                "product_name": product.name,
                "moved_to_cart": True
            },
            message="Product moved to cart successfully"
        )

    except Exception as e:
        logger.error(f"Move wishlist to cart error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to move product to cart",
            status_code=500,
            error_code="WISHLIST_MOVE_FAILED"
        )

# User Activity and Analytics Endpoints

@users_bp.route('/activity', methods=['GET'])
@jwt_required_v2()
@rate_limit_v2(limit=60, window=60, per='user')
def get_user_activity(user):
    """
    Get user activity history.

    GET /api/v1/users/activity
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        activity_type = request.args.get('type')  # order, search, view, etc.

        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=50)

        from app import db, UserActivity, Order, Product

        # Build activity query
        query = UserActivity.query.filter_by(user_id=user.id)

        if activity_type:
            query = query.filter_by(activity_type=activity_type)

        query = query.order_by(UserActivity.created_at.desc())

        # Execute paginated query
        paginated_activity = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        activity_data = []
        for activity in paginated_activity.items:
            activity_item = {
                "id": activity.id,
                "type": activity.activity_type,
                "description": activity.description,
                "metadata": activity.metadata or {},
                "created_at": activity.created_at.isoformat() if activity.created_at else None
            }

            # Add related object details based on activity type
            if activity.activity_type == 'product_view' and activity.metadata:
                product_id = activity.metadata.get('product_id')
                if product_id:
                    product = Product.query.get(product_id)
                    if product:
                        activity_item['product'] = {
                            "id": product.id,
                            "name": product.name,
                            "category": product.category
                        }

            elif activity.activity_type == 'order_placed' and activity.metadata:
                order_id = activity.metadata.get('order_id')
                if order_id:
                    order = Order.query.get(order_id)
                    if order:
                        activity_item['order'] = {
                            "id": order.id,
                            "order_number": f"ORD-{order.id:06d}",
                            "total_amount": float(order.total_amount)
                        }

            activity_data.append(activity_item)

        return paginated_response(
            data=activity_data,
            page=page,
            per_page=per_page,
            total=paginated_activity.total,
            message="User activity retrieved successfully",
            meta={
                "filter": {"type": activity_type} if activity_type else None
            }
        )

    except Exception as e:
        logger.error(f"Get user activity error: {e}")
        return error_response(
            message="Failed to retrieve user activity",
            status_code=500,
            error_code="USER_ACTIVITY_FETCH_FAILED"
        )

@users_bp.route('/analytics/search', methods=['POST'])
@jwt_required_v2()
@validate_content_type()
@rate_limit_v2(limit=100, window=60, per='user')
def log_search_analytics(user):
    """
    Log user search analytics.

    POST /api/v1/users/analytics/search
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['query']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        query = data['query'].strip()
        results_count = data.get('results_count', 0)
        filters_applied = data.get('filters_applied', {})

        from app import db, SearchAnalytics

        # Log search analytics
        search_log = SearchAnalytics(
            user_id=user.id,
            query=query,
            results_count=results_count,
            filters_applied=filters_applied,
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent'),
            created_at=datetime.utcnow()
        )

        db.session.add(search_log)
        db.session.commit()

        return success_response(
            data={
                "search_id": search_log.id,
                "query": query,
                "logged_at": search_log.created_at.isoformat()
            },
            message="Search analytics logged successfully",
            status_code=201
        )

    except Exception as e:
        logger.error(f"Log search analytics error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to log search analytics",
            status_code=500,
            error_code="SEARCH_ANALYTICS_LOG_FAILED"
        )

@users_bp.route('/analytics/product-view', methods=['POST'])
@jwt_required_v2()
@validate_content_type()
@rate_limit_v2(limit=200, window=60, per='user')
def log_product_view_analytics(user):
    """
    Log product view analytics.

    POST /api/v1/users/analytics/product-view
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['product_id']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        product_id = data['product_id']
        view_duration = data.get('view_duration', 0)  # in seconds
        source = data.get('source', 'direct')  # search, category, recommendation, etc.

        from app import db, Product, ProductViewAnalytics, UserActivity

        # Verify product exists
        product = Product.query.get(product_id)
        if not product:
            return not_found_response("Product", product_id)

        # Log product view analytics
        view_log = ProductViewAnalytics(
            user_id=user.id,
            product_id=product_id,
            view_duration=view_duration,
            source=source,
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent'),
            created_at=datetime.utcnow()
        )

        db.session.add(view_log)

        # Also log as user activity
        activity = UserActivity(
            user_id=user.id,
            activity_type='product_view',
            description=f"Viewed product: {product.name}",
            metadata={
                'product_id': product_id,
                'product_name': product.name,
                'view_duration': view_duration,
                'source': source
            },
            created_at=datetime.utcnow()
        )

        db.session.add(activity)
        db.session.commit()

        return success_response(
            data={
                "view_id": view_log.id,
                "product_id": product_id,
                "logged_at": view_log.created_at.isoformat()
            },
            message="Product view analytics logged successfully",
            status_code=201
        )

    except Exception as e:
        logger.error(f"Log product view analytics error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to log product view analytics",
            status_code=500,
            error_code="PRODUCT_VIEW_ANALYTICS_LOG_FAILED"
        )
