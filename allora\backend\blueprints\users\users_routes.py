"""
Users Routes
============

User management endpoints with consistent response format and URL patterns.
"""

from flask import request
from datetime import datetime
import logging

from ..common.versioning import create_versioned_blueprint
from ..common.response_wrapper import (
    success_response, error_response, paginated_response, 
    not_found_response, validation_error_response
)
from ..common.auth_decorators import jwt_required_v2, rate_limit_v2, validate_content_type
from ..common.validators import (
    validate_json, validate_required_fields, validate_pagination,
    validate_email, validate_phone
)

logger = logging.getLogger(__name__)

# Create versioned blueprint
users_bp = create_versioned_blueprint('users', __name__, url_prefix='/users')

@users_bp.route('/profile', methods=['GET'])
@jwt_required_v2()
def get_profile(user):
    """
    Get user profile information.
    
    GET /api/v1/users/profile
    """
    try:
        from app import UserAddress
        
        # Get user addresses
        addresses = UserAddress.query.filter_by(user_id=user.id).all()
        
        profile_data = {
            "id": user.id,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "phone": getattr(user, 'phone', None),
            "date_of_birth": user.date_of_birth.isoformat() if getattr(user, 'date_of_birth', None) else None,
            "gender": getattr(user, 'gender', None),
            "is_active": user.is_active,
            "email_verified": getattr(user, 'email_verified', False),
            "phone_verified": getattr(user, 'phone_verified', False),
            "addresses": [
                {
                    "id": addr.id,
                    "type": addr.address_type,
                    "street": addr.street,
                    "city": addr.city,
                    "state": addr.state,
                    "postal_code": addr.postal_code,
                    "country": addr.country,
                    "is_default": addr.is_default
                }
                for addr in addresses
            ],
            "created_at": user.created_at.isoformat() if user.created_at else None,
            "last_login": user.last_login.isoformat() if getattr(user, 'last_login', None) else None
        }
        
        return success_response(
            data=profile_data,
            message="Profile retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get profile error: {e}")
        return error_response(
            message="Failed to retrieve profile",
            status_code=500,
            error_code="PROFILE_FETCH_FAILED"
        )

@users_bp.route('/profile', methods=['PUT'])
@jwt_required_v2()
@validate_content_type()
def update_profile(user):
    """
    Update user profile information.
    
    PUT /api/v1/users/profile
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data
        
        from app import db
        
        # Update allowed fields
        allowed_fields = ['first_name', 'last_name', 'phone', 'date_of_birth', 'gender']
        updated_fields = []
        
        for field in allowed_fields:
            if field in data:
                if field == 'phone' and data[field]:
                    if not validate_phone(data[field]):
                        return validation_error_response(
                            errors={"phone": ["Invalid phone number format"]},
                            message="Invalid phone number"
                        )
                
                if field == 'date_of_birth' and data[field]:
                    try:
                        from datetime import datetime
                        datetime.fromisoformat(data[field].replace('Z', '+00:00'))
                    except ValueError:
                        return validation_error_response(
                            errors={"date_of_birth": ["Invalid date format. Use ISO format (YYYY-MM-DD)"]},
                            message="Invalid date format"
                        )
                
                setattr(user, field, data[field])
                updated_fields.append(field)
        
        if updated_fields:
            user.updated_at = datetime.utcnow()
            db.session.commit()
            
            return success_response(
                data={
                    "updated_fields": updated_fields,
                    "profile": {
                        "id": user.id,
                        "first_name": user.first_name,
                        "last_name": user.last_name,
                        "phone": getattr(user, 'phone', None),
                        "date_of_birth": user.date_of_birth.isoformat() if getattr(user, 'date_of_birth', None) else None,
                        "gender": getattr(user, 'gender', None),
                        "updated_at": user.updated_at.isoformat()
                    }
                },
                message="Profile updated successfully"
            )
        else:
            return error_response(
                message="No valid fields provided for update",
                status_code=400,
                error_code="NO_FIELDS_TO_UPDATE"
            )
        
    except Exception as e:
        logger.error(f"Update profile error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to update profile",
            status_code=500,
            error_code="PROFILE_UPDATE_FAILED"
        )

@users_bp.route('/addresses', methods=['GET'])
@jwt_required_v2()
def get_addresses(user):
    """
    Get user addresses.
    
    GET /api/v1/users/addresses
    """
    try:
        from app import UserAddress
        
        addresses = UserAddress.query.filter_by(user_id=user.id).order_by(
            UserAddress.is_default.desc(),
            UserAddress.created_at.desc()
        ).all()
        
        addresses_data = []
        for addr in addresses:
            addresses_data.append({
                "id": addr.id,
                "type": addr.address_type,
                "name": getattr(addr, 'name', None),
                "street": addr.street,
                "city": addr.city,
                "state": addr.state,
                "postal_code": addr.postal_code,
                "country": addr.country,
                "is_default": addr.is_default,
                "created_at": addr.created_at.isoformat() if addr.created_at else None
            })
        
        return success_response(
            data={"addresses": addresses_data},
            message="Addresses retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get addresses error: {e}")
        return error_response(
            message="Failed to retrieve addresses",
            status_code=500,
            error_code="ADDRESSES_FETCH_FAILED"
        )

@users_bp.route('/addresses', methods=['POST'])
@jwt_required_v2()
@validate_content_type()
def add_address(user):
    """
    Add new user address.
    
    POST /api/v1/users/addresses
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data
        
        # Validate required fields
        required_fields = ['street', 'city', 'state', 'postal_code', 'country']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp
        
        from app import db, UserAddress
        
        # Check if this should be the default address
        is_default = data.get('is_default', False)
        
        # If setting as default, unset other default addresses
        if is_default:
            UserAddress.query.filter_by(user_id=user.id, is_default=True).update(
                {'is_default': False}
            )
        
        # Create new address
        new_address = UserAddress(
            user_id=user.id,
            address_type=data.get('type', 'shipping'),
            name=data.get('name'),
            street=data['street'],
            city=data['city'],
            state=data['state'],
            postal_code=data['postal_code'],
            country=data['country'],
            is_default=is_default,
            created_at=datetime.utcnow()
        )
        
        db.session.add(new_address)
        db.session.commit()
        
        address_data = {
            "id": new_address.id,
            "type": new_address.address_type,
            "name": new_address.name,
            "street": new_address.street,
            "city": new_address.city,
            "state": new_address.state,
            "postal_code": new_address.postal_code,
            "country": new_address.country,
            "is_default": new_address.is_default,
            "created_at": new_address.created_at.isoformat()
        }
        
        return success_response(
            data=address_data,
            message="Address added successfully",
            status_code=201
        )
        
    except Exception as e:
        logger.error(f"Add address error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to add address",
            status_code=500,
            error_code="ADDRESS_ADD_FAILED"
        )

@users_bp.route('/wishlist', methods=['GET'])
@jwt_required_v2()
def get_wishlist(user):
    """
    Get user wishlist items.
    
    GET /api/v1/users/wishlist
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=50)
        
        from app import Wishlist, Product, ProductImage
        
        # Get wishlist items with pagination
        query = Wishlist.query.filter_by(user_id=user.id).order_by(
            Wishlist.created_at.desc()
        )
        
        paginated_wishlist = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        wishlist_data = []
        for item in paginated_wishlist.items:
            product = Product.query.get(item.product_id)
            if product and product.is_active:
                wishlist_data.append({
                    "id": item.id,
                    "product": {
                        "id": product.id,
                        "name": product.name,
                        "price": float(product.price),
                        "category": product.category,
                        "image_url": product.images[0].image_url if product.images else None,
                        "in_stock": product.stock_quantity > 0,
                        "stock_quantity": product.stock_quantity
                    },
                    "added_at": item.created_at.isoformat() if item.created_at else None
                })
        
        return paginated_response(
            data=wishlist_data,
            page=page,
            per_page=per_page,
            total=paginated_wishlist.total,
            message="Wishlist retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get wishlist error: {e}")
        return error_response(
            message="Failed to retrieve wishlist",
            status_code=500,
            error_code="WISHLIST_FETCH_FAILED"
        )
