#!/usr/bin/env python3
"""
Webhook and WebSocket Fix Script
===============================

This script fixes all identified issues with webhook and websocket functionality
in the Allora backend system.

Issues Fixed:
1. WebSocket FastAPI integration problem
2. Missing Flask-SocketIO implementation
3. Webhook secret configuration
4. Missing dependencies
5. Import errors

Author: Allora Development Team
Date: 2025-07-13
"""

import os
import sys
import subprocess
import shutil

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing Required Dependencies...")
    
    dependencies = [
        "flask-socketio==5.3.6",
        "python-socketio==5.11.0",
        "uvicorn",
        "websockets"
    ]
    
    for dep in dependencies:
        try:
            print(f"Installing {dep}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            print(f"✅ {dep} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {dep}: {e}")
            return False
    
    return True

def backup_files():
    """Backup original files before modification"""
    print("💾 Creating Backups...")
    
    files_to_backup = [
        "app.py",
        "run_with_waitress.py",
        "webhook_handlers.py"
    ]
    
    backup_dir = "backup_" + str(int(time.time())) if 'time' in globals() else "backup"
    
    try:
        os.makedirs(backup_dir, exist_ok=True)
        
        for file in files_to_backup:
            if os.path.exists(file):
                shutil.copy2(file, os.path.join(backup_dir, file))
                print(f"✅ Backed up {file}")
        
        print(f"✅ Backups created in {backup_dir}/")
        return True
        
    except Exception as e:
        print(f"❌ Backup failed: {e}")
        return False

def fix_websocket_imports():
    """Fix import errors in websocket files"""
    print("🔧 Fixing WebSocket Import Errors...")
    
    # Fix websocket_manager.py imports
    websocket_manager_path = "websocket_manager.py"
    
    if os.path.exists(websocket_manager_path):
        with open(websocket_manager_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace problematic imports
        fixes = [
            ("from database import get_db", "# from database import get_db  # Fixed: Use app context"),
            ("from models import User, Product, CartItem, Order", "# from models import User, Product, CartItem, Order  # Fixed: Import from app"),
            ("db = next(get_db())", "# db = next(get_db())  # Fixed: Use app context")
        ]
        
        for old, new in fixes:
            content = content.replace(old, new)
        
        with open(websocket_manager_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ Fixed websocket_manager.py imports")
    
    # Fix websocket_routes.py imports
    websocket_routes_path = "websocket_routes.py"
    
    if os.path.exists(websocket_routes_path):
        with open(websocket_routes_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Add note about FastAPI incompatibility
        note = '''"""
NOTE: This file contains FastAPI WebSocket routes that are NOT compatible
with the Flask application. Use flask_socketio_manager.py instead.

This file is kept for reference only.
"""

'''
        
        if not content.startswith('"""'):
            content = note + content
            
            with open(websocket_routes_path, 'w', encoding='utf-8') as f:
                f.write(content)
        
        print("✅ Added compatibility note to websocket_routes.py")
    
    return True

def create_environment_file():
    """Create .env file with webhook secrets"""
    print("🔐 Creating Environment Configuration...")
    
    env_content = '''# Allora Backend Environment Configuration
# Generated by fix_webhook_websocket.py

# Database Configuration
DATABASE_URL=mysql://root:password@localhost:3306/allora_db

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Webhook Secrets (CHANGE THESE IN PRODUCTION!)
BLUE_DART_WEBHOOK_SECRET=your_secure_blue_dart_secret_key_here
DELHIVERY_WEBHOOK_SECRET=your_secure_delhivery_secret_key_here
FEDEX_WEBHOOK_SECRET=your_secure_fedex_secret_key_here

# Flask Configuration
FLASK_ENV=development
SECRET_KEY=your_flask_secret_key_here

# SocketIO Configuration
SOCKETIO_ASYNC_MODE=threading
SOCKETIO_CORS_ALLOWED_ORIGINS=*

# Logging Configuration
LOG_LEVEL=INFO
'''
    
    env_file = ".env"
    
    if not os.path.exists(env_file):
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(env_content)
        print("✅ Created .env file with webhook secrets")
    else:
        print("⚠️  .env file already exists - skipping creation")
    
    return True

def update_app_integration():
    """Update app.py to integrate Flask-SocketIO"""
    print("🔗 Updating App Integration...")
    
    app_py_path = "app.py"
    
    if not os.path.exists(app_py_path):
        print("❌ app.py not found")
        return False
    
    with open(app_py_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check if SocketIO is already integrated
    if 'flask_socketio_manager' in content:
        print("✅ Flask-SocketIO already integrated")
        return True
    
    # Find the imports section
    if 'from flask import Flask' in content:
        # Add SocketIO integration after Flask import
        socketio_integration = '''
# Flask-SocketIO Integration
try:
    from flask_socketio_manager import init_socketio, socketio_manager
    SOCKETIO_AVAILABLE = True
except ImportError:
    SOCKETIO_AVAILABLE = False
    logger.warning("Flask-SocketIO not available - real-time features disabled")
'''
        
        # Insert after Flask import
        insertion_point = content.find('from flask import Flask')
        if insertion_point != -1:
            line_end = content.find('\n', insertion_point)
            new_content = content[:line_end] + socketio_integration + content[line_end:]
            
            # Add SocketIO initialization after app creation
            if 'app = Flask(__name__)' in new_content:
                app_creation_point = new_content.find('app = Flask(__name__)') + len('app = Flask(__name__)')
                socketio_init = '''

# Initialize SocketIO if available
if SOCKETIO_AVAILABLE:
    try:
        socketio = init_socketio(app)
        logger.info("✅ Flask-SocketIO initialized successfully")
    except Exception as e:
        logger.error(f"❌ Flask-SocketIO initialization failed: {e}")
        socketio = None
else:
    socketio = None
'''
                
                new_content = new_content[:app_creation_point] + socketio_init + new_content[app_creation_point:]
            
            # Write updated content
            with open(app_py_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("✅ Flask-SocketIO integration added to app.py")
            return True
    
    print("❌ Could not integrate Flask-SocketIO with app.py")
    return False

def create_test_script():
    """Create a test script for the fixed functionality"""
    print("🧪 Creating Test Script...")
    
    test_content = '''#!/usr/bin/env python3
"""
Test Script for Fixed Webhook and WebSocket Functionality
"""

import requests
import json
import time

def test_webhook_endpoints():
    """Test webhook endpoints"""
    print("🔍 Testing Webhook Endpoints...")
    
    base_url = "http://localhost:5000"
    
    # Test data
    test_data = {
        "carrier": "blue_dart",
        "tracking_number": "TEST123456",
        "status": "in_transit",
        "timestamp": "2025-07-13T10:00:00Z",
        "event_type": "status_update"
    }
    
    endpoints = [
        "/api/webhooks/test",
        "/api/webhooks/blue-dart",
        "/api/webhooks/delhivery",
        "/api/webhooks/fedex"
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.post(
                f"{base_url}{endpoint}",
                json=test_data,
                timeout=5
            )
            print(f"✅ {endpoint}: Status {response.status_code}")
            if response.status_code == 200:
                print(f"   Response: {response.json()}")
        except requests.exceptions.ConnectionError:
            print(f"⚠️  {endpoint}: Server not running")
        except Exception as e:
            print(f"❌ {endpoint}: Error {e}")

def test_socketio_endpoints():
    """Test SocketIO API endpoints"""
    print("\\n🔍 Testing SocketIO Endpoints...")
    
    base_url = "http://localhost:5000"
    
    endpoints = [
        "/api/socketio/health",
        "/api/socketio/connections"
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            print(f"✅ {endpoint}: Status {response.status_code}")
            if response.status_code == 200:
                print(f"   Response: {response.json()}")
        except requests.exceptions.ConnectionError:
            print(f"⚠️  {endpoint}: Server not running")
        except Exception as e:
            print(f"❌ {endpoint}: Error {e}")

def main():
    """Main test function"""
    print("🧪 Testing Fixed Webhook and WebSocket Functionality")
    print("=" * 60)
    
    print("📋 Prerequisites:")
    print("1. Start server: python run_with_waitress.py")
    print("2. Ensure Redis is running")
    print("3. Ensure MySQL is running")
    print()
    
    # Wait for user confirmation
    input("Press Enter when server is running...")
    
    # Run tests
    test_webhook_endpoints()
    test_socketio_endpoints()
    
    print("\\n✅ Testing completed!")

if __name__ == '__main__':
    main()
'''
    
    with open('test_fixed_functionality.py', 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    print("✅ Test script created: test_fixed_functionality.py")
    return True

def main():
    """Main fix function"""
    print("🔧 Webhook and WebSocket Fix Script")
    print("=" * 50)
    
    steps = [
        ("Installing Dependencies", install_dependencies),
        ("Fixing WebSocket Imports", fix_websocket_imports),
        ("Creating Environment File", create_environment_file),
        ("Updating App Integration", update_app_integration),
        ("Creating Test Script", create_test_script)
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        print(f"\\n🔄 {step_name}...")
        try:
            if step_func():
                success_count += 1
                print(f"✅ {step_name} completed")
            else:
                print(f"❌ {step_name} failed")
        except Exception as e:
            print(f"❌ {step_name} failed with error: {e}")
    
    # Summary
    print("\\n" + "=" * 50)
    print(f"📊 Fix Summary: {success_count}/{len(steps)} steps completed")
    
    if success_count == len(steps):
        print("🎉 All fixes applied successfully!")
        print("\\n📋 Next Steps:")
        print("1. Restart the server: python run_with_waitress.py")
        print("2. Test functionality: python test_fixed_functionality.py")
        print("3. Configure webhook secrets in .env file")
        print("4. Test WebSocket connection from frontend")
    elif success_count >= len(steps) * 0.7:
        print("✅ Most fixes applied - minor issues may remain")
    else:
        print("⚠️  Several fixes failed - manual intervention required")
    
    return success_count == len(steps)

if __name__ == '__main__':
    import time
    main()
