#!/usr/bin/env python3
"""
Simple test script to check Flask routes
"""
import os
import sys

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import app
    
    print("Flask app imported successfully!")
    print(f"App name: {app.name}")
    print(f"Debug mode: {app.debug}")
    
    # List all registered routes
    print("\n=== REGISTERED ROUTES ===")
    for rule in app.url_map.iter_rules():
        print(f"{rule.rule} -> {rule.endpoint} [{', '.join(rule.methods)}]")
    
    print(f"\nTotal routes: {len(list(app.url_map.iter_rules()))}")
    
    # Test the health endpoint specifically
    with app.test_client() as client:
        print("\n=== TESTING HEALTH ENDPOINT ===")
        response = client.get('/api/health')
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.get_data(as_text=True)}")
        
        print("\n=== TESTING ROOT ENDPOINT ===")
        response = client.get('/')
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.get_data(as_text=True)}")

except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
