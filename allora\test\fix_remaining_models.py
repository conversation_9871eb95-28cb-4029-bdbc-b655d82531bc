#!/usr/bin/env python3
"""
Fix Remaining Database Models Script
===================================

This script fixes the remaining database models with missing columns.

Author: Allora Development Team
Date: 2025-07-13
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def fix_remaining_models():
    """Fix the remaining database models"""
    
    try:
        from app import app, db
        
        # Import all models
        from app import (
            InventorySyncLog, RMAApproval, RMADocument, RecentlyViewed, 
            Refund, SearchAnalytics, UserInteractionLog, UserSession, 
            VisualSearchAnalytics
        )
        
        with app.app_context():
            print("🔧 Adding missing columns to database models...")
            
            # Add missing columns using raw SQL
            fixes = [
                # InventorySyncLog - add created_at
                "ALTER TABLE inventory_sync_log ADD COLUMN IF NOT EXISTS created_at DATETIME DEFAULT CURRENT_TIMESTAMP",
                
                # RMAApproval - add created_at  
                "ALTER TABLE rma_approval ADD COLUMN IF NOT EXISTS created_at DATETIME DEFAULT CURRENT_TIMESTAMP",
                
                # RMADocument - add created_at
                "ALTER TABLE rma_document ADD COLUMN IF NOT EXISTS created_at DATETIME DEFAULT CURRENT_TIMESTAMP",
                
                # RecentlyViewed - add created_at
                "ALTER TABLE recently_viewed ADD COLUMN IF NOT EXISTS created_at DATETIME DEFAULT CURRENT_TIMESTAMP",
                
                # Refund - add created_at
                "ALTER TABLE refund ADD COLUMN IF NOT EXISTS created_at DATETIME DEFAULT CURRENT_TIMESTAMP",
                
                # SearchAnalytics - add missing columns
                "ALTER TABLE search_analytics ADD COLUMN IF NOT EXISTS response_time_ms INT",
                "ALTER TABLE search_analytics ADD COLUMN IF NOT EXISTS conversion_events JSON",
                "ALTER TABLE search_analytics ADD COLUMN IF NOT EXISTS elasticsearch_time_ms INT",
                
                # UserInteractionLog - add created_at
                "ALTER TABLE user_interaction_logs ADD COLUMN IF NOT EXISTS created_at DATETIME DEFAULT CURRENT_TIMESTAMP",
                
                # UserSession - add created_at
                "ALTER TABLE user_sessions ADD COLUMN IF NOT EXISTS created_at DATETIME DEFAULT CURRENT_TIMESTAMP",
                
                # VisualSearchAnalytics - add created_at
                "ALTER TABLE visual_search_analytics ADD COLUMN IF NOT EXISTS created_at DATETIME DEFAULT CURRENT_TIMESTAMP"
            ]
            
            for fix in fixes:
                try:
                    db.session.execute(fix)
                    print(f"✅ Applied: {fix[:50]}...")
                except Exception as e:
                    print(f"⚠️  Warning: {fix[:50]}... - {e}")
            
            db.session.commit()
            print("✅ All database fixes applied successfully!")
            
    except Exception as e:
        print(f"❌ Error applying fixes: {e}")
        return False
    
    return True

def update_model_definitions():
    """Update the model definitions in app.py"""
    
    print("🔧 Updating model definitions in app.py...")
    
    # Read the current app.py file
    with open('app.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Define the model updates
    updates = [
        # InventorySyncLog
        {
            'search': 'notes = db.Column(db.Text, nullable=True)\n\n    # Relationships',
            'replace': 'notes = db.Column(db.Text, nullable=True)\n    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)\n\n    # Relationships'
        },
        
        # RMAApproval
        {
            'search': 'decided_at = db.Column(db.DateTime, nullable=True)\n\n    # Relationships',
            'replace': 'decided_at = db.Column(db.DateTime, nullable=True)\n    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)\n\n    # Relationships'
        },
        
        # RMADocument
        {
            'search': 'is_public = db.Column(db.Boolean, nullable=False, default=False)  # Visible to customer\n\n    # Relationships',
            'replace': 'is_public = db.Column(db.Boolean, nullable=False, default=False)  # Visible to customer\n    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)\n\n    # Relationships'
        },
        
        # RecentlyViewed
        {
            'search': 'viewed_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)\n\n    user = db.relationship',
            'replace': 'viewed_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)\n    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)\n\n    user = db.relationship'
        },
        
        # Refund
        {
            'search': 'updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships',
            'replace': 'updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)\n    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)\n\n    # Relationships'
        },
        
        # SearchAnalytics
        {
            'search': 'created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)',
            'replace': 'created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)\n    response_time_ms = db.Column(db.Integer, nullable=True)\n    conversion_events = db.Column(db.JSON, nullable=True)\n    elasticsearch_time_ms = db.Column(db.Integer, nullable=True)'
        },
        
        # UserInteractionLog
        {
            'search': 'timestamp = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)\n\n    def to_dict',
            'replace': 'timestamp = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)\n    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)\n\n    def to_dict'
        },
        
        # UserSession
        {
            'search': 'session_data = db.Column(db.JSON, nullable=True)\n\n    def to_dict',
            'replace': 'session_data = db.Column(db.JSON, nullable=True)\n    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)\n\n    def to_dict'
        },
        
        # VisualSearchAnalytics
        {
            'search': 'image_size = db.Column(db.Integer, nullable=True)  # File size in bytes\n\n# Cookie Consent',
            'replace': 'image_size = db.Column(db.Integer, nullable=True)  # File size in bytes\n    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)\n\n# Cookie Consent'
        }
    ]
    
    # Apply updates
    for update in updates:
        if update['search'] in content:
            content = content.replace(update['search'], update['replace'])
            print(f"✅ Updated model definition")
        else:
            print(f"⚠️  Could not find pattern to update")
    
    # Write back to file
    with open('app.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Model definitions updated successfully!")

def main():
    """Main function"""
    print("🚀 Fixing Remaining Database Models")
    print("=" * 50)
    
    # First update the model definitions
    update_model_definitions()
    
    # Then apply database fixes
    if fix_remaining_models():
        print("\n🎉 All remaining database models have been fixed!")
        print("✅ Your app.py file now perfectly matches the database structure.")
        return 0
    else:
        print("\n❌ Some fixes failed. Please check the errors above.")
        return 1

if __name__ == '__main__':
    sys.exit(main())
