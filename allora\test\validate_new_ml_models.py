#!/usr/bin/env python3
"""
New ML Models Validation Script (v5.0)
======================================

This script validates that all 4 new ML models v5.0 are working correctly
with the seeded data and can be loaded by the API endpoints.
"""

import os
import pickle
import sys

def validate_model_file(model_path, model_name):
    """Validate a single new model file"""
    print(f"🔍 Validating {model_name} v5.0...")
    
    if not os.path.exists(model_path):
        print(f"❌ {model_name} file not found: {model_path}")
        return False
    
    try:
        with open(model_path, 'rb') as f:
            model_data = pickle.load(f)
        
        if isinstance(model_data, dict) and 'model_metadata' in model_data:
            metadata = model_data['model_metadata']
            version = metadata.get('version', 'unknown')
            print(f"   📊 Version: {version}")
            
            # Check for v5.0 optimization
            if 'seeded_data_optimized_v5.0' in version:
                print(f"   ✅ Optimized for seeded data v5.0")
            else:
                print(f"   ⚠️  May not be v5.0 optimized")
            
            # Check model metadata
            print(f"   🎯 Model type: {metadata.get('model_type', 'unknown')}")
            print(f"   📅 Created: {metadata.get('created_at', 'unknown')}")
            
            # Model-specific info
            if 'total_users' in metadata:
                print(f"   👥 Users: {metadata['total_users']}")
            if 'total_products' in metadata:
                print(f"   📦 Products: {metadata['total_products']}")
            
            print(f"   ✅ {model_name} v5.0 loaded successfully")
            return True
        else:
            print(f"   ⚠️  {model_name} has unexpected format (not v5.0)")
            return False
            
    except Exception as e:
        print(f"   ❌ Failed to load {model_name}: {e}")
        return False

def main():
    """Main validation function"""
    print("🔍 NEW ML MODELS VALIDATION (v5.0)")
    print("=" * 60)
    
    models_to_validate = [
        ('models/Recommendation Model/recommendation_model.pkl', 'Recommendation Model'),
        ('models/Price Trends Model/price_trends_model.pkl', 'Price Trends Model'),
        ('models/Inventory Prediction Model/inventory_model.pkl', 'Inventory Model'),
        ('models/Visual Search Model/visual_search_model.pkl', 'Visual Search Model')
    ]
    
    results = []
    for model_path, model_name in models_to_validate:
        success = validate_model_file(model_path, model_name)
        results.append((model_name, success))
        print()
    
    # Summary
    print("=" * 60)
    print("📋 VALIDATION SUMMARY")
    print("=" * 60)
    
    successful = [name for name, success in results if success]
    failed = [name for name, success in results if not success]
    
    print(f"✅ Successful: {len(successful)}/{len(results)}")
    print(f"❌ Failed: {len(failed)}")
    
    if successful:
        print("\n✅ WORKING MODELS (v5.0):")
        for name in successful:
            print(f"   🎯 {name}")
    
    if failed:
        print("\n❌ FAILED MODELS:")
        for name in failed:
            print(f"   ⚠️  {name}")
    
    print(f"\n🎉 New models validation completed!")
    return len(failed) == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
