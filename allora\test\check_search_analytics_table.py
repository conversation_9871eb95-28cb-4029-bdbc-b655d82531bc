#!/usr/bin/env python3
"""
Search Analytics Table Checker
==============================

This script checks the search_analytics table for any structural issues,
data inconsistencies, or conflicts between different model definitions.

Features:
- Compares database table structure with model definitions
- Identifies missing columns or data type mismatches
- Checks for foreign key constraints
- Validates data integrity
- Provides fix recommendations

Usage:
    python check_search_analytics_table.py [--fix]
"""

import os
import sys
import argparse
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from sqlalchemy import inspect, text
from sqlalchemy.engine.reflection import Inspector

def check_search_analytics_table():
    """Check the search_analytics table for issues"""
    print("🔍 SEARCH ANALYTICS TABLE ANALYSIS")
    print("=" * 60)
    print(f"⏰ Analysis started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    with app.app_context():
        try:
            # Get database inspector
            inspector = inspect(db.engine)
            
            # Check if search_analytics table exists
            table_names = inspector.get_table_names()
            
            if 'search_analytics' not in table_names:
                print("❌ ERROR: search_analytics table does not exist!")
                print("\n🔧 RECOMMENDED ACTIONS:")
                print("1. Create the table using Flask-Migrate")
                print("2. Run: flask db migrate -m 'Create search_analytics table'")
                print("3. Run: flask db upgrade")
                return False
            
            print("✅ search_analytics table exists")
            
            # Get table structure
            columns = inspector.get_columns('search_analytics')
            indexes = inspector.get_indexes('search_analytics')
            foreign_keys = inspector.get_foreign_keys('search_analytics')
            pk_constraint = inspector.get_pk_constraint('search_analytics')
            
            print(f"\n📊 TABLE STRUCTURE ANALYSIS")
            print("-" * 40)
            print(f"Columns: {len(columns)}")
            print(f"Indexes: {len(indexes)}")
            print(f"Foreign Keys: {len(foreign_keys)}")
            
            # Analyze columns
            print(f"\n📋 COLUMN ANALYSIS")
            print("-" * 30)
            
            expected_columns = {
                'id': {'type': 'INTEGER', 'nullable': False, 'primary_key': True},
                'user_id': {'type': 'INTEGER', 'nullable': True, 'foreign_key': 'users.id'},
                'guest_session_id': {'type': 'VARCHAR', 'nullable': True},
                'search_query': {'type': 'VARCHAR', 'nullable': False},
                'search_type': {'type': 'VARCHAR', 'nullable': False},
                'results_count': {'type': 'INTEGER', 'nullable': False},
                'filters_applied': {'type': 'JSON', 'nullable': True},
                'clicked_results': {'type': 'JSON', 'nullable': True},
                'session_id': {'type': 'VARCHAR', 'nullable': True},
                'ip_address': {'type': 'VARCHAR', 'nullable': True},
                'user_agent': {'type': 'VARCHAR', 'nullable': True},
                'created_at': {'type': 'DATETIME', 'nullable': False}
            }
            
            actual_columns = {}
            issues = []
            
            for col in columns:
                col_name = col['name']
                col_type = str(col['type']).upper()
                col_nullable = col['nullable']
                
                actual_columns[col_name] = {
                    'type': col_type,
                    'nullable': col_nullable
                }
                
                print(f"  • {col_name:<20} {col_type:<15} {'NULL' if col_nullable else 'NOT NULL'}")
            
            # Check for missing columns
            missing_columns = set(expected_columns.keys()) - set(actual_columns.keys())
            if missing_columns:
                print(f"\n❌ MISSING COLUMNS ({len(missing_columns)}):")
                for col in missing_columns:
                    print(f"  • {col}")
                    issues.append(f"Missing column: {col}")
            
            # Check for extra columns
            extra_columns = set(actual_columns.keys()) - set(expected_columns.keys())
            if extra_columns:
                print(f"\n⚠️  EXTRA COLUMNS ({len(extra_columns)}):")
                for col in extra_columns:
                    print(f"  • {col}")
            
            # Check column types and constraints
            type_mismatches = []
            for col_name, expected in expected_columns.items():
                if col_name in actual_columns:
                    actual = actual_columns[col_name]
                    
                    # Check nullable constraint
                    if actual['nullable'] != expected['nullable']:
                        type_mismatches.append(f"{col_name}: nullable mismatch (expected: {expected['nullable']}, actual: {actual['nullable']})")
                    
                    # Check basic type compatibility
                    expected_type = expected['type']
                    actual_type = actual['type']
                    
                    if not _types_compatible(expected_type, actual_type):
                        type_mismatches.append(f"{col_name}: type mismatch (expected: {expected_type}, actual: {actual_type})")
            
            if type_mismatches:
                print(f"\n❌ TYPE MISMATCHES ({len(type_mismatches)}):")
                for mismatch in type_mismatches:
                    print(f"  • {mismatch}")
                    issues.append(mismatch)
            
            # Check primary key
            print(f"\n🔑 PRIMARY KEY ANALYSIS")
            print("-" * 25)
            if pk_constraint and pk_constraint.get('constrained_columns'):
                pk_cols = pk_constraint['constrained_columns']
                print(f"Primary Key: {', '.join(pk_cols)}")
                if pk_cols != ['id']:
                    issues.append(f"Primary key mismatch: expected ['id'], actual {pk_cols}")
            else:
                print("❌ No primary key found!")
                issues.append("Missing primary key")
            
            # Check foreign keys
            print(f"\n🔗 FOREIGN KEY ANALYSIS")
            print("-" * 25)
            expected_fks = {'user_id': 'users.id'}
            actual_fks = {}
            
            for fk in foreign_keys:
                local_cols = fk['constrained_columns']
                ref_table = fk['referred_table']
                ref_cols = fk['referred_columns']
                
                for i, local_col in enumerate(local_cols):
                    actual_fks[local_col] = f"{ref_table}.{ref_cols[i]}"
                    print(f"  • {local_col} → {ref_table}.{ref_cols[i]}")
            
            # Check for missing foreign keys
            missing_fks = set(expected_fks.keys()) - set(actual_fks.keys())
            if missing_fks:
                print(f"\n❌ MISSING FOREIGN KEYS ({len(missing_fks)}):")
                for fk in missing_fks:
                    print(f"  • {fk} → {expected_fks[fk]}")
                    issues.append(f"Missing foreign key: {fk} → {expected_fks[fk]}")
            
            # Check indexes
            print(f"\n📇 INDEX ANALYSIS")
            print("-" * 20)
            if indexes:
                for idx in indexes:
                    cols = ', '.join(idx['column_names'])
                    unique = "UNIQUE " if idx['unique'] else ""
                    print(f"  • {unique}{idx['name']}: {cols}")
            else:
                print("⚠️  No indexes found (consider adding for performance)")
            
            # Check data integrity
            print(f"\n🔍 DATA INTEGRITY CHECK")
            print("-" * 30)
            
            with db.engine.connect() as connection:
                # Check row count
                result = connection.execute(text("SELECT COUNT(*) FROM search_analytics"))
                row_count = result.fetchone()[0]
                print(f"Total rows: {row_count:,}")
                
                if row_count > 0:
                    # Check for null values in required fields
                    null_checks = [
                        ("search_query", "SELECT COUNT(*) FROM search_analytics WHERE search_query IS NULL OR search_query = ''"),
                        ("search_type", "SELECT COUNT(*) FROM search_analytics WHERE search_type IS NULL OR search_type = ''"),
                        ("results_count", "SELECT COUNT(*) FROM search_analytics WHERE results_count IS NULL"),
                        ("created_at", "SELECT COUNT(*) FROM search_analytics WHERE created_at IS NULL")
                    ]
                    
                    for field, query in null_checks:
                        result = connection.execute(text(query))
                        null_count = result.fetchone()[0]
                        if null_count > 0:
                            print(f"⚠️  {field}: {null_count} null/empty values")
                            issues.append(f"Data integrity: {null_count} null/empty values in {field}")
                        else:
                            print(f"✅ {field}: No null/empty values")
                    
                    # Check for orphaned records (user_id references non-existent users)
                    orphan_query = """
                    SELECT COUNT(*) FROM search_analytics sa 
                    LEFT JOIN users u ON sa.user_id = u.id 
                    WHERE sa.user_id IS NOT NULL AND u.id IS NULL
                    """
                    result = connection.execute(text(orphan_query))
                    orphan_count = result.fetchone()[0]
                    if orphan_count > 0:
                        print(f"⚠️  Orphaned records: {orphan_count} records with invalid user_id")
                        issues.append(f"Data integrity: {orphan_count} orphaned records")
                    else:
                        print("✅ No orphaned records found")
            
            # Summary
            print(f"\n📊 ANALYSIS SUMMARY")
            print("=" * 30)
            if issues:
                print(f"❌ ISSUES FOUND: {len(issues)}")
                for i, issue in enumerate(issues, 1):
                    print(f"  {i}. {issue}")
                
                print(f"\n🔧 RECOMMENDED FIXES:")
                print("1. Create database migration to fix structural issues")
                print("2. Clean up data integrity issues")
                print("3. Add missing indexes for performance")
                print("4. Consider adding constraints for data validation")
                
                return False
            else:
                print("✅ NO ISSUES FOUND - Table structure is correct!")
                return True
                
        except Exception as e:
            print(f"❌ Error during analysis: {e}")
            return False

def _types_compatible(expected_type, actual_type):
    """Check if database types are compatible"""
    # Normalize types for comparison
    type_mappings = {
        'VARCHAR': ['VARCHAR', 'TEXT', 'STRING'],
        'INTEGER': ['INTEGER', 'INT', 'BIGINT'],
        'DATETIME': ['DATETIME', 'TIMESTAMP'],
        'JSON': ['JSON', 'TEXT']
    }
    
    for base_type, variants in type_mappings.items():
        if expected_type in variants and any(variant in actual_type for variant in variants):
            return True
    
    return expected_type in actual_type or actual_type in expected_type

def fix_search_analytics_table():
    """Generate SQL to fix search_analytics table issues"""
    print("\n🔧 GENERATING FIX SCRIPT")
    print("=" * 30)
    
    # This would generate the appropriate ALTER TABLE statements
    # For now, we'll provide manual instructions
    
    print("To fix the search_analytics table, run the following:")
    print("\n1. Create a new migration:")
    print("   flask db migrate -m 'Fix search_analytics table structure'")
    
    print("\n2. Review and edit the migration file if needed")
    
    print("\n3. Apply the migration:")
    print("   flask db upgrade")
    
    print("\n4. Verify the changes:")
    print("   python check_search_analytics_table.py")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Check search_analytics table structure')
    parser.add_argument('--fix', action='store_true', help='Generate fix recommendations')
    
    args = parser.parse_args()
    
    # Run analysis
    success = check_search_analytics_table()
    
    if not success and args.fix:
        fix_search_analytics_table()
    
    print(f"\n⏰ Analysis completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
