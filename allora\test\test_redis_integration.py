#!/usr/bin/env python3
"""
Redis Integration Testing Script
===============================

This script tests the improved Redis integration with centralized configuration,
caching, and session management.

Author: Allora Development Team
Date: 2025-07-13
"""

import os
import sys
import json
import time
import uuid
import requests
from datetime import datetime

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_centralized_redis_config():
    """Test centralized Redis configuration"""
    print("🔍 Testing Centralized Redis Configuration...")
    try:
        from redis_config import get_redis_config, get_redis_cache, get_session_manager
        
        # Test configuration
        config = get_redis_config()
        print(f"✅ Redis Config - Host: {config.host}, Port: {config.port}")
        print(f"✅ Redis Available: {config.is_available()}")
        
        # Test cache
        cache = get_redis_cache()
        test_key = f"integration_test_{int(time.time())}"
        cache.set(test_key, "test_value", expire=60)
        value = cache.get(test_key)
        cache.delete(test_key)
        print(f"✅ Cache Test: {value == 'test_value'}")
        
        # Test session manager
        session_mgr = get_session_manager()
        session_data = {'test': 'session_data'}
        session_mgr.set_session('test_session', session_data)
        retrieved = session_mgr.get_session('test_session')
        session_mgr.delete_session('test_session')
        print(f"✅ Session Test: {retrieved == session_data}")
        
        return True
    except Exception as e:
        print(f"❌ Centralized Config Failed: {e}")
        return False

def test_jwt_blacklisting_integration():
    """Test improved JWT blacklisting"""
    print("\n🔍 Testing JWT Blacklisting Integration...")
    try:
        from app import is_token_blacklisted, blacklist_token
        
        # Test with a fake JTI
        test_jti = str(uuid.uuid4())
        
        # Check initial state
        is_blacklisted_before = is_token_blacklisted(test_jti)
        print(f"✅ Initial blacklist check: {not is_blacklisted_before}")
        
        # Blacklist the token
        blacklist_result = blacklist_token(test_jti)
        print(f"✅ Token blacklisting: {blacklist_result}")
        
        # Check if blacklisted
        is_blacklisted_after = is_token_blacklisted(test_jti)
        print(f"✅ Post-blacklist check: {is_blacklisted_after}")
        
        return blacklist_result and is_blacklisted_after
    except Exception as e:
        print(f"❌ JWT Blacklisting Failed: {e}")
        return False

def test_fallback_mechanisms():
    """Test Redis fallback mechanisms"""
    print("\n🔍 Testing Fallback Mechanisms...")
    try:
        from redis_config import get_redis_cache
        
        cache = get_redis_cache()
        
        # Test normal operation
        cache.set('fallback_test', 'normal_operation')
        value1 = cache.get('fallback_test')
        print(f"✅ Normal operation: {value1 == 'normal_operation'}")
        
        # Simulate Redis failure by using a bad config
        from redis_config import RedisCache, RedisConfig
        bad_config = RedisConfig()
        bad_config.host = 'nonexistent_host'
        bad_config.port = 9999
        fallback_cache = RedisCache(bad_config)
        
        # This should use in-memory fallback
        fallback_cache.set('fallback_test', 'fallback_operation')
        value2 = fallback_cache.get('fallback_test')
        print(f"✅ Fallback operation: {value2 == 'fallback_operation'}")
        
        # Clean up
        cache.delete('fallback_test')
        
        return True
    except Exception as e:
        print(f"❌ Fallback Test Failed: {e}")
        return False

def test_api_endpoints():
    """Test new Redis API endpoints"""
    print("\n🔍 Testing Redis API Endpoints...")
    
    # Note: This requires the Flask app to be running
    base_url = "http://localhost:5000"
    
    try:
        # Test cache endpoint
        response = requests.get(f"{base_url}/api/cache/test", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Cache API: {data.get('status') == 'success'}")
        else:
            print(f"⚠️  Cache API: Server not running (status: {response.status_code})")
        
        # Test session endpoint
        response = requests.get(f"{base_url}/api/session/test", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Session API: {data.get('status') == 'success'}")
        else:
            print(f"⚠️  Session API: Server not running (status: {response.status_code})")
        
        return True
    except requests.exceptions.ConnectionError:
        print("⚠️  API Tests: Flask server not running - start with 'python app.py'")
        return False
    except Exception as e:
        print(f"❌ API Tests Failed: {e}")
        return False

def test_connection_consolidation():
    """Test that Redis connections are consolidated"""
    print("\n🔍 Testing Connection Consolidation...")
    try:
        # Read app.py and analyze Redis usage
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Count Redis connection patterns
        import re
        redis_imports = len(re.findall(r'import redis', content))
        direct_connections = len(re.findall(r'redis\.Redis\(', content))
        redis_config_imports = len(re.findall(r'from redis_config import', content))
        
        print(f"📊 Redis imports: {redis_imports}")
        print(f"📊 Direct Redis.Redis() connections: {direct_connections}")
        print(f"📊 redis_config imports: {redis_config_imports}")
        
        # Check for improvement
        improved = redis_config_imports > 0 and direct_connections <= 2
        print(f"✅ Connection consolidation: {improved}")
        
        return improved
    except Exception as e:
        print(f"❌ Connection Analysis Failed: {e}")
        return False

def test_performance_improvement():
    """Test performance improvements with caching"""
    print("\n🔍 Testing Performance Improvements...")
    try:
        from redis_config import get_redis_cache
        
        cache = get_redis_cache()
        
        # Simulate database query caching
        start_time = time.time()
        
        # First call - cache miss (simulated)
        cache_key = "performance_test"
        cached_data = cache.get(cache_key)
        if not cached_data:
            # Simulate database query delay
            time.sleep(0.1)
            data = {"query_result": "expensive_operation", "timestamp": time.time()}
            cache.set(cache_key, data, expire=300)
            first_call_time = time.time() - start_time
        
        # Second call - cache hit
        start_time = time.time()
        cached_data = cache.get(cache_key)
        second_call_time = time.time() - start_time
        
        print(f"✅ First call (cache miss): {first_call_time:.4f}s")
        print(f"✅ Second call (cache hit): {second_call_time:.4f}s")
        print(f"✅ Performance improvement: {first_call_time > second_call_time}")
        
        # Clean up
        cache.delete(cache_key)
        
        return first_call_time > second_call_time
    except Exception as e:
        print(f"❌ Performance Test Failed: {e}")
        return False

def test_redis_health():
    """Test Redis health and monitoring"""
    print("\n🔍 Testing Redis Health Monitoring...")
    try:
        from redis_config import test_redis_connection
        
        result = test_redis_connection()
        print(f"✅ Redis Available: {result['available']}")
        
        if result['available']:
            info = result['info']
            print(f"✅ Redis Version: {info['version']}")
            print(f"✅ Memory Used: {info['memory_used']}")
            print(f"✅ Connected Clients: {info['connected_clients']}")
            print(f"✅ Uptime: {info['uptime']} seconds")
        else:
            print(f"❌ Redis Error: {result['error']}")
        
        return result['available']
    except Exception as e:
        print(f"❌ Health Test Failed: {e}")
        return False

def main():
    """Main testing function"""
    print("🚀 Redis Integration Testing Suite")
    print("=" * 60)
    
    results = {}
    
    # Run all tests
    results['centralized_config'] = test_centralized_redis_config()
    results['jwt_blacklisting'] = test_jwt_blacklisting_integration()
    results['fallback_mechanisms'] = test_fallback_mechanisms()
    results['api_endpoints'] = test_api_endpoints()
    results['connection_consolidation'] = test_connection_consolidation()
    results['performance_improvement'] = test_performance_improvement()
    results['redis_health'] = test_redis_health()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 INTEGRATION TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All Redis integration improvements are working perfectly!")
    elif passed >= total * 0.8:
        print("\n✅ Most Redis improvements are working - minor issues to address")
    else:
        print("\n⚠️  Several Redis integration issues need attention")
    
    return results

if __name__ == '__main__':
    main()
