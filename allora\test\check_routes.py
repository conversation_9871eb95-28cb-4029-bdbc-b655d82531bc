"""
Check what routes are registered in the Flask app
"""
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("Importing Flask app...")
    from app import app
    
    print("✅ Flask app imported successfully")
    
    with app.app_context():
        print(f"\n📋 Registered routes in Flask app:")
        print(f"Total routes: {len(app.url_map._rules)}")
        
        # Group routes by endpoint
        routes_by_method = {}
        for rule in app.url_map.iter_rules():
            methods = ', '.join(sorted(rule.methods - {'HEAD', 'OPTIONS'}))
            route_info = f"{methods} {rule.rule}"
            
            if methods not in routes_by_method:
                routes_by_method[methods] = []
            routes_by_method[methods].append(rule.rule)
        
        # Print routes organized by HTTP method
        for method, routes in sorted(routes_by_method.items()):
            print(f"\n{method} routes ({len(routes)}):")
            for route in sorted(routes):
                print(f"  {route}")
        
        # Check specifically for /api/products
        products_routes = [rule for rule in app.url_map.iter_rules() if 'products' in rule.rule]
        print(f"\n🔍 Product-related routes ({len(products_routes)}):")
        for rule in products_routes:
            methods = ', '.join(sorted(rule.methods - {'HEAD', 'OPTIONS'}))
            print(f"  {methods} {rule.rule} -> {rule.endpoint}")
        
        # Check root route
        root_routes = [rule for rule in app.url_map.iter_rules() if rule.rule == '/']
        print(f"\n🏠 Root routes ({len(root_routes)}):")
        for rule in root_routes:
            methods = ', '.join(sorted(rule.methods - {'HEAD', 'OPTIONS'}))
            print(f"  {methods} {rule.rule} -> {rule.endpoint}")

except ImportError as e:
    print(f"❌ Failed to import Flask app: {e}")
    import traceback
    traceback.print_exc()
except Exception as e:
    print(f"❌ Error checking routes: {e}")
    import traceback
    traceback.print_exc()
