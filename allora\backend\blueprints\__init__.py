"""
Blueprints Package
==================

Organized Flask blueprints for the Allora E-commerce API.
This package contains all API endpoints organized by business domain.

Structure:
- auth/         - Authentication and authorization
- products/     - Product management
- orders/       - Order and cart management  
- users/        - User profile and management
- payments/     - Payment processing
- community/    - Community features
- analytics/    - Analytics and reporting
- admin/        - Admin operations
- search/       - Search functionality (existing)
- fulfillment/  - Order fulfillment (existing)
- rma/          - Return management (existing)
- sustainability/ - Sustainability features (existing)
"""

from .auth import auth_bp
from .products import products_bp
from .orders import orders_bp
from .users import users_bp
from .payments import payments_bp
from .community import community_bp
from .analytics import analytics_bp
from .admin import admin_bp

# Import existing blueprints
from ..search_system.search_api import search_bp
from ..order_fulfillment.fulfillment_api import fulfillment_bp
from ..rma.rma_api import rma_bp
from ..sustainability_api import sustainability_bp
from ..community_highlights_api import community_highlights_bp

__all__ = [
    # New organized blueprints
    'auth_bp',
    'products_bp', 
    'orders_bp',
    'users_bp',
    'payments_bp',
    'community_bp',
    'analytics_bp',
    'admin_bp',
    
    # Existing blueprints
    'search_bp',
    'fulfillment_bp',
    'rma_bp',
    'sustainability_bp',
    'community_highlights_bp'
]
