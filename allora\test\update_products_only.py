#!/usr/bin/env python3
"""
Update only products with sustainable names and images
"""

import os
import sys
import random
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, Product, Seller
from seed_database import DatabaseSeeder

def update_products_with_sustainable_names():
    """Update existing products with sustainable names"""
    print("🌱 Updating products with sustainable names...")
    
    with app.app_context():
        # Get existing products and sellers
        products = Product.query.all()
        sellers = Seller.query.all()
        
        if not products:
            print("❌ No products found in database. Run seed_database.py first.")
            return False
            
        if not sellers:
            print("❌ No sellers found in database. Run seed_database.py first.")
            return False
        
        print(f"📦 Found {len(products)} products to update")
        
        # Initialize seeder to get sustainable product data
        seeder = DatabaseSeeder()
        
        # Update each product
        for i, product in enumerate(products):
            # Select random category and get sustainable product name
            main_category = random.choice(list(seeder.categories.keys()))
            brand = random.choice(seeder.brands.get(main_category, ['EcoChoice', 'GreenLiving', 'Sustainable']))
            
            # Get realistic sustainable product name
            product_templates = seeder.sustainable_products.get(main_category, ['Eco-Friendly Product'])
            base_product_name = random.choice(product_templates)
            
            # Create full product name with brand
            new_name = f"{brand} {base_product_name}"
            if len(new_name) > 100:
                new_name = new_name[:97] + "..."
            
            # Generate sustainable description
            sustainability_features = [
                "Made from recycled materials", "Carbon-neutral shipping", "Biodegradable packaging",
                "Fair trade certified", "Organic materials", "Renewable energy production",
                "Zero waste manufacturing", "Ethically sourced", "Plastic-free packaging",
                "Sustainably harvested", "Eco-friendly production", "Minimal environmental impact"
            ]
            
            sustainability_feature = random.choice(sustainability_features)
            new_description = f"Sustainable {base_product_name.lower()} designed for environmentally conscious consumers. {sustainability_feature}. Perfect for those who care about the planet while enjoying quality products."
            
            # Set appropriate materials based on category
            sustainable_materials = {
                'Electronics': ['Recycled Aluminum', 'Bioplastic', 'Recycled Steel', 'Sustainable Silicon'],
                'Fashion': ['Organic Cotton', 'Hemp', 'Bamboo Fiber', 'Recycled Polyester', 'Linen', 'Tencel'],
                'Home & Garden': ['Bamboo', 'Recycled Wood', 'Organic Cotton', 'Cork', 'Recycled Glass'],
                'Sports & Outdoors': ['Recycled Polyester', 'Organic Cotton', 'Natural Rubber', 'Hemp'],
                'Health & Beauty': ['Organic Ingredients', 'Natural Materials', 'Biodegradable Components'],
                'Food & Beverages': ['Organic', 'Fair Trade', 'Natural', 'Non-GMO'],
                'Baby & Kids': ['Organic Cotton', 'Natural Wood', 'BPA-Free Materials', 'Non-toxic Materials'],
                'Books & Media': ['Recycled Paper', 'Soy-based Ink', 'Sustainable Materials'],
                'Toys & Games': ['Sustainable Wood', 'Organic Cotton', 'Recycled Plastic', 'Natural Materials'],
                'Automotive': ['Recycled Materials', 'Bio-based Components', 'Sustainable Composites']
            }
            
            new_material = random.choice(sustainable_materials.get(main_category, ['Eco-Friendly Materials']))
            
            # Generate category-appropriate image
            category_seed = hash(main_category + str(i)) % 1000
            new_image = f"https://picsum.photos/400/400?random={category_seed}"
            
            # Update product
            product.name = new_name
            product.description = new_description
            product.category = main_category
            product.brand = brand
            product.material = new_material
            product.image = new_image
            product.sustainability_score = random.randint(70, 100)  # Higher sustainability scores
            product.sku = f"ECO-{product.sku.split('-')[-1]}"  # Keep original ID but add ECO prefix
            product.care_instructions = f"Eco-friendly care: {product.care_instructions}"
            
            # Slightly adjust price for sustainable products
            if product.price < 50:
                product.price = round(product.price * 1.1, 2)  # 10% premium for sustainability
            
            print(f"  ✅ Updated: {new_name}")
        
        # Commit all changes
        db.session.commit()
        print(f"🎉 Successfully updated {len(products)} products with sustainable names!")
        
        # Show sample of updated products
        print("\n📋 Sample of updated products:")
        sample_products = Product.query.limit(5).all()
        for product in sample_products:
            print(f"  - {product.name} ({product.category}) - ${product.price}")
        
        return True

def main():
    """Main function"""
    try:
        success = update_products_with_sustainable_names()
        if success:
            print("\n🔄 Next steps:")
            print("1. Run: python reindex_products.py")
            print("2. Test autocomplete with queries like 'organic', 'bamboo', 'eco', 'sustainable'")
        return success
    except Exception as e:
        print(f"❌ Error updating products: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
