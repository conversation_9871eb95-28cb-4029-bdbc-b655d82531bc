#!/usr/bin/env python3
"""
Quick Backend Testing Script
===========================

This script runs essential backend tests to verify functionality.
Use this for rapid testing during development.
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://localhost:5000"

def print_header(title):
    """Print a formatted header"""
    print(f"\n{'='*60}")
    print(f"🧪 {title}")
    print(f"{'='*60}")

def print_result(test_name, success, details=""):
    """Print test result"""
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status} {test_name}")
    if details:
        print(f"    {details}")

def test_server_health():
    """Test if server is running and healthy"""
    print_header("SERVER HEALTH CHECK")
    
    try:
        response = requests.get(f"{BASE_URL}/api/health", timeout=5)
        success = response.status_code == 200
        print_result("Server Health", success, f"Status: {response.status_code}")
        return success
    except Exception as e:
        print_result("Server Health", False, f"Error: {e}")
        return False

def test_basic_endpoints():
    """Test basic endpoints that should always work"""
    print_header("BASIC ENDPOINTS")
    
    endpoints = [
        ("GET", "/api/products", "Product Listing"),
        ("GET", "/api/categories", "Categories"),
        ("GET", "/sitemap.xml", "Sitemap"),
        ("GET", "/api/search?q=test", "Basic Search"),
    ]
    
    results = []
    for method, endpoint, name in endpoints:
        try:
            response = requests.request(method, f"{BASE_URL}{endpoint}", timeout=5)
            success = response.status_code in [200, 201]
            print_result(name, success, f"Status: {response.status_code}")
            results.append(success)
        except Exception as e:
            print_result(name, False, f"Error: {e}")
            results.append(False)
    
    return results

def test_authentication():
    """Test authentication system"""
    print_header("AUTHENTICATION SYSTEM")
    
    # Test user registration
    test_user = {
        "username": f"testuser_{int(time.time())}",
        "email": f"test_{int(time.time())}@example.com",
        "password": "testpass123",
        "full_name": "Test User"
    }
    
    try:
        # Register user
        response = requests.post(
            f"{BASE_URL}/api/register",
            json=test_user,
            timeout=5
        )
        register_success = response.status_code in [200, 201]
        print_result("User Registration", register_success, f"Status: {response.status_code}")
        
        if register_success:
            # Try to login
            login_response = requests.post(
                f"{BASE_URL}/api/login",
                json={
                    "email": test_user["email"],
                    "password": test_user["password"]
                },
                timeout=5
            )
            login_success = login_response.status_code == 200
            print_result("User Login", login_success, f"Status: {login_response.status_code}")
            
            if login_success:
                try:
                    token = login_response.json().get('access_token')
                    if token:
                        print_result("Token Received", True, "JWT token obtained")
                        return token
                    else:
                        print_result("Token Received", False, "No token in response")
                except:
                    print_result("Token Received", False, "Invalid JSON response")
        
        return None
        
    except Exception as e:
        print_result("Authentication Test", False, f"Error: {e}")
        return None

def test_protected_endpoints(token):
    """Test endpoints that require authentication"""
    print_header("PROTECTED ENDPOINTS")
    
    if not token:
        print_result("Protected Endpoints", False, "No token available")
        return []
    
    headers = {"Authorization": f"Bearer {token}"}
    endpoints = [
        ("GET", "/api/profile", "User Profile"),
        ("GET", "/api/cart", "Shopping Cart"),
        ("GET", "/api/wishlist", "Wishlist"),
        ("GET", "/api/orders", "Order History"),
        ("GET", "/api/addresses", "User Addresses"),
    ]
    
    results = []
    for method, endpoint, name in endpoints:
        try:
            response = requests.request(
                method, 
                f"{BASE_URL}{endpoint}", 
                headers=headers, 
                timeout=5
            )
            success = response.status_code in [200, 201]
            print_result(name, success, f"Status: {response.status_code}")
            results.append(success)
        except Exception as e:
            print_result(name, False, f"Error: {e}")
            results.append(False)
    
    return results

def test_admin_endpoints():
    """Test admin endpoints"""
    print_header("ADMIN ENDPOINTS")
    
    endpoints = [
        ("GET", "/api/admin/dashboard", "Admin Dashboard"),
        ("GET", "/api/admin/users", "User Management"),
        ("GET", "/api/admin/products", "Product Management"),
    ]
    
    results = []
    for method, endpoint, name in endpoints:
        try:
            response = requests.request(method, f"{BASE_URL}{endpoint}", timeout=5)
            success = response.status_code in [200, 401]  # 401 is expected without auth
            print_result(name, success, f"Status: {response.status_code}")
            results.append(success)
        except Exception as e:
            print_result(name, False, f"Error: {e}")
            results.append(False)
    
    return results

def generate_summary(all_results):
    """Generate test summary"""
    print_header("TEST SUMMARY")
    
    total_tests = sum(len(results) for results in all_results.values())
    passed_tests = sum(sum(results) for results in all_results.values())
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    print(f"📊 Total Tests: {total_tests}")
    print(f"✅ Passed: {passed_tests}")
    print(f"❌ Failed: {total_tests - passed_tests}")
    print(f"📈 Success Rate: {success_rate:.1f}%")
    print(f"🕒 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Category breakdown
    print(f"\n📋 Results by Category:")
    for category, results in all_results.items():
        if results:
            category_success = (sum(results) / len(results) * 100)
            print(f"   {category}: {sum(results)}/{len(results)} ({category_success:.1f}%)")
    
    # Overall status
    if success_rate >= 90:
        status = "🟢 EXCELLENT - Production Ready"
    elif success_rate >= 80:
        status = "🟡 GOOD - Minor Issues"
    elif success_rate >= 70:
        status = "🟠 FAIR - Needs Work"
    else:
        status = "🔴 POOR - Major Issues"
    
    print(f"\n🎯 Overall Status: {status}")
    
    return success_rate

def main():
    """Run all tests"""
    print("🚀 ALLORA BACKEND QUICK TESTING")
    print(f"Testing backend at: {BASE_URL}")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    all_results = {}
    
    # Run tests
    server_healthy = test_server_health()
    if not server_healthy:
        print("\n❌ Server is not running or unhealthy. Please start the backend first.")
        print("Run: cd allora/backend && python run_with_waitress.py")
        return
    
    all_results["Basic Endpoints"] = test_basic_endpoints()
    
    token = test_authentication()
    all_results["Authentication"] = [token is not None]
    
    all_results["Protected Endpoints"] = test_protected_endpoints(token)
    all_results["Admin Endpoints"] = test_admin_endpoints()
    
    # Generate summary
    success_rate = generate_summary(all_results)
    
    # Recommendations
    print(f"\n💡 Recommendations:")
    if success_rate < 80:
        print("   - Run comprehensive tests: python test_all_endpoints.py")
        print("   - Check database connectivity: python test_db.py")
        print("   - Review error logs for specific issues")
    else:
        print("   - Backend is in good shape!")
        print("   - Consider running full test suite for production readiness")
    
    print(f"\n📋 For detailed testing guide, see: COMPREHENSIVE_BACKEND_TESTING_GUIDE.md")

if __name__ == "__main__":
    main()
