"""
Payments Routes
===============

Payment processing endpoints with consistent response format and URL patterns.
"""

from flask import request
from datetime import datetime
import logging

from ..common.versioning import create_versioned_blueprint
from ..common.response_wrapper import (
    success_response, error_response, paginated_response, 
    not_found_response, validation_error_response
)
from ..common.auth_decorators import jwt_required_v2, rate_limit_v2, validate_content_type
from ..common.validators import (
    validate_json, validate_required_fields, validate_pagination,
    validate_price
)

logger = logging.getLogger(__name__)

# Create versioned blueprint
payments_bp = create_versioned_blueprint('payments', __name__, url_prefix='/payments')

@payments_bp.route('/methods', methods=['GET'])
@jwt_required_v2()
def get_payment_methods(user):
    """
    Get user's saved payment methods.
    
    GET /api/v1/payments/methods
    """
    try:
        from app import PaymentMethod
        
        payment_methods = PaymentMethod.query.filter_by(
            user_id=user.id,
            is_active=True
        ).order_by(PaymentMethod.is_default.desc(), PaymentMethod.created_at.desc()).all()
        
        methods_data = []
        for method in payment_methods:
            methods_data.append({
                "id": method.id,
                "type": method.payment_type,  # 'card', 'bank', 'wallet', etc.
                "provider": method.provider,  # 'stripe', 'razorpay', etc.
                "last_four": getattr(method, 'last_four', None),
                "brand": getattr(method, 'brand', None),  # 'visa', 'mastercard', etc.
                "expiry_month": getattr(method, 'expiry_month', None),
                "expiry_year": getattr(method, 'expiry_year', None),
                "is_default": method.is_default,
                "is_verified": getattr(method, 'is_verified', False),
                "created_at": method.created_at.isoformat() if method.created_at else None
            })
        
        return success_response(
            data={"payment_methods": methods_data},
            message="Payment methods retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get payment methods error: {e}")
        return error_response(
            message="Failed to retrieve payment methods",
            status_code=500,
            error_code="PAYMENT_METHODS_FETCH_FAILED"
        )

@payments_bp.route('/methods', methods=['POST'])
@jwt_required_v2()
@validate_content_type()
@rate_limit_v2(limit=10, window=300, per='user')  # 10 payment method additions per 5 minutes
def add_payment_method(user):
    """
    Add new payment method.
    
    POST /api/v1/payments/methods
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data
        
        # Validate required fields
        required_fields = ['type', 'provider', 'token']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp
        
        from app import db, PaymentMethod
        
        payment_type = data['type']
        provider = data['provider']
        token = data['token']  # Payment token from frontend
        is_default = data.get('is_default', False)
        
        # Validate payment type
        valid_types = ['card', 'bank', 'wallet', 'upi']
        if payment_type not in valid_types:
            return validation_error_response(
                errors={"type": [f"Invalid payment type. Must be one of: {', '.join(valid_types)}"]},
                message="Invalid payment type"
            )
        
        # Process payment method based on provider
        if provider == 'stripe':
            # Process Stripe payment method
            try:
                import stripe
                stripe.api_key = current_app.config.get('STRIPE_SECRET_KEY')
                
                # Create payment method in Stripe
                payment_method = stripe.PaymentMethod.retrieve(token)
                
                # Extract card details if it's a card
                card_details = {}
                if payment_method.type == 'card':
                    card = payment_method.card
                    card_details = {
                        'last_four': card.last4,
                        'brand': card.brand,
                        'expiry_month': card.exp_month,
                        'expiry_year': card.exp_year
                    }
                
            except Exception as stripe_error:
                logger.error(f"Stripe payment method error: {stripe_error}")
                return error_response(
                    message="Failed to process payment method with Stripe",
                    status_code=400,
                    error_code="STRIPE_PROCESSING_FAILED"
                )
        
        # If setting as default, unset other default payment methods
        if is_default:
            PaymentMethod.query.filter_by(user_id=user.id, is_default=True).update(
                {'is_default': False}
            )
        
        # Create new payment method record
        new_payment_method = PaymentMethod(
            user_id=user.id,
            payment_type=payment_type,
            provider=provider,
            provider_payment_method_id=token,
            last_four=card_details.get('last_four'),
            brand=card_details.get('brand'),
            expiry_month=card_details.get('expiry_month'),
            expiry_year=card_details.get('expiry_year'),
            is_default=is_default,
            is_active=True,
            is_verified=True,  # Assume verified if successfully processed
            created_at=datetime.utcnow()
        )
        
        db.session.add(new_payment_method)
        db.session.commit()
        
        method_data = {
            "id": new_payment_method.id,
            "type": new_payment_method.payment_type,
            "provider": new_payment_method.provider,
            "last_four": new_payment_method.last_four,
            "brand": new_payment_method.brand,
            "is_default": new_payment_method.is_default,
            "created_at": new_payment_method.created_at.isoformat()
        }
        
        return success_response(
            data=method_data,
            message="Payment method added successfully",
            status_code=201
        )
        
    except Exception as e:
        logger.error(f"Add payment method error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to add payment method",
            status_code=500,
            error_code="PAYMENT_METHOD_ADD_FAILED"
        )

@payments_bp.route('/process', methods=['POST'])
@jwt_required_v2()
@validate_content_type()
@rate_limit_v2(limit=20, window=300, per='user')  # 20 payment attempts per 5 minutes
def process_payment(user):
    """
    Process a payment for an order.
    
    POST /api/v1/payments/process
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data
        
        # Validate required fields
        required_fields = ['order_id', 'payment_method_id', 'amount']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp
        
        order_id = data['order_id']
        payment_method_id = data['payment_method_id']
        amount = data['amount']
        
        # Validate amount
        is_valid_amount, normalized_amount = validate_price(amount)
        if not is_valid_amount or normalized_amount <= 0:
            return validation_error_response(
                errors={"amount": ["Invalid amount"]},
                message="Invalid payment amount"
            )
        
        from app import db, Order, PaymentMethod, PaymentTransaction
        
        # Verify order belongs to user
        order = Order.query.filter_by(id=order_id, user_id=user.id).first()
        if not order:
            return not_found_response("Order", order_id)
        
        # Verify payment method belongs to user
        payment_method = PaymentMethod.query.filter_by(
            id=payment_method_id, 
            user_id=user.id,
            is_active=True
        ).first()
        if not payment_method:
            return not_found_response("Payment method", payment_method_id)
        
        # Verify amount matches order total
        if abs(float(order.total_amount) - normalized_amount) > 0.01:
            return validation_error_response(
                errors={"amount": ["Amount does not match order total"]},
                message="Payment amount mismatch"
            )
        
        # Process payment based on provider
        transaction_id = None
        if payment_method.provider == 'stripe':
            try:
                import stripe
                stripe.api_key = current_app.config.get('STRIPE_SECRET_KEY')
                
                # Create payment intent
                payment_intent = stripe.PaymentIntent.create(
                    amount=int(normalized_amount * 100),  # Convert to cents
                    currency='usd',  # or get from config
                    payment_method=payment_method.provider_payment_method_id,
                    confirm=True,
                    metadata={
                        'order_id': order_id,
                        'user_id': user.id
                    }
                )
                
                transaction_id = payment_intent.id
                
            except Exception as stripe_error:
                logger.error(f"Stripe payment error: {stripe_error}")
                return error_response(
                    message="Payment processing failed",
                    status_code=400,
                    error_code="PAYMENT_PROCESSING_FAILED"
                )
        
        # Create payment transaction record
        payment_transaction = PaymentTransaction(
            user_id=user.id,
            order_id=order_id,
            payment_method_id=payment_method_id,
            amount=normalized_amount,
            currency='USD',
            status='completed',
            provider=payment_method.provider,
            provider_transaction_id=transaction_id,
            created_at=datetime.utcnow()
        )
        
        db.session.add(payment_transaction)
        
        # Update order status
        order.status = 'paid'
        order.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        transaction_data = {
            "id": payment_transaction.id,
            "order_id": order_id,
            "amount": float(payment_transaction.amount),
            "currency": payment_transaction.currency,
            "status": payment_transaction.status,
            "provider": payment_transaction.provider,
            "transaction_id": transaction_id,
            "created_at": payment_transaction.created_at.isoformat()
        }
        
        return success_response(
            data=transaction_data,
            message="Payment processed successfully",
            status_code=201
        )
        
    except Exception as e:
        logger.error(f"Process payment error: {e}")
        db.session.rollback()
        return error_response(
            message="Payment processing failed",
            status_code=500,
            error_code="PAYMENT_PROCESSING_ERROR"
        )

@payments_bp.route('/transactions', methods=['GET'])
@jwt_required_v2()
def get_payment_history(user):
    """
    Get user's payment transaction history.
    
    GET /api/v1/payments/transactions
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status')
        
        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=50)
        
        from app import PaymentTransaction, Order
        
        # Build query
        query = PaymentTransaction.query.filter_by(user_id=user.id)
        
        if status:
            query = query.filter_by(status=status)
        
        query = query.order_by(PaymentTransaction.created_at.desc())
        
        # Execute paginated query
        paginated_transactions = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        transactions_data = []
        for transaction in paginated_transactions.items:
            order = Order.query.get(transaction.order_id)
            
            transactions_data.append({
                "id": transaction.id,
                "order_id": transaction.order_id,
                "order_number": f"ORD-{order.id}" if order else None,
                "amount": float(transaction.amount),
                "currency": transaction.currency,
                "status": transaction.status,
                "provider": transaction.provider,
                "provider_transaction_id": transaction.provider_transaction_id,
                "created_at": transaction.created_at.isoformat() if transaction.created_at else None
            })
        
        return paginated_response(
            data=transactions_data,
            page=page,
            per_page=per_page,
            total=paginated_transactions.total,
            message="Payment history retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get payment history error: {e}")
        return error_response(
            message="Failed to retrieve payment history",
            status_code=500,
            error_code="PAYMENT_HISTORY_FETCH_FAILED"
        )
