#!/usr/bin/env python3
"""
Quick Fix for Project Freezing Issues
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import psutil
import time
from datetime import datetime

def check_system_resources():
    """Check system resources that might cause freezing"""
    print("🔍 CHECKING SYSTEM RESOURCES")
    print("="*60)
    
    # CPU Usage
    cpu_percent = psutil.cpu_percent(interval=1)
    print(f"💻 CPU Usage: {cpu_percent}%")
    
    # Memory Usage
    memory = psutil.virtual_memory()
    print(f"🧠 Memory Usage: {memory.percent}% ({memory.used // (1024**3)} GB / {memory.total // (1024**3)} GB)")
    
    # Disk Usage
    disk = psutil.disk_usage('/')
    print(f"💾 Disk Usage: {disk.percent}% ({disk.used // (1024**3)} GB / {disk.total // (1024**3)} GB)")
    
    # Check for high resource usage
    if cpu_percent > 80:
        print("⚠️  HIGH CPU USAGE DETECTED!")
    if memory.percent > 85:
        print("⚠️  HIGH MEMORY USAGE DETECTED!")
    if disk.percent > 90:
        print("⚠️  LOW DISK SPACE DETECTED!")

def check_running_processes():
    """Check for processes that might be causing issues"""
    print("\n🔍 CHECKING RUNNING PROCESSES")
    print("="*60)
    
    # Look for Python processes
    python_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
        try:
            if 'python' in proc.info['name'].lower():
                python_processes.append(proc.info)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    
    print(f"🐍 Python processes running: {len(python_processes)}")
    for proc in python_processes[:5]:  # Show top 5
        print(f"  - PID {proc['pid']}: {proc['name']} (CPU: {proc['cpu_percent']}%, Memory: {proc['memory_percent']:.1f}%)")

def check_port_conflicts():
    """Check for port conflicts"""
    print("\n🔍 CHECKING PORT CONFLICTS")
    print("="*60)
    
    important_ports = [3000, 5000, 9200, 6379, 3306]
    
    for port in important_ports:
        connections = psutil.net_connections()
        port_in_use = any(conn.laddr.port == port for conn in connections if conn.laddr)
        
        if port_in_use:
            print(f"✅ Port {port}: IN USE")
        else:
            print(f"❌ Port {port}: FREE")

def kill_hanging_processes():
    """Kill hanging Python processes"""
    print("\n🔧 KILLING HANGING PROCESSES")
    print("="*60)
    
    killed_count = 0
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            # Look for hanging Python processes
            if 'python' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                
                # Kill if it's a Flask/backend process that might be hanging
                if any(keyword in cmdline.lower() for keyword in ['app.py', 'flask', 'waitress']):
                    print(f"🔪 Killing process PID {proc.info['pid']}: {cmdline[:50]}...")
                    proc.kill()
                    killed_count += 1
                    
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    
    print(f"✅ Killed {killed_count} hanging processes")

def restart_services():
    """Restart key services"""
    print("\n🔄 RESTARTING SERVICES")
    print("="*60)
    
    # Kill any existing Flask processes
    os.system("taskkill /f /im python.exe 2>nul")
    time.sleep(2)
    
    print("✅ Cleared Python processes")
    
    # Check if Elasticsearch is running
    try:
        import requests
        response = requests.get('http://localhost:9200', timeout=5)
        if response.status_code == 200:
            print("✅ Elasticsearch is running")
        else:
            print("⚠️  Elasticsearch may have issues")
    except:
        print("❌ Elasticsearch is not responding")

def optimize_system():
    """Apply system optimizations"""
    print("\n⚡ APPLYING OPTIMIZATIONS")
    print("="*60)
    
    # Clear Python cache
    os.system("py -c \"import sys; [sys.modules.pop(m) for m in list(sys.modules.keys()) if m.startswith('__pycache__')]\" 2>nul")
    
    # Clear temporary files
    temp_dirs = [
        os.path.expanduser("~/AppData/Local/Temp"),
        "C:/Windows/Temp"
    ]
    
    for temp_dir in temp_dirs:
        if os.path.exists(temp_dir):
            try:
                os.system(f'del /q /s "{temp_dir}\\*.tmp" 2>nul')
                print(f"✅ Cleaned {temp_dir}")
            except:
                pass

def create_lightweight_startup():
    """Create a lightweight startup script"""
    print("\n📝 CREATING LIGHTWEIGHT STARTUP")
    print("="*60)
    
    # Create minimal backend startup
    minimal_backend = '''#!/usr/bin/env python3
"""
Minimal Backend Startup - Optimized for Performance
"""

import os
import sys
from flask import Flask, jsonify
from flask_cors import CORS

# Minimal Flask app
app = Flask(__name__)
CORS(app)

# Disable debug mode for performance
app.config['DEBUG'] = False
app.config['TESTING'] = False

@app.route('/api/health')
def health_check():
    return jsonify({'status': 'ok', 'message': 'Backend is running'})

@app.route('/api/products')
def get_products():
    # Minimal product response
    return jsonify({
        'products': [
            {'id': 1, 'name': 'Eco T-Shirt', 'price': 899},
            {'id': 2, 'name': 'Bamboo Bottle', 'price': 1299}
        ]
    })

if __name__ == '__main__':
    print("🚀 Starting minimal backend...")
    app.run(host='0.0.0.0', port=5000, debug=False, threaded=True)
'''
    
    with open('minimal_backend.py', 'w') as f:
        f.write(minimal_backend)
    
    print("✅ Created minimal_backend.py")

def main():
    print("🚨 FIXING PROJECT FREEZING ISSUE")
    print("="*60)
    print(f"⏰ Started at: {datetime.now()}")
    
    # Step 1: Check system resources
    check_system_resources()
    
    # Step 2: Check running processes
    check_running_processes()
    
    # Step 3: Check port conflicts
    check_port_conflicts()
    
    # Step 4: Kill hanging processes
    kill_hanging_processes()
    
    # Step 5: Restart services
    restart_services()
    
    # Step 6: Optimize system
    optimize_system()
    
    # Step 7: Create lightweight startup
    create_lightweight_startup()
    
    print(f"\n🎯 QUICK FIX RECOMMENDATIONS")
    print("="*60)
    print("1. 🔄 Restart your computer if memory usage is high")
    print("2. 🚀 Use minimal_backend.py for testing")
    print("3. 🔍 Check Task Manager for hanging processes")
    print("4. 💾 Free up disk space if needed")
    print("5. ⚡ Close unnecessary applications")
    
    print(f"\n✅ FREEZING ISSUE FIX COMPLETED!")
    print(f"⏰ Completed at: {datetime.now()}")

if __name__ == '__main__':
    main()
