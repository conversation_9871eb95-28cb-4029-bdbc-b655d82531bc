#!/usr/bin/env python3
"""
Project Files Updater for New ML Models
=======================================

This script updates all project files to work with the new advanced ML models:
- Updates import statements and file paths
- Changes pkl file locations to model folders (not pkl subfolders)
- Updates model version references to v5.0
- Fixes API endpoints to load new model formats
- Updates validation scripts

Usage:
    python update_project_for_new_models.py
"""

import os
import re

def update_smart_discovery_api():
    """Update smart_discovery_api.py to use new model paths"""
    file_path = 'smart_discovery_api.py'
    
    if not os.path.exists(file_path):
        print(f"⚠️  {file_path} not found, skipping...")
        return
    
    print(f"🔧 Updating {file_path}...")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Update the load_ml_models function
    updated_function = '''def load_ml_models():
    """Load ML models for recommendations - optimized for seeded data v5.0"""
    try:
        # Load new recommendation model (saved directly in model folder)
        pkl_path = os.path.join('models', 'Recommendation Model', 'recommendation_model.pkl')
        if os.path.exists(pkl_path):
            with open(pkl_path, 'rb') as f:
                recommendation_data = pickle.load(f)
                
                # Validate new model format
                if isinstance(recommendation_data, dict):
                    version = recommendation_data.get('model_metadata', {}).get('version', '')
                    if 'seeded_data_optimized_v5.0' in version:
                        logger.info("✅ Loaded new seeded-data optimized recommendation model v5.0")
                    else:
                        logger.info("ℹ️  Loaded recommendation model (may not be v5.0)")
                    
                    # Log model info
                    metadata = recommendation_data.get('model_metadata', {})
                    if metadata:
                        logger.info(f"📊 Model trained on {metadata.get('total_users', 0)} users, "
                                  f"{metadata.get('total_products', 0)} products")
                
                return recommendation_data
        else:
            logger.warning(f"New ML model not found at {pkl_path}")
            return None
    except Exception as e:
        logger.error(f"Error loading new ML models: {e}")
        return None'''
    
    # Replace the load_ml_models function
    content = re.sub(
        r'def load_ml_models\(\):.*?return None',
        updated_function,
        content,
        flags=re.DOTALL
    )
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ Updated {file_path}")

def update_smart_features_api():
    """Update smart_features_api.py to use new model paths"""
    file_path = 'smart_features_api.py'
    
    if not os.path.exists(file_path):
        print(f"⚠️  {file_path} not found, skipping...")
        return
    
    print(f"🔧 Updating {file_path}...")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Update model loading function
    load_models_function = '''def load_ml_models():
    """Load all ML models for smart features - new v5.0 models"""
    try:
        models = {}
        models_dir = 'models'
        
        # Load Visual Search model (new location)
        visual_search_path = os.path.join(models_dir, 'Visual Search Model', 'visual_search_model.pkl')
        if os.path.exists(visual_search_path):
            with open(visual_search_path, 'rb') as f:
                models['visual_search'] = pickle.load(f)
        
        # Load Price Trends model (new location)
        price_trends_path = os.path.join(models_dir, 'Price Trends Model', 'price_trends_model.pkl')
        if os.path.exists(price_trends_path):
            with open(price_trends_path, 'rb') as f:
                models['price_trends'] = pickle.load(f)
        
        # Load Inventory model (new location)
        inventory_path = os.path.join(models_dir, 'Inventory Prediction Model', 'inventory_model.pkl')
        if os.path.exists(inventory_path):
            with open(inventory_path, 'rb') as f:
                models['inventory'] = pickle.load(f)
        
        # Load Recommendation model (new location)
        recommendation_path = os.path.join(models_dir, 'Recommendation Model', 'recommendation_model.pkl')
        if os.path.exists(recommendation_path):
            with open(recommendation_path, 'rb') as f:
                models['recommendations'] = pickle.load(f)
        
        # Validate new model versions
        for model_name, model_data in models.items():
            if isinstance(model_data, dict) and 'model_metadata' in model_data:
                metadata = model_data['model_metadata']
                version = metadata.get('version', 'unknown')
                if 'seeded_data_optimized_v5.0' in version:
                    logger.info(f"✅ {model_name} model v5.0 loaded successfully")
                else:
                    logger.warning(f"⚠️  {model_name} model may not be v5.0")
        
        logger.info(f"Loaded {len(models)} new ML models v5.0")
        return models
        
    except Exception as e:
        logger.error(f"Error loading new ML models: {e}")
        return {}'''
    
    # Replace the load_ml_models function
    content = re.sub(
        r'def load_ml_models\(\):.*?return \{\}',
        load_models_function,
        content,
        flags=re.DOTALL
    )
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ Updated {file_path}")

def update_production_smart_features_api():
    """Update production_smart_features_api.py"""
    file_path = 'production_smart_features_api.py'
    
    if not os.path.exists(file_path):
        print(f"⚠️  {file_path} not found, skipping...")
        return
    
    print(f"🔧 Updating {file_path}...")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Update model version references
    content = re.sub(
        r"'model_version': 'seeded_data_optimized_v4\.0'",
        "'model_version': 'seeded_data_optimized_v5.0'",
        content
    )
    
    # Add new model validation
    new_validation = '''
        # Validate new v5.0 models
        models_info = {}
        for model_name in ['visual_search', 'price_trends', 'inventory', 'recommendations']:
            model_data = models.get(model_name, {})
            if isinstance(model_data, dict) and 'model_metadata' in model_data:
                metadata = model_data['model_metadata']
                version = metadata.get('version', 'unknown')
                models_info[model_name] = {
                    'version': version,
                    'v5_optimized': 'seeded_data_optimized_v5.0' in version,
                    'model_type': metadata.get('model_type', 'unknown'),
                    'created_at': metadata.get('created_at', 'unknown')
                }
        '''
    
    # Add validation after model loading
    if 'models_info' not in content:
        content = content.replace(
            'models = load_ml_models()',
            'models = load_ml_models()' + new_validation
        )
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ Updated {file_path}")

def update_recommendation_api():
    """Update recommendation_api.py to work with new models"""
    file_path = 'recommendation_api.py'
    
    if not os.path.exists(file_path):
        print(f"⚠️  {file_path} not found, skipping...")
        return
    
    print(f"🔧 Updating {file_path}...")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Update comment about model structure
    content = re.sub(
        r'# Note: Using organized model structure - advanced model is in models/Recommendation Model/',
        '# Note: Using new v5.0 model structure - model saved directly in models/Recommendation Model/',
        content
    )
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ Updated {file_path}")

def create_new_model_validation_script():
    """Create validation script for new v5.0 models"""
    script_content = '''#!/usr/bin/env python3
"""
New ML Models Validation Script (v5.0)
======================================

This script validates that all 4 new ML models v5.0 are working correctly
with the seeded data and can be loaded by the API endpoints.
"""

import os
import pickle
import sys

def validate_model_file(model_path, model_name):
    """Validate a single new model file"""
    print(f"🔍 Validating {model_name} v5.0...")
    
    if not os.path.exists(model_path):
        print(f"❌ {model_name} file not found: {model_path}")
        return False
    
    try:
        with open(model_path, 'rb') as f:
            model_data = pickle.load(f)
        
        if isinstance(model_data, dict) and 'model_metadata' in model_data:
            metadata = model_data['model_metadata']
            version = metadata.get('version', 'unknown')
            print(f"   📊 Version: {version}")
            
            # Check for v5.0 optimization
            if 'seeded_data_optimized_v5.0' in version:
                print(f"   ✅ Optimized for seeded data v5.0")
            else:
                print(f"   ⚠️  May not be v5.0 optimized")
            
            # Check model metadata
            print(f"   🎯 Model type: {metadata.get('model_type', 'unknown')}")
            print(f"   📅 Created: {metadata.get('created_at', 'unknown')}")
            
            # Model-specific info
            if 'total_users' in metadata:
                print(f"   👥 Users: {metadata['total_users']}")
            if 'total_products' in metadata:
                print(f"   📦 Products: {metadata['total_products']}")
            
            print(f"   ✅ {model_name} v5.0 loaded successfully")
            return True
        else:
            print(f"   ⚠️  {model_name} has unexpected format (not v5.0)")
            return False
            
    except Exception as e:
        print(f"   ❌ Failed to load {model_name}: {e}")
        return False

def main():
    """Main validation function"""
    print("🔍 NEW ML MODELS VALIDATION (v5.0)")
    print("=" * 60)
    
    models_to_validate = [
        ('models/Recommendation Model/recommendation_model.pkl', 'Recommendation Model'),
        ('models/Price Trends Model/price_trends_model.pkl', 'Price Trends Model'),
        ('models/Inventory Prediction Model/inventory_model.pkl', 'Inventory Model'),
        ('models/Visual Search Model/visual_search_model.pkl', 'Visual Search Model')
    ]
    
    results = []
    for model_path, model_name in models_to_validate:
        success = validate_model_file(model_path, model_name)
        results.append((model_name, success))
        print()
    
    # Summary
    print("=" * 60)
    print("📋 VALIDATION SUMMARY")
    print("=" * 60)
    
    successful = [name for name, success in results if success]
    failed = [name for name, success in results if not success]
    
    print(f"✅ Successful: {len(successful)}/{len(results)}")
    print(f"❌ Failed: {len(failed)}")
    
    if successful:
        print("\\n✅ WORKING MODELS (v5.0):")
        for name in successful:
            print(f"   🎯 {name}")
    
    if failed:
        print("\\n❌ FAILED MODELS:")
        for name in failed:
            print(f"   ⚠️  {name}")
    
    print(f"\\n🎉 New models validation completed!")
    return len(failed) == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
'''
    
    with open('validate_new_ml_models.py', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("✅ Created validate_new_ml_models.py")

def main():
    """Main function to update all project files"""
    print("🔧 PROJECT FILES UPDATER FOR NEW ML MODELS v5.0")
    print("=" * 70)
    print("Updating all project files to work with new advanced ML models...")
    print()
    
    # Update API files
    update_smart_discovery_api()
    update_smart_features_api()
    update_production_smart_features_api()
    update_recommendation_api()
    
    # Create new validation script
    create_new_model_validation_script()
    
    print()
    print("=" * 70)
    print("✅ PROJECT FILES UPDATE COMPLETED")
    print("=" * 70)
    print()
    print("📋 Next steps:")
    print("1. Run the new ML models: python run_new_ml_models.py")
    print("2. Validate new models: python validate_new_ml_models.py")
    print("3. Test API endpoints with new v5.0 models")
    print()
    print("🎯 All project files are now updated for new ML models v5.0!")

if __name__ == "__main__":
    main()
