"""
Authentication Routes
====================

All authentication and authorization related endpoints.
Uses consistent response format and proper URL patterns.
"""

from flask import request
from flask_jwt_extended import create_access_token, create_refresh_token, get_jwt_identity, get_jwt
import logging

from ..common.versioning import create_versioned_blueprint
from ..common.response_wrapper import (
    success_response, error_response, validation_error_response,
    unauthorized_response, forbidden_response, rate_limit_response
)
from ..common.auth_decorators import jwt_required_v2, rate_limit_v2, validate_content_type
from ..common.validators import (
    validate_json, validate_required_fields, validate_email, 
    validate_password_strength, validate_phone
)

logger = logging.getLogger(__name__)

# Create versioned blueprint
auth_bp = create_versioned_blueprint('auth', __name__, url_prefix='/auth')

@auth_bp.route('/sign-up', methods=['POST'])
@rate_limit_v2(limit=3, window=3600, per='ip', block_duration=3600)  # 3 signups per hour
@validate_content_type()
def sign_up():
    """
    User registration endpoint.
    
    POST /api/v1/auth/sign-up
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data
        
        # Validate required fields
        required_fields = ['email', 'password', 'first_name', 'last_name']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp
        
        email = data['email'].strip().lower()
        password = data['password']
        first_name = data['first_name'].strip()
        last_name = data['last_name'].strip()
        
        # Validate email format
        if not validate_email(email):
            return validation_error_response(
                errors={"email": ["Invalid email format"]},
                message="Invalid email format"
            )
        
        # Validate password strength
        is_strong, password_errors = validate_password_strength(password)
        if not is_strong:
            return validation_error_response(
                errors={"password": password_errors},
                message="Password does not meet requirements"
            )
        
        # Check if user already exists
        from app import db, User, bcrypt
        existing_user = User.query.filter_by(email=email).first()
        if existing_user:
            return error_response(
                message="User with this email already exists",
                status_code=409,
                error_code="USER_EXISTS"
            )
        
        # Create new user
        hashed_password = bcrypt.generate_password_hash(password).decode('utf-8')
        new_user = User(
            email=email,
            password_hash=hashed_password,
            first_name=first_name,
            last_name=last_name,
            is_active=True
        )
        
        db.session.add(new_user)
        db.session.commit()
        
        # Create access token
        access_token = create_access_token(identity=new_user.id)
        refresh_token = create_refresh_token(identity=new_user.id)
        
        user_data = {
            "id": new_user.id,
            "email": new_user.email,
            "first_name": new_user.first_name,
            "last_name": new_user.last_name,
            "created_at": new_user.created_at.isoformat() if new_user.created_at else None
        }
        
        return success_response(
            data={
                "user": user_data,
                "access_token": access_token,
                "refresh_token": refresh_token
            },
            message="User registered successfully",
            status_code=201
        )
        
    except Exception as e:
        logger.error(f"Sign up error: {e}")
        return error_response(
            message="Registration failed",
            status_code=500,
            error_code="REGISTRATION_FAILED"
        )

@auth_bp.route('/sign-in', methods=['POST'])
@rate_limit_v2(limit=10, window=300, per='ip', block_duration=600)  # 10 attempts per 5 minutes
@validate_content_type()
def sign_in():
    """
    User login endpoint.
    
    POST /api/v1/auth/sign-in
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data
        
        # Validate required fields
        required_fields = ['email', 'password']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp
        
        email = data['email'].strip().lower()
        password = data['password']
        
        # Find user
        from app import db, User, bcrypt
        user = User.query.filter_by(email=email).first()
        
        if not user or not bcrypt.check_password_hash(user.password_hash, password):
            return unauthorized_response("Invalid email or password")
        
        if not user.is_active:
            return forbidden_response("Account is deactivated")
        
        # Create tokens
        access_token = create_access_token(identity=user.id)
        refresh_token = create_refresh_token(identity=user.id)
        
        user_data = {
            "id": user.id,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "last_login": user.last_login.isoformat() if user.last_login else None
        }
        
        # Update last login
        from datetime import datetime
        user.last_login = datetime.utcnow()
        db.session.commit()
        
        return success_response(
            data={
                "user": user_data,
                "access_token": access_token,
                "refresh_token": refresh_token
            },
            message="Login successful"
        )
        
    except Exception as e:
        logger.error(f"Sign in error: {e}")
        return error_response(
            message="Login failed",
            status_code=500,
            error_code="LOGIN_FAILED"
        )

@auth_bp.route('/refresh-token', methods=['POST'])
@rate_limit_v2(limit=20, window=300, per='ip')  # 20 refresh attempts per 5 minutes
def refresh_token():
    """
    Refresh JWT access token.
    
    POST /api/v1/auth/refresh-token
    """
    try:
        from flask_jwt_extended import jwt_required, get_jwt_identity, create_access_token
        
        # This endpoint requires a refresh token
        @jwt_required(refresh=True)
        def _refresh():
            user_id = get_jwt_identity()
            
            # Verify user still exists and is active
            from app import User
            user = User.query.get(user_id)
            if not user or not user.is_active:
                return unauthorized_response("User account is inactive")
            
            # Create new access token
            new_access_token = create_access_token(identity=user_id)
            
            return success_response(
                data={"access_token": new_access_token},
                message="Token refreshed successfully"
            )
        
        return _refresh()
        
    except Exception as e:
        logger.error(f"Token refresh error: {e}")
        return error_response(
            message="Token refresh failed",
            status_code=500,
            error_code="TOKEN_REFRESH_FAILED"
        )

@auth_bp.route('/sign-out', methods=['POST'])
@jwt_required_v2()
def sign_out(user):
    """
    User logout endpoint.
    
    POST /api/v1/auth/sign-out
    """
    try:
        # Get JWT token for blacklisting
        jti = get_jwt()['jti']
        
        # Add token to blacklist (implement token blacklisting)
        # This would typically involve storing the JTI in Redis or database
        # For now, we'll just return success
        
        return success_response(
            message="Logged out successfully"
        )
        
    except Exception as e:
        logger.error(f"Sign out error: {e}")
        return error_response(
            message="Logout failed",
            status_code=500,
            error_code="LOGOUT_FAILED"
        )

@auth_bp.route('/oauth/providers', methods=['GET'])
def get_oauth_providers():
    """
    Get available OAuth providers.
    
    GET /api/v1/auth/oauth/providers
    """
    try:
        # Import OAuth configuration
        from app import OAUTH_PROVIDERS
        
        providers = []
        for provider_name, config in OAUTH_PROVIDERS.items():
            if config.get('enabled', False):
                providers.append({
                    "name": provider_name,
                    "display_name": config.get('display_name', provider_name.title()),
                    "icon": config.get('icon'),
                    "authorize_url": f"/api/v1/auth/oauth/{provider_name}/authorize"
                })
        
        return success_response(
            data={"providers": providers},
            message="OAuth providers retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"OAuth providers error: {e}")
        return error_response(
            message="Failed to get OAuth providers",
            status_code=500,
            error_code="OAUTH_PROVIDERS_FAILED"
        )
