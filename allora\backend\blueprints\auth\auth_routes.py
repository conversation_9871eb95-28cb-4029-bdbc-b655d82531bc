"""
Authentication Routes
====================

All authentication and authorization related endpoints.
Uses consistent response format and proper URL patterns.
"""
import datetime
import secrets

from flask import request
from flask_bcrypt import Bcrypt
from flask_jwt_extended import create_access_token, create_refresh_token, get_jwt_identity, get_jwt
import logging, random, string

from ..common.versioning import create_versioned_blueprint
from ..common.response_wrapper import (
    success_response, error_response, validation_error_response,
    unauthorized_response, forbidden_response, rate_limit_response
)
from ..common.auth_decorators import jwt_required_v2, rate_limit_v2, validate_content_type
from ..common.validators import (
    validate_json, validate_required_fields, validate_email, 
    validate_password_strength, validate_phone
)

logger = logging.getLogger(__name__)

# Create versioned blueprint
auth_bp = create_versioned_blueprint('auth', __name__, url_prefix='/auth')

@auth_bp.route('/sign-up', methods=['POST'])
@rate_limit_v2(limit=3, window=3600, per='ip', block_duration=3600)  # 3 signups per hour
@validate_content_type()
def sign_up():
    """
    User registration endpoint.
    
    POST /api/v1/auth/sign-up
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data
        
        # Validate required fields
        required_fields = ['email', 'password', 'first_name', 'last_name']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp
        
        email = data['email'].strip().lower()
        password = data['password']
        first_name = data['first_name'].strip()
        last_name = data['last_name'].strip()
        
        # Validate email format
        if not validate_email(email):
            return validation_error_response(
                errors={"email": ["Invalid email format"]},
                message="Invalid email format"
            )
        
        # Validate password strength
        is_strong, password_errors = validate_password_strength(password)
        if not is_strong:
            return validation_error_response(
                errors={"password": password_errors},
                message="Password does not meet requirements"
            )
        
        # Check if user already exists
        from app import db, User, bcrypt
        existing_user = User.query.filter_by(email=email).first()
        if existing_user:
            return error_response(
                message="User with this email already exists",
                status_code=409,
                error_code="USER_EXISTS"
            )
        
        # Create new user
        hashed_password = bcrypt.generate_password_hash(password).decode('utf-8')
        new_user = User(
            email=email,
            password_hash=hashed_password,
            first_name=first_name,
            last_name=last_name,
            is_active=True
        )
        
        db.session.add(new_user)
        db.session.commit()
        
        # Create access token
        access_token = create_access_token(identity=new_user.id)
        refresh_token = create_refresh_token(identity=new_user.id)
        
        user_data = {
            "id": new_user.id,
            "email": new_user.email,
            "first_name": new_user.first_name,
            "last_name": new_user.last_name,
            "created_at": new_user.created_at.isoformat() if new_user.created_at else None
        }
        
        return success_response(
            data={
                "user": user_data,
                "access_token": access_token,
                "refresh_token": refresh_token
            },
            message="User registered successfully",
            status_code=201
        )
        
    except Exception as e:
        logger.error(f"Sign up error: {e}")
        return error_response(
            message="Registration failed",
            status_code=500,
            error_code="REGISTRATION_FAILED"
        )

@auth_bp.route('/sign-in', methods=['POST'])
@rate_limit_v2(limit=10, window=300, per='ip', block_duration=600)  # 10 attempts per 5 minutes
@validate_content_type()
def sign_in():
    """
    User login endpoint.
    
    POST /api/v1/auth/sign-in
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data
        
        # Validate required fields
        required_fields = ['email', 'password']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp
        
        email = data['email'].strip().lower()
        password = data['password']
        
        # Find user
        from app import db, User, bcrypt
        user = User.query.filter_by(email=email).first()
        
        if not user or not bcrypt.check_password_hash(user.password_hash, password):
            return unauthorized_response("Invalid email or password")
        
        if not user.is_active:
            return forbidden_response("Account is deactivated")
        
        # Create tokens
        access_token = create_access_token(identity=user.id)
        refresh_token = create_refresh_token(identity=user.id)
        
        user_data = {
            "id": user.id,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "last_login": user.last_login.isoformat() if user.last_login else None
        }
        
        # Update last login
        from datetime import datetime
        user.last_login = datetime.utcnow()
        db.session.commit()
        
        return success_response(
            data={
                "user": user_data,
                "access_token": access_token,
                "refresh_token": refresh_token
            },
            message="Login successful"
        )
        
    except Exception as e:
        logger.error(f"Sign in error: {e}")
        return error_response(
            message="Login failed",
            status_code=500,
            error_code="LOGIN_FAILED"
        )

@auth_bp.route('/refresh-token', methods=['POST'])
@rate_limit_v2(limit=20, window=300, per='ip')  # 20 refresh attempts per 5 minutes
def refresh_token():
    """
    Refresh JWT access token.
    
    POST /api/v1/auth/refresh-token
    """
    try:
        from flask_jwt_extended import jwt_required, get_jwt_identity, create_access_token
        
        # This endpoint requires a refresh token
        @jwt_required(refresh=True)
        def _refresh():
            user_id = get_jwt_identity()
            
            # Verify user still exists and is active
            from app import User
            user = User.query.get(user_id)
            if not user or not user.is_active:
                return unauthorized_response("User account is inactive")
            
            # Create new access token
            new_access_token = create_access_token(identity=user_id)
            
            return success_response(
                data={"access_token": new_access_token},
                message="Token refreshed successfully"
            )
        
        return _refresh()
        
    except Exception as e:
        logger.error(f"Token refresh error: {e}")
        return error_response(
            message="Token refresh failed",
            status_code=500,
            error_code="TOKEN_REFRESH_FAILED"
        )

@auth_bp.route('/sign-out', methods=['POST'])
@jwt_required_v2()
def sign_out(user):
    """
    User logout endpoint.
    
    POST /api/v1/auth/sign-out
    """
    try:
        # Get JWT token for blacklisting
        jti = get_jwt()['jti']
        
        # Add token to blacklist (implement token blacklisting)
        # This would typically involve storing the JTI in Redis or database
        # For now, we'll just return success
        
        return success_response(
            message="Logged out successfully"
        )
        
    except Exception as e:
        logger.error(f"Sign out error: {e}")
        return error_response(
            message="Logout failed",
            status_code=500,
            error_code="LOGOUT_FAILED"
        )

@auth_bp.route('/oauth/providers', methods=['GET'])
def get_oauth_providers():
    """
    Get available OAuth providers.
    
    GET /api/v1/auth/oauth/providers
    """
    try:
        # Import OAuth configuration
        from app import OAUTH_PROVIDERS
        
        providers = []
        for provider_name, config in OAUTH_PROVIDERS.items():
            if config.get('enabled', False):
                providers.append({
                    "name": provider_name,
                    "display_name": config.get('display_name', provider_name.title()),
                    "icon": config.get('icon'),
                    "authorize_url": f"/api/v1/auth/oauth/{provider_name}/authorize"
                })
        
        return success_response(
            data={"providers": providers},
            message="OAuth providers retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"OAuth providers error: {e}")
        return error_response(
            message="Failed to get OAuth providers",
            status_code=500,
            error_code="OAUTH_PROVIDERS_FAILED"
        )

@auth_bp.route('/oauth/<provider>/authorize', methods=['GET'])
def get_oauth_authorize_url(provider):
    """
    Get OAuth authorization URL for a provider.

    GET /api/v1/auth/oauth/{provider}/authorize
    """
    try:
        from app import OAUTH_PROVIDERS

        if provider not in OAUTH_PROVIDERS:
            return error_response(
                message="Unsupported OAuth provider",
                status_code=400,
                error_code="UNSUPPORTED_PROVIDER"
            )

        config = OAUTH_PROVIDERS[provider]
        if not config.get('enabled', False):
            return error_response(
                message="OAuth provider is disabled",
                status_code=400,
                error_code="PROVIDER_DISABLED"
            )

        # Generate state parameter for security
        import secrets
        state = secrets.token_urlsafe(32)

        # Store state in session or cache for verification
        # This is a simplified version - in production, store in Redis

        if provider == 'google':
            from urllib.parse import urlencode

            params = {
                'client_id': config['client_id'],
                'redirect_uri': config['redirect_uri'],
                'scope': 'openid email profile',
                'response_type': 'code',
                'state': state
            }

            auth_url = f"https://accounts.google.com/o/oauth2/v2/auth?{urlencode(params)}"

        elif provider == 'facebook':
            from urllib.parse import urlencode

            params = {
                'client_id': config['client_id'],
                'redirect_uri': config['redirect_uri'],
                'scope': 'email',
                'response_type': 'code',
                'state': state
            }

            auth_url = f"https://www.facebook.com/v18.0/dialog/oauth?{urlencode(params)}"

        else:
            return error_response(
                message="OAuth provider not implemented",
                status_code=501,
                error_code="PROVIDER_NOT_IMPLEMENTED"
            )

        return success_response(
            data={
                "provider": provider,
                "authorization_url": auth_url,
                "state": state
            },
            message="OAuth authorization URL generated successfully"
        )

    except Exception as e:
        logger.error(f"OAuth authorize URL error: {e}")
        return error_response(
            message="Failed to generate OAuth authorization URL",
            status_code=500,
            error_code="OAUTH_AUTHORIZE_FAILED"
        )

@auth_bp.route('/oauth/<provider>/callback', methods=['POST'])
@rate_limit_v2(limit=10, window=300, per='ip', block_duration=900)
def oauth_callback(provider):
    """
    Handle OAuth callback for various providers.

    POST /api/v1/auth/oauth/{provider}/callback
    """
    try:
        from app import OAUTH_PROVIDERS, db, User, bcrypt

        if provider not in OAUTH_PROVIDERS:
            return error_response(
                message="Unsupported OAuth provider",
                status_code=400,
                error_code="UNSUPPORTED_PROVIDER"
            )

        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        code = data.get('code')
        state = data.get('state')

        if not code:
            return validation_error_response(
                errors={"code": ["Authorization code is required"]},
                message="Missing authorization code"
            )

        # Verify state parameter (simplified - in production, verify against stored state)

        # Exchange code for access token and get user info
        user_info = None

        if provider == 'google':
            # Exchange code for token with Google
            import requests

            token_data = {
                'client_id': OAUTH_PROVIDERS[provider]['client_id'],
                'client_secret': OAUTH_PROVIDERS[provider]['client_secret'],
                'code': code,
                'grant_type': 'authorization_code',
                'redirect_uri': OAUTH_PROVIDERS[provider]['redirect_uri']
            }

            token_response = requests.post(
                'https://oauth2.googleapis.com/token',
                data=token_data
            )

            if token_response.status_code != 200:
                return error_response(
                    message="Failed to exchange code for token",
                    status_code=400,
                    error_code="TOKEN_EXCHANGE_FAILED"
                )

            token_info = token_response.json()
            access_token = token_info.get('access_token')

            # Get user info from Google
            user_response = requests.get(
                'https://www.googleapis.com/oauth2/v2/userinfo',
                headers={'Authorization': f'Bearer {access_token}'}
            )

            if user_response.status_code == 200:
                user_info = user_response.json()

        if not user_info:
            return error_response(
                message="Failed to get user information from OAuth provider",
                status_code=400,
                error_code="USER_INFO_FAILED"
            )

        # Find or create user
        email = user_info.get('email')
        if not email:
            return error_response(
                message="Email not provided by OAuth provider",
                status_code=400,
                error_code="EMAIL_NOT_PROVIDED"
            )

        user = User.query.filter_by(email=email).first()

        if not user:
            # Create new user
            user = User(
                email=email,
                first_name=user_info.get('given_name', ''),
                last_name=user_info.get('family_name', ''),
                password_hash=bcrypt.generate_password_hash(secrets.token_urlsafe(32)).decode('utf-8'),
                is_active=True,
                email_verified=True,  # OAuth emails are considered verified
                oauth_provider=provider,
                oauth_id=user_info.get('id'),
                created_at=datetime.utcnow()
            )
            db.session.add(user)
            db.session.commit()

        # Create tokens
        access_token = create_access_token(identity=user.id)
        refresh_token = create_refresh_token(identity=user.id)

        # Update last login
        user.last_login = datetime.utcnow()
        db.session.commit()

        user_data = {
            "id": user.id,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "oauth_provider": provider
        }

        return success_response(
            data={
                "user": user_data,
                "access_token": access_token,
                "refresh_token": refresh_token
            },
            message="OAuth authentication successful"
        )

    except Exception as e:
        logger.error(f"OAuth callback error: {e}")
        return error_response(
            message="OAuth authentication failed",
            status_code=500,
            error_code="OAUTH_CALLBACK_FAILED"
        )

@auth_bp.route('/send-otp', methods=['POST'])
@rate_limit_v2(limit=5, window=300, per='ip', block_duration=900)
@validate_content_type()
def send_otp():
    """
    Send OTP to phone number for authentication.

    POST /api/v1/auth/send-otp
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['phone']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        phone = data['phone'].strip()

        # Validate phone format
        if not validate_phone(phone):
            return validation_error_response(
                errors={"phone": ["Invalid phone number format"]},
                message="Invalid phone number"
            )

        # Generate OTP
        import random
        otp = str(random.randint(100000, 999999))

        # Store OTP in cache/database (simplified - use Redis in production)
        from app import db, OTPVerification

        # Remove any existing OTP for this phone
        OTPVerification.query.filter_by(phone=phone).delete()

        # Create new OTP record
        otp_record = OTPVerification(
            phone=phone,
            otp=otp,
            expires_at=datetime.utcnow() + datetime.timedelta(minutes=5),
            created_at=datetime.utcnow()
        )
        db.session.add(otp_record)
        db.session.commit()

        # Send OTP via SMS (implement your SMS service here)
        # For demo purposes, we'll just log it
        logger.info(f"OTP for {phone}: {otp}")

        # In production, integrate with SMS service like Twilio, AWS SNS, etc.
        # sms_service.send_otp(phone, otp)

        return success_response(
            data={
                "phone": phone,
                "expires_in": 300  # 5 minutes
            },
            message="OTP sent successfully"
        )

    except Exception as e:
        logger.error(f"Send OTP error: {e}")
        return error_response(
            message="Failed to send OTP",
            status_code=500,
            error_code="OTP_SEND_FAILED"
        )

@auth_bp.route('/sign-in-phone', methods=['POST'])
@rate_limit_v2(limit=10, window=300, per='ip', block_duration=900)
@validate_content_type()
def sign_in_phone():
    """
    Sign in with phone number and OTP.

    POST /api/v1/auth/sign-in-phone
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['phone', 'otp']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        phone = data['phone'].strip()
        otp = data['otp'].strip()

        from app import db, User, OTPVerification

        # Verify OTP
        otp_record = OTPVerification.query.filter_by(
            phone=phone,
            otp=otp
        ).first()

        if not otp_record:
            return unauthorized_response("Invalid OTP")

        if otp_record.expires_at < datetime.utcnow():
            # Clean up expired OTP
            db.session.delete(otp_record)
            db.session.commit()
            return unauthorized_response("OTP has expired")

        # Find user by phone
        user = User.query.filter_by(phone=phone).first()

        if not user:
            return unauthorized_response("No account found with this phone number")

        if not user.is_active:
            return forbidden_response("Account is deactivated")

        # Clean up used OTP
        db.session.delete(otp_record)

        # Create tokens
        access_token = create_access_token(identity=user.id)
        refresh_token = create_refresh_token(identity=user.id)

        # Update last login
        user.last_login = datetime.utcnow()
        db.session.commit()

        user_data = {
            "id": user.id,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "phone": user.phone,
            "phone_verified": True
        }

        return success_response(
            data={
                "user": user_data,
                "access_token": access_token,
                "refresh_token": refresh_token
            },
            message="Phone login successful"
        )

    except Exception as e:
        logger.error(f"Phone login error: {e}")
        return error_response(
            message="Phone login failed",
            status_code=500,
            error_code="PHONE_LOGIN_FAILED"
        )

@auth_bp.route('/sign-up-phone', methods=['POST'])
@rate_limit_v2(limit=5, window=300, per='ip', block_duration=900)
@validate_content_type()
def sign_up_phone():
    """
    Sign up with phone number and OTP.

    POST /api/v1/auth/sign-up-phone
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['phone', 'otp', 'first_name', 'last_name']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        phone = data['phone'].strip()
        otp = data['otp'].strip()
        first_name = data['first_name'].strip()
        last_name = data['last_name'].strip()
        email = data.get('email', '').strip()

        from app import db, User, OTPVerification, bcrypt

        # Verify OTP
        otp_record = OTPVerification.query.filter_by(
            phone=phone,
            otp=otp
        ).first()

        if not otp_record:
            return unauthorized_response("Invalid OTP")

        if otp_record.expires_at < datetime.utcnow():
            # Clean up expired OTP
            db.session.delete(otp_record)
            db.session.commit()
            return unauthorized_response("OTP has expired")

        # Check if user already exists
        existing_user = User.query.filter_by(phone=phone).first()
        if existing_user:
            return error_response(
                message="User with this phone number already exists",
                status_code=409,
                error_code="PHONE_EXISTS"
            )

        if email:
            existing_email = User.query.filter_by(email=email).first()
            if existing_email:
                return error_response(
                    message="User with this email already exists",
                    status_code=409,
                    error_code="EMAIL_EXISTS"
                )

        # Create new user
        new_user = User(
            phone=phone,
            email=email or None,
            first_name=first_name,
            last_name=last_name,
            password_hash=bcrypt.generate_password_hash(secrets.token_urlsafe(32)).decode('utf-8'),
            is_active=True,
            phone_verified=True,
            created_at=datetime.utcnow()
        )

        db.session.add(new_user)

        # Clean up used OTP
        db.session.delete(otp_record)

        db.session.commit()

        # Create tokens
        access_token = create_access_token(identity=new_user.id)
        refresh_token = create_refresh_token(identity=new_user.id)

        user_data = {
            "id": new_user.id,
            "phone": new_user.phone,
            "email": new_user.email,
            "first_name": new_user.first_name,
            "last_name": new_user.last_name,
            "phone_verified": True
        }

        return success_response(
            data={
                "user": user_data,
                "access_token": access_token,
                "refresh_token": refresh_token
            },
            message="Phone registration successful",
            status_code=201
        )

    except Exception as e:
        logger.error(f"Phone signup error: {e}")
        db.session.rollback()
        return error_response(
            message="Phone registration failed",
            status_code=500,
            error_code="PHONE_SIGNUP_FAILED"
        )

@auth_bp.route('/oauth/google', methods=['POST'])
@rate_limit_v2(limit=10, window=300, per='ip', block_duration=900)
@validate_content_type()
def google_oauth():
    """
    Handle Google OAuth authentication (direct token method).

    POST /api/v1/auth/oauth/google
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['token']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        token = data['token']

        # Verify Google token
        try:
            import requests

            # Verify token with Google
            response = requests.get(
                f'https://www.googleapis.com/oauth2/v1/tokeninfo?access_token={token}'
            )

            if response.status_code != 200:
                return unauthorized_response("Invalid Google token")

            token_info = response.json()

            # Get user info from Google
            user_response = requests.get(
                'https://www.googleapis.com/oauth2/v2/userinfo',
                headers={'Authorization': f'Bearer {token}'}
            )

            if user_response.status_code != 200:
                return error_response(
                    message="Failed to get user information from Google",
                    status_code=400,
                    error_code="GOOGLE_USER_INFO_FAILED"
                )

            user_info = user_response.json()

        except Exception as google_error:
            logger.error(f"Google OAuth error: {google_error}")
            return error_response(
                message="Google authentication failed",
                status_code=400,
                error_code="GOOGLE_AUTH_FAILED"
            )

        # Find or create user
        email = user_info.get('email')
        if not email:
            return error_response(
                message="Email not provided by Google",
                status_code=400,
                error_code="EMAIL_NOT_PROVIDED"
            )

        from app import db, User, bcrypt

        user = User.query.filter_by(email=email).first()

        if not user:
            # Create new user
            user = User(
                email=email,
                first_name=user_info.get('given_name', ''),
                last_name=user_info.get('family_name', ''),
                password_hash=bcrypt.generate_password_hash(secrets.token_urlsafe(32)).decode('utf-8'),
                is_active=True,
                email_verified=True,
                oauth_provider='google',
                oauth_id=user_info.get('id'),
                created_at=datetime.utcnow()
            )
            db.session.add(user)
            db.session.commit()

        # Create tokens
        access_token = create_access_token(identity=user.id)
        refresh_token = create_refresh_token(identity=user.id)

        # Update last login
        user.last_login = datetime.utcnow()
        db.session.commit()

        user_data = {
            "id": user.id,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "oauth_provider": "google"
        }

        return success_response(
            data={
                "user": user_data,
                "access_token": access_token,
                "refresh_token": refresh_token
            },
            message="Google authentication successful"
        )

    except Exception as e:
        logger.error(f"Google OAuth error: {e}")
        return error_response(
            message="Google authentication failed",
            status_code=500,
            error_code="GOOGLE_OAUTH_FAILED"
        )
