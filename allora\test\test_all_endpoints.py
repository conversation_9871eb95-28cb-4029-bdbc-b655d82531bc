#!/usr/bin/env python3
"""
Comprehensive API Endpoint Testing Script
=========================================

This script tests every API endpoint in the Allora E-commerce Backend
and reports their status, response times, and any errors.

Features:
- Tests all 196+ API endpoints
- Authentication handling (JWT, Admin, Seller)
- Rate limiting awareness
- Detailed reporting with status codes
- Performance metrics
- Error categorization
- HTML report generation

Author: Allora Development Team
Date: 2025-07-12
"""

import requests
import json
import time
import sys
import os
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from urllib.parse import urljoin

# Configuration
BASE_URL = "http://localhost:5000"
MAX_WORKERS = 5  # Limit concurrent requests to avoid overwhelming server
REQUEST_TIMEOUT = 30  # seconds
RATE_LIMIT_DELAY = 1  # seconds between requests to respect rate limits

@dataclass
class EndpointTest:
    """Represents a single endpoint test"""
    method: str
    path: str
    description: str
    requires_auth: bool = False
    requires_admin: bool = False
    requires_seller: bool = False
    test_data: Optional[Dict] = None
    expected_status: List[int] = None
    category: str = "General"

@dataclass
class TestResult:
    """Represents the result of an endpoint test"""
    endpoint: EndpointTest
    status_code: int
    response_time: float
    success: bool
    error_message: Optional[str] = None
    response_data: Optional[Dict] = None

class APITester:
    """Main API testing class"""
    
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.session = requests.Session()
        self.tokens = {
            'user': None,
            'admin': None,
            'seller': None
        }
        self.test_data = {}
        self.results: List[TestResult] = []
        self.lock = threading.Lock()
        
    def setup_test_environment(self):
        """Set up test environment with authentication tokens and test data"""
        print("🔧 Setting up test environment...")
        
        # Test basic connectivity
        try:
            response = self.session.get(f"{self.base_url}/api/health", timeout=REQUEST_TIMEOUT)
            if response.status_code != 200:
                print(f"❌ Backend not accessible at {self.base_url}")
                return False
            print("✅ Backend connectivity confirmed")
        except Exception as e:
            print(f"❌ Cannot connect to backend: {e}")
            return False
        
        # Get authentication tokens
        self._setup_authentication()
        
        # Setup test data
        self._setup_test_data()
        
        return True
    
    def _setup_authentication(self):
        """Setup authentication tokens for testing"""
        print("🔐 Setting up authentication...")
        
        # Try to get user token (create test user if needed)
        try:
            # Try login first
            login_data = {
                "email": "<EMAIL>",
                "password": "testpassword123"
            }
            response = self.session.post(f"{self.base_url}/api/login", json=login_data, timeout=REQUEST_TIMEOUT)
            
            if response.status_code == 200:
                self.tokens['user'] = response.json().get('access_token')
                print("✅ User authentication successful")
            else:
                # Try to create user
                signup_data = {
                    "email": "<EMAIL>",
                    "password": "testpassword123",
                    "first_name": "Test",
                    "last_name": "User"
                }
                response = self.session.post(f"{self.base_url}/api/signup", json=signup_data, timeout=REQUEST_TIMEOUT)
                if response.status_code in [200, 201]:
                    # Try login again
                    response = self.session.post(f"{self.base_url}/api/login", json=login_data, timeout=REQUEST_TIMEOUT)
                    if response.status_code == 200:
                        self.tokens['user'] = response.json().get('access_token')
                        print("✅ User created and authenticated")
        except Exception as e:
            print(f"⚠️  User authentication failed: {e}")
        
        # Try to get admin token
        try:
            admin_data = {
                "email": "<EMAIL>",
                "password": "admin123"
            }
            response = self.session.post(f"{self.base_url}/api/admin/login", json=admin_data, timeout=REQUEST_TIMEOUT)
            if response.status_code == 200:
                self.tokens['admin'] = response.json().get('access_token')
                print("✅ Admin authentication successful")
        except Exception as e:
            print(f"⚠️  Admin authentication failed: {e}")
        
        # Try to get seller token
        try:
            seller_data = {
                "email": "<EMAIL>",
                "password": "sellerpassword123"
            }
            response = self.session.post(f"{self.base_url}/api/seller/login", json=seller_data, timeout=REQUEST_TIMEOUT)
            if response.status_code == 200:
                self.tokens['seller'] = response.json().get('access_token')
                print("✅ Seller authentication successful")
        except Exception as e:
            print(f"⚠️  Seller authentication failed: {e}")
    
    def _setup_test_data(self):
        """Setup test data for endpoints that require it"""
        self.test_data = {
            'product_id': 1,
            'order_id': 1,
            'user_id': 1,
            'seller_id': 1,
            'category_name': 'Electronics',
            'search_query': 'laptop',
            'guest_session_id': 'test-session-123'
        }
    
    def get_auth_headers(self, auth_type: str) -> Dict[str, str]:
        """Get authentication headers for different user types"""
        headers = {'Content-Type': 'application/json'}
        
        if auth_type in self.tokens and self.tokens[auth_type]:
            headers['Authorization'] = f'Bearer {self.tokens[auth_type]}'
        
        return headers
    
    def test_endpoint(self, endpoint: EndpointTest) -> TestResult:
        """Test a single endpoint"""
        start_time = time.time()
        
        try:
            # Prepare headers
            if endpoint.requires_admin:
                headers = self.get_auth_headers('admin')
            elif endpoint.requires_seller:
                headers = self.get_auth_headers('seller')
            elif endpoint.requires_auth:
                headers = self.get_auth_headers('user')
            else:
                headers = {'Content-Type': 'application/json'}
            
            # Prepare URL with test data substitution
            url = self._substitute_path_params(endpoint.path)
            full_url = urljoin(self.base_url, url)
            
            # Make request
            if endpoint.method.upper() == 'GET':
                response = self.session.get(full_url, headers=headers, timeout=REQUEST_TIMEOUT)
            elif endpoint.method.upper() == 'POST':
                response = self.session.post(full_url, headers=headers, json=endpoint.test_data, timeout=REQUEST_TIMEOUT)
            elif endpoint.method.upper() == 'PUT':
                response = self.session.put(full_url, headers=headers, json=endpoint.test_data, timeout=REQUEST_TIMEOUT)
            elif endpoint.method.upper() == 'DELETE':
                response = self.session.delete(full_url, headers=headers, timeout=REQUEST_TIMEOUT)
            elif endpoint.method.upper() == 'PATCH':
                response = self.session.patch(full_url, headers=headers, json=endpoint.test_data, timeout=REQUEST_TIMEOUT)
            else:
                raise ValueError(f"Unsupported HTTP method: {endpoint.method}")
            
            response_time = time.time() - start_time
            
            # Determine success
            expected_statuses = endpoint.expected_status or [200, 201, 202, 204]
            success = response.status_code in expected_statuses
            
            # Handle authentication required responses
            if response.status_code == 401 and (endpoint.requires_auth or endpoint.requires_admin or endpoint.requires_seller):
                success = True  # Expected for endpoints requiring auth when no token available
            
            # Handle rate limiting
            if response.status_code == 429:
                success = True  # Rate limiting is working as expected
            
            # Parse response data
            response_data = None
            try:
                response_data = response.json()
            except:
                response_data = {'raw_response': response.text[:200]}
            
            return TestResult(
                endpoint=endpoint,
                status_code=response.status_code,
                response_time=response_time,
                success=success,
                response_data=response_data
            )
            
        except Exception as e:
            response_time = time.time() - start_time
            return TestResult(
                endpoint=endpoint,
                status_code=0,
                response_time=response_time,
                success=False,
                error_message=str(e)
            )
    
    def _substitute_path_params(self, path: str) -> str:
        """Substitute path parameters with test data"""
        # Replace common path parameters
        substitutions = {
            '{id}': str(self.test_data['product_id']),
            '{product_id}': str(self.test_data['product_id']),
            '{order_id}': str(self.test_data['order_id']),
            '{user_id}': str(self.test_data['user_id']),
            '{seller_id}': str(self.test_data['seller_id']),
            '{category_name}': self.test_data['category_name'],
            '{session_id}': self.test_data['guest_session_id'],
            '<int:id>': str(self.test_data['product_id']),
            '<int:product_id>': str(self.test_data['product_id']),
            '<int:order_id>': str(self.test_data['order_id']),
            '<int:user_id>': str(self.test_data['user_id']),
            '<int:seller_id>': str(self.test_data['seller_id']),
            '<category_name>': self.test_data['category_name'],
            '<session_id>': self.test_data['guest_session_id']
        }
        
        result_path = path
        for placeholder, value in substitutions.items():
            result_path = result_path.replace(placeholder, value)
        
        return result_path
    
    def run_tests(self, endpoints: List[EndpointTest]) -> List[TestResult]:
        """Run all endpoint tests"""
        print(f"\n🧪 Testing {len(endpoints)} API endpoints...")
        print("=" * 80)
        
        results = []
        
        # Use ThreadPoolExecutor for concurrent testing (with rate limiting)
        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            # Submit all tests
            future_to_endpoint = {
                executor.submit(self.test_endpoint, endpoint): endpoint 
                for endpoint in endpoints
            }
            
            # Collect results as they complete
            for i, future in enumerate(as_completed(future_to_endpoint)):
                result = future.result()
                results.append(result)
                
                # Progress indicator
                progress = (i + 1) / len(endpoints) * 100
                status_icon = "✅" if result.success else "❌"
                print(f"{status_icon} [{progress:5.1f}%] {result.endpoint.method:6} {result.endpoint.path:50} ({result.status_code}) {result.response_time:.3f}s")
                
                # Rate limiting delay
                time.sleep(RATE_LIMIT_DELAY / MAX_WORKERS)
        
        self.results = results
        return results
    
    def generate_report(self) -> str:
        """Generate a comprehensive test report"""
        if not self.results:
            return "No test results available"
        
        # Calculate statistics
        total_tests = len(self.results)
        successful_tests = sum(1 for r in self.results if r.success)
        failed_tests = total_tests - successful_tests
        success_rate = (successful_tests / total_tests) * 100
        avg_response_time = sum(r.response_time for r in self.results) / total_tests
        
        # Group by category
        categories = {}
        for result in self.results:
            category = result.endpoint.category
            if category not in categories:
                categories[category] = {'total': 0, 'success': 0, 'failed': 0}
            categories[category]['total'] += 1
            if result.success:
                categories[category]['success'] += 1
            else:
                categories[category]['failed'] += 1
        
        # Generate report
        report = []
        report.append("🧪 COMPREHENSIVE API ENDPOINT TEST REPORT")
        report.append("=" * 80)
        report.append(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"🌐 Base URL: {self.base_url}")
        report.append(f"📊 Total Endpoints Tested: {total_tests}")
        report.append(f"✅ Successful Tests: {successful_tests}")
        report.append(f"❌ Failed Tests: {failed_tests}")
        report.append(f"📈 Success Rate: {success_rate:.1f}%")
        report.append(f"⏱️  Average Response Time: {avg_response_time:.3f}s")
        report.append("")
        
        # Category breakdown
        report.append("📋 RESULTS BY CATEGORY")
        report.append("-" * 80)
        for category, stats in sorted(categories.items()):
            success_pct = (stats['success'] / stats['total']) * 100
            report.append(f"{category:30} | {stats['success']:3}/{stats['total']:3} ({success_pct:5.1f}%)")
        report.append("")
        
        # Failed tests details
        if failed_tests > 0:
            report.append("❌ FAILED TESTS DETAILS")
            report.append("-" * 80)
            for result in self.results:
                if not result.success:
                    report.append(f"Method: {result.endpoint.method}")
                    report.append(f"Path: {result.endpoint.path}")
                    report.append(f"Status Code: {result.status_code}")
                    report.append(f"Error: {result.error_message or 'HTTP Error'}")
                    report.append(f"Response Time: {result.response_time:.3f}s")
                    report.append("-" * 40)
        
        return "\n".join(report)
    
    def save_detailed_report(self, filename: str = "api_test_report.json"):
        """Save detailed test results to JSON file"""
        detailed_results = []
        for result in self.results:
            detailed_results.append({
                'endpoint': {
                    'method': result.endpoint.method,
                    'path': result.endpoint.path,
                    'description': result.endpoint.description,
                    'category': result.endpoint.category,
                    'requires_auth': result.endpoint.requires_auth,
                    'requires_admin': result.endpoint.requires_admin,
                    'requires_seller': result.endpoint.requires_seller
                },
                'result': {
                    'status_code': result.status_code,
                    'response_time': result.response_time,
                    'success': result.success,
                    'error_message': result.error_message,
                    'response_data': result.response_data
                }
            })
        
        report_data = {
            'test_metadata': {
                'timestamp': datetime.now().isoformat(),
                'base_url': self.base_url,
                'total_tests': len(self.results),
                'successful_tests': sum(1 for r in self.results if r.success),
                'failed_tests': sum(1 for r in self.results if not r.success)
            },
            'results': detailed_results
        }
        
        with open(filename, 'w') as f:
            json.dump(report_data, f, indent=2)
        
        print(f"📄 Detailed report saved to: {filename}")

def get_all_endpoints() -> List[EndpointTest]:
    """Define all API endpoints to test"""
    endpoints = []
    
    # Health & System Endpoints
    endpoints.extend([
        EndpointTest("GET", "/", "Root endpoint", category="System"),
        EndpointTest("GET", "/api/health", "Health check", category="System"),
        EndpointTest("GET", "/api/health/status", "System status", category="System"),
        EndpointTest("GET", "/sitemap.xml", "SEO sitemap", category="System"),
        EndpointTest("GET", "/robots.txt", "Robots.txt", category="System"),
    ])
    
    # Authentication Endpoints
    endpoints.extend([
        EndpointTest("POST", "/api/signup", "User registration", 
                    test_data={"email": "<EMAIL>", "password": "password123", "first_name": "New", "last_name": "User"},
                    expected_status=[200, 201, 400, 409], category="Authentication"),
        EndpointTest("POST", "/api/login", "User login",
                    test_data={"email": "<EMAIL>", "password": "testpassword123"},
                    expected_status=[200, 401], category="Authentication"),
        EndpointTest("POST", "/api/auth/refresh", "Refresh token", requires_auth=True, category="Authentication"),
        EndpointTest("POST", "/api/auth/logout", "User logout", requires_auth=True, category="Authentication"),
        EndpointTest("GET", "/api/oauth/providers", "OAuth providers", category="Authentication"),
        EndpointTest("POST", "/api/oauth/google", "Google OAuth",
                    test_data={"token": "fake_google_token"}, expected_status=[200, 400, 401], category="Authentication"),
    ])
    
    # Product Endpoints
    endpoints.extend([
        EndpointTest("GET", "/api/products", "Get products", category="Products"),
        EndpointTest("GET", "/api/products/<int:product_id>", "Get product details", category="Products"),
        EndpointTest("GET", "/api/categories", "Get categories", category="Products"),
        EndpointTest("GET", "/api/categories/<category_name>/products", "Get category products", category="Products"),
        EndpointTest("GET", "/api/products/best-sellers", "Get best sellers", category="Products"),
        EndpointTest("GET", "/api/products/new-arrivals", "Get new arrivals", category="Products"),
        EndpointTest("POST", "/api/products/batch", "Get products batch",
                    test_data={"product_ids": [1, 2, 3]}, category="Products"),
        EndpointTest("GET", "/api/products/<int:product_id>/reviews", "Get product reviews", category="Products"),
        EndpointTest("GET", "/api/products/<int:product_id>/images", "Get product images", category="Products"),
        EndpointTest("GET", "/api/products/<int:product_id>/variants", "Get product variants", category="Products"),
    ])
    
    # Search Endpoints
    endpoints.extend([
        EndpointTest("GET", "/api/search", "Advanced search", category="Search"),
        EndpointTest("GET", "/api/search/suggestions", "Search suggestions", category="Search"),
        EndpointTest("GET", "/api/search/autocomplete", "Search autocomplete", category="Search"),
        EndpointTest("GET", "/api/search/filters", "Search filters", category="Search"),
        EndpointTest("GET", "/api/search/similar/<int:product_id>", "Similar products search", category="Search"),
        EndpointTest("POST", "/api/search/complex", "Complex boolean search",
                    test_data={"search_terms": [{"query": "laptop", "operator": "must"}]}, category="Search"),
        EndpointTest("GET", "/api/search/health", "Search system health", category="Search"),
    ])

    # ML & Recommendations Endpoints
    endpoints.extend([
        EndpointTest("GET", "/api/recommendations", "Get recommendations", category="ML & AI"),
        EndpointTest("GET", "/api/recommendations/personalized", "Personalized recommendations", requires_auth=True, category="ML & AI"),
        EndpointTest("GET", "/api/recommendations/similar/<int:product_id>", "Similar product recommendations", category="ML & AI"),
        EndpointTest("GET", "/api/recommendations/advanced", "Advanced recommendations", category="ML & AI"),
        EndpointTest("GET", "/api/recommendations/advanced/user/<int:user_id>", "Advanced user recommendations", category="ML & AI"),
        EndpointTest("POST", "/api/visual_search", "Visual search",
                    test_data={"image": "base64_image_data"}, expected_status=[200, 400], category="ML & AI"),
        EndpointTest("GET", "/api/inventory_predictions", "Inventory predictions", category="ML & AI"),
        EndpointTest("GET", "/api/advanced_inventory_predictions", "Advanced inventory predictions", category="ML & AI"),
        EndpointTest("GET", "/api/price_trends", "Price trends", category="ML & AI"),
        EndpointTest("GET", "/api/advanced_price_trends", "Advanced price trends", category="ML & AI"),
    ])

    # User Profile & Account Endpoints
    endpoints.extend([
        EndpointTest("GET", "/api/profile", "Get user profile", requires_auth=True, category="User Account"),
        EndpointTest("PUT", "/api/profile", "Update user profile", requires_auth=True,
                    test_data={"first_name": "Updated", "last_name": "User"}, category="User Account"),
        EndpointTest("GET", "/api/addresses", "Get user addresses", requires_auth=True, category="User Account"),
        EndpointTest("POST", "/api/addresses", "Add user address", requires_auth=True,
                    test_data={"street": "123 Test St", "city": "Test City", "state": "TS", "zip_code": "12345"}, category="User Account"),
        EndpointTest("GET", "/api/payment-methods", "Get payment methods", requires_auth=True, category="User Account"),
        EndpointTest("GET", "/api/recently-viewed", "Get recently viewed", requires_auth=True, category="User Account"),
    ])

    # Shopping Cart Endpoints
    endpoints.extend([
        EndpointTest("GET", "/api/cart", "Get cart", requires_auth=True, category="Shopping Cart"),
        EndpointTest("POST", "/api/cart", "Add to cart", requires_auth=True,
                    test_data={"product_id": 1, "quantity": 1}, category="Shopping Cart"),
        EndpointTest("GET", "/api/cart/smart-bundles", "Get smart bundles", requires_auth=True, category="Shopping Cart"),
        EndpointTest("GET", "/api/cart/saved", "Get saved carts", requires_auth=True, category="Shopping Cart"),
        EndpointTest("POST", "/api/cart/save", "Save cart", requires_auth=True, category="Shopping Cart"),
        EndpointTest("POST", "/api/shipping/calculate", "Calculate shipping",
                    test_data={"address": {"city": "Test", "state": "TS", "zip": "12345"}}, category="Shopping Cart"),
        EndpointTest("POST", "/api/tax/calculate", "Calculate tax",
                    test_data={"address": {"city": "Test", "state": "TS", "zip": "12345"}}, category="Shopping Cart"),
        EndpointTest("POST", "/api/coupon/validate", "Validate coupon",
                    test_data={"code": "TESTCODE"}, expected_status=[200, 400, 404], category="Shopping Cart"),
    ])

    # Order Management Endpoints
    endpoints.extend([
        EndpointTest("GET", "/api/orders", "Get user orders", requires_auth=True, category="Orders"),
        EndpointTest("POST", "/api/orders", "Create order", requires_auth=True,
                    test_data={"items": [{"product_id": 1, "quantity": 1}]}, category="Orders"),
        EndpointTest("GET", "/api/orders/<int:order_id>", "Get order details", requires_auth=True, category="Orders"),
        EndpointTest("POST", "/api/checkout/guest", "Guest checkout",
                    test_data={"email": "<EMAIL>", "items": [{"product_id": 1, "quantity": 1}]}, category="Orders"),
    ])

    # Wishlist Endpoints
    endpoints.extend([
        EndpointTest("GET", "/api/wishlist", "Get wishlist", requires_auth=True, category="Wishlist"),
        EndpointTest("POST", "/api/wishlist", "Add to wishlist", requires_auth=True,
                    test_data={"product_id": 1}, category="Wishlist"),
    ])

    # Community Endpoints
    endpoints.extend([
        EndpointTest("GET", "/api/community_posts", "Get community posts", category="Community"),
        EndpointTest("POST", "/api/community_posts", "Create community post", requires_auth=True,
                    test_data={"content": "Test post", "title": "Test"}, category="Community"),
        EndpointTest("GET", "/api/community/trending-topics", "Get trending topics", category="Community"),
        EndpointTest("GET", "/api/community/stats", "Get community stats", category="Community"),
    ])

    # Analytics Endpoints
    endpoints.extend([
        EndpointTest("POST", "/api/analytics/search", "Log search analytics",
                    test_data={"query": "test", "results_count": 10}, category="Analytics"),
        EndpointTest("POST", "/api/analytics/visual-search", "Log visual search analytics",
                    test_data={"query_type": "image", "results_count": 5}, category="Analytics"),
        EndpointTest("GET", "/api/analytics/best-sellers", "Get best sellers analytics", category="Analytics"),
    ])

    # Payment Endpoints
    endpoints.extend([
        EndpointTest("GET", "/api/payment/gateways", "Get payment gateways", category="Payments"),
        EndpointTest("POST", "/api/payment/process", "Process payment", requires_auth=True,
                    test_data={"amount": 100, "gateway": "stripe", "token": "test_token"},
                    expected_status=[200, 400, 402], category="Payments"),
        EndpointTest("GET", "/api/invoices", "Get invoices", requires_auth=True, category="Payments"),
        EndpointTest("GET", "/api/refunds", "Get refunds", requires_auth=True, category="Payments"),
    ])

    # Admin Endpoints
    endpoints.extend([
        EndpointTest("POST", "/api/admin/login", "Admin login",
                    test_data={"email": "<EMAIL>", "password": "admin123"},
                    expected_status=[200, 401], category="Admin"),
        EndpointTest("GET", "/api/admin/dashboard", "Admin dashboard", requires_admin=True, category="Admin"),
        EndpointTest("GET", "/api/admin/products", "Admin get products", requires_admin=True, category="Admin"),
        EndpointTest("GET", "/api/admin/orders", "Admin get orders", requires_admin=True, category="Admin"),
        EndpointTest("GET", "/api/admin/users", "Admin get users", requires_admin=True, category="Admin"),
        EndpointTest("GET", "/api/admin/inventory", "Admin inventory", requires_admin=True, category="Admin"),
        EndpointTest("GET", "/api/admin/analytics/sales", "Admin sales analytics", requires_admin=True, category="Admin"),
    ])

    # Seller Endpoints
    endpoints.extend([
        EndpointTest("POST", "/api/seller/register", "Seller registration",
                    test_data={"email": "<EMAIL>", "business_name": "Test Business"},
                    expected_status=[200, 201, 400, 409], category="Seller"),
        EndpointTest("POST", "/api/seller/login", "Seller login",
                    test_data={"email": "<EMAIL>", "password": "sellerpassword123"},
                    expected_status=[200, 401], category="Seller"),
        EndpointTest("GET", "/api/seller/dashboard", "Seller dashboard", requires_seller=True, category="Seller"),
        EndpointTest("GET", "/api/seller/products", "Seller products", requires_seller=True, category="Seller"),
        EndpointTest("GET", "/api/seller/orders", "Seller orders", requires_seller=True, category="Seller"),
        EndpointTest("GET", "/api/seller/earnings", "Seller earnings", requires_seller=True, category="Seller"),
    ])

    # Support & Misc Endpoints
    endpoints.extend([
        EndpointTest("POST", "/api/support/contact", "Contact support",
                    test_data={"name": "Test User", "email": "<EMAIL>", "message": "Test message"}, category="Support"),
        EndpointTest("GET", "/api/support/status", "Support status", category="Support"),
        EndpointTest("POST", "/api/newsletter/subscribe", "Newsletter subscribe",
                    test_data={"email": "<EMAIL>"}, expected_status=[200, 400, 409], category="Support"),
        EndpointTest("GET", "/api/cookie-consent", "Get cookie consent", category="Support"),
        EndpointTest("POST", "/api/cookie-consent", "Save cookie consent",
                    test_data={"analytics": True, "marketing": False}, category="Support"),
    ])

    # Error & Performance Endpoints
    endpoints.extend([
        EndpointTest("POST", "/api/errors", "Log error",
                    test_data={"error": "Test error", "url": "/test"}, category="System"),
        EndpointTest("POST", "/api/performance/metrics", "Log performance metrics",
                    test_data={"page": "/test", "load_time": 1000}, category="System"),
    ])

    return endpoints

def main():
    """Main function to run all endpoint tests"""
    print("🚀 ALLORA API ENDPOINT COMPREHENSIVE TESTING")
    print("=" * 80)
    
    # Initialize tester
    tester = APITester()
    
    # Setup test environment
    if not tester.setup_test_environment():
        print("❌ Failed to setup test environment")
        sys.exit(1)
    
    # Get all endpoints to test
    endpoints = get_all_endpoints()
    
    # Run tests
    results = tester.run_tests(endpoints)
    
    # Generate and display report
    report = tester.generate_report()
    print("\n" + report)
    
    # Save detailed report
    tester.save_detailed_report()
    
    # Exit with appropriate code
    failed_count = sum(1 for r in results if not r.success)
    sys.exit(0 if failed_count == 0 else 1)

if __name__ == "__main__":
    main()
