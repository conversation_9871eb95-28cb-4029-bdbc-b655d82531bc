"""
Standardized Authentication Decorators
======================================

Unified authentication and authorization decorators for all blueprints.
Replaces the inconsistent authentication patterns throughout the codebase.
"""

from functools import wraps
from flask import request, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt, verify_jwt_in_request
from typing import Optional, Callable, Any
import logging

from .response_wrapper import unauthorized_response, forbidden_response
from .exceptions import AuthenticationError, AuthorizationError

logger = logging.getLogger(__name__)

def jwt_required_v2(optional: bool = False):
    """
    Standardized JWT authentication decorator.
    
    Args:
        optional: If True, authentication is optional
    
    Returns:
        Decorator function
    """
    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                if optional:
                    # Try to verify JWT but don't fail if not present
                    try:
                        verify_jwt_in_request(optional=True)
                        user_id = get_jwt_identity()
                    except:
                        user_id = None
                else:
                    # Require JWT
                    verify_jwt_in_request()
                    user_id = get_jwt_identity()
                    
                    if not user_id:
                        return unauthorized_response("Valid authentication token required")
                
                # Get user from database
                user = None
                if user_id:
                    try:
                        from app import User
                        user = User.query.get(user_id)
                        if not user or not user.is_active:
                            return unauthorized_response("User account is inactive")
                    except Exception as e:
                        logger.error(f"Error fetching user: {e}")
                        if not optional:
                            return unauthorized_response("Authentication failed")
                
                # Pass user to the endpoint
                return f(user, *args, **kwargs)
                
            except Exception as e:
                logger.error(f"Authentication error: {e}")
                if optional:
                    return f(None, *args, **kwargs)
                return unauthorized_response("Authentication failed")
        
        return decorated_function
    return decorator

def admin_required_v2(permission: Optional[str] = None):
    """
    Standardized admin authentication decorator.
    
    Args:
        permission: Specific admin permission required
    
    Returns:
        Decorator function
    """
    def decorator(f: Callable) -> Callable:
        @wraps(f)
        @jwt_required_v2()
        def decorated_function(user, *args, **kwargs):
            if not user:
                return unauthorized_response("Authentication required")
            
            try:
                from app import AdminUser
                admin_user = AdminUser.query.filter_by(user_id=user.id).first()
                
                if not admin_user:
                    return forbidden_response("Admin access required")
                
                # Check specific permission if provided
                if permission:
                    if not hasattr(admin_user, permission) or not getattr(admin_user, permission):
                        return forbidden_response(f"Permission '{permission}' required")
                
                # Pass admin_user to the endpoint
                return f(user, admin_user, *args, **kwargs)
                
            except Exception as e:
                logger.error(f"Admin authorization error: {e}")
                return forbidden_response("Authorization failed")
        
        return decorated_function
    return decorator

def seller_required_v2():
    """
    Standardized seller authentication decorator.
    
    Returns:
        Decorator function
    """
    def decorator(f: Callable) -> Callable:
        @wraps(f)
        @jwt_required_v2()
        def decorated_function(user, *args, **kwargs):
            if not user:
                return unauthorized_response("Authentication required")
            
            try:
                from app import Seller
                seller = Seller.query.filter_by(user_id=user.id).first()
                
                if not seller:
                    return forbidden_response("Seller access required")
                
                if seller.status != 'approved':
                    return forbidden_response("Seller account not approved")
                
                # Pass seller to the endpoint
                return f(user, seller, *args, **kwargs)
                
            except Exception as e:
                logger.error(f"Seller authorization error: {e}")
                return forbidden_response("Authorization failed")
        
        return decorated_function
    return decorator

def optional_auth():
    """
    Optional authentication decorator.
    User will be None if not authenticated.
    
    Returns:
        Decorator function
    """
    return jwt_required_v2(optional=True)

def rate_limit_v2(limit: int, window: int, per: str = 'ip', block_duration: int = None):
    """
    Standardized rate limiting decorator.
    
    Args:
        limit: Number of requests allowed
        window: Time window in seconds
        per: Rate limit per ('ip', 'user')
        block_duration: How long to block after limit exceeded
    
    Returns:
        Decorator function
    """
    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Import rate limiting from existing system
            try:
                from app import rate_limit
                # Apply existing rate limit decorator
                rate_limited_func = rate_limit(
                    limit=limit,
                    window=window,
                    per=per,
                    block_duration=block_duration
                )(f)
                return rate_limited_func(*args, **kwargs)
            except Exception as e:
                logger.error(f"Rate limiting error: {e}")
                # Continue without rate limiting if it fails
                return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def validate_content_type(content_type: str = 'application/json'):
    """
    Validate request content type.
    
    Args:
        content_type: Expected content type
    
    Returns:
        Decorator function
    """
    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if request.method in ['POST', 'PUT', 'PATCH']:
                if not request.is_json and content_type == 'application/json':
                    from .response_wrapper import error_response
                    return error_response(
                        message="Content-Type must be application/json",
                        status_code=415,
                        error_code="UNSUPPORTED_MEDIA_TYPE"
                    )
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator
