#!/usr/bin/env python3
"""
Check what products are in the database
"""

import sys
sys.path.append('.')

from app import app, Product

def main():
    """Check product names in database"""
    with app.app_context():
        products = Product.query.limit(20).all()
        print('Sample product names:')
        for p in products:
            print(f'  - {p.name} (Brand: {p.brand}, Category: {p.category})')

if __name__ == "__main__":
    main()
