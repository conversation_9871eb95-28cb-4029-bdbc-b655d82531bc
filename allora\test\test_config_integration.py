#!/usr/bin/env python3
"""
Configuration Integration Test Suite
===================================

Comprehensive testing for the centralized configuration system integration.
Tests config.py integration across the entire application.

Author: Allora Development Team
Date: 2025-07-13
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import unittest
from unittest.mock import patch, MagicMock

def test_config_module_structure():
    """Test the config module structure and classes"""
    print("🔍 Testing Config Module Structure")
    print("=" * 45)
    
    try:
        # Import config module
        import config
        
        print("✅ Config module imported successfully")
        
        # Test config classes
        required_classes = ['Config', 'DevelopmentConfig', 'ProductionConfig', 'TestingConfig']
        for class_name in required_classes:
            if hasattr(config, class_name):
                print(f"   ✅ {class_name}: Found")
            else:
                print(f"   ❌ {class_name}: Missing")
        
        # Test config functions
        required_functions = ['get_config', 'get_search_config', 'get_analytics_config']
        for func_name in required_functions:
            if hasattr(config, func_name):
                print(f"   ✅ {func_name}: Found")
            else:
                print(f"   ❌ {func_name}: Missing")
        
        # Test config mapping
        if hasattr(config, 'config') and isinstance(config.config, dict):
            print("   ✅ Config mapping: Found")
            print(f"      Available environments: {list(config.config.keys())}")
        else:
            print("   ❌ Config mapping: Missing")
        
        return True
        
    except ImportError as e:
        print(f"❌ Config module import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Config module test error: {e}")
        return False

def test_config_values():
    """Test configuration values and environment handling"""
    print("\n🔧 Testing Configuration Values")
    print("=" * 40)
    
    try:
        from config import get_config, DevelopmentConfig, ProductionConfig, TestingConfig
        
        # Test development config
        dev_config = DevelopmentConfig()
        print("✅ Development config instantiated")
        print(f"   Debug: {getattr(dev_config, 'DEBUG', 'Not set')}")
        print(f"   Testing: {getattr(dev_config, 'TESTING', 'Not set')}")
        print(f"   Rate Limiting: {getattr(dev_config, 'RATE_LIMIT_ENABLED', 'Not set')}")
        
        # Test production config
        prod_config = ProductionConfig()
        print("✅ Production config instantiated")
        print(f"   Debug: {getattr(prod_config, 'DEBUG', 'Not set')}")
        print(f"   HTTPS Redirect: {getattr(prod_config, 'HTTPS_REDIRECT', 'Not set')}")
        print(f"   Rate Limiting: {getattr(prod_config, 'RATE_LIMIT_ENABLED', 'Not set')}")
        
        # Test testing config
        test_config = TestingConfig()
        print("✅ Testing config instantiated")
        print(f"   Testing: {getattr(test_config, 'TESTING', 'Not set')}")
        print(f"   Database URI: {getattr(test_config, 'SQLALCHEMY_DATABASE_URI', 'Not set')}")
        
        # Test get_config function
        config_class = get_config('development')
        print(f"✅ get_config('development'): {config_class.__name__}")
        
        config_class = get_config('production')
        print(f"✅ get_config('production'): {config_class.__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ Config values test error: {e}")
        return False

def test_app_integration():
    """Test Flask app integration with centralized config"""
    print("\n🔗 Testing Flask App Integration")
    print("=" * 40)
    
    try:
        # Import Flask app
        from app import app
        
        print("✅ Flask app imported successfully")
        
        # Test if config is applied
        config_items = [
            'SECRET_KEY',
            'JWT_SECRET_KEY',
            'SQLALCHEMY_DATABASE_URI',
            'SQLALCHEMY_TRACK_MODIFICATIONS',
            'CORS_ORIGINS'
        ]

        for item in config_items:
            if item in app.config:
                print(f"   ✅ {item}: Configured")
                if item == 'CORS_ORIGINS':
                    print(f"      Value: {app.config[item]}")
            else:
                print(f"   ❌ {item}: Missing")

        # Test SQLAlchemy engine options
        if 'SQLALCHEMY_ENGINE_OPTIONS' in app.config:
            engine_options = app.config['SQLALCHEMY_ENGINE_OPTIONS']
            print(f"   ✅ SQLALCHEMY_ENGINE_OPTIONS: Configured")
            print(f"      Pool size: {engine_options.get('pool_size', 'Not set')}")
            print(f"      Max overflow: {engine_options.get('max_overflow', 'Not set')}")
        else:
            print(f"   ❌ SQLALCHEMY_ENGINE_OPTIONS: Missing")
        
        # Test JWT configuration
        jwt_items = ['JWT_ACCESS_TOKEN_EXPIRES', 'JWT_REFRESH_TOKEN_EXPIRES']
        for item in jwt_items:
            if item in app.config:
                print(f"   ✅ {item}: {app.config[item]}")
            else:
                print(f"   ❌ {item}: Missing")
        
        return True
        
    except Exception as e:
        print(f"❌ App integration test error: {e}")
        return False

def test_redis_integration():
    """Test Redis configuration integration"""
    print("\n🔴 Testing Redis Integration")
    print("=" * 35)
    
    try:
        from redis_config import get_redis_config
        
        # Test Redis config
        redis_config = get_redis_config()
        print("✅ Redis config imported successfully")
        print(f"   Host: {redis_config.host}")
        print(f"   Port: {redis_config.port}")
        print(f"   DB: {redis_config.db}")
        print(f"   Available: {redis_config.is_available()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Redis integration test error: {e}")
        return False

def test_search_integration():
    """Test search system configuration integration"""
    print("\n🔍 Testing Search System Integration")
    print("=" * 45)
    
    try:
        from config import get_search_config
        
        # Test search config
        search_config = get_search_config()
        print("✅ Search config retrieved successfully")
        print(f"   Enabled: {search_config.get('enabled')}")
        print(f"   Elasticsearch URL: {search_config.get('elasticsearch_url')}")
        print(f"   Fallback Enabled: {search_config.get('fallback_enabled')}")
        print(f"   Default Page Size: {search_config.get('default_page_size')}")
        
        # Test Elasticsearch config
        try:
            from search_system.elasticsearch_config import ElasticsearchConfig
            es_config = ElasticsearchConfig()
            print("✅ Elasticsearch config instantiated")
            print(f"   Enabled: {es_config.enabled}")
            print(f"   Host: {es_config.es_host}")
            print(f"   Port: {es_config.es_port}")
        except Exception as es_error:
            print(f"⚠️  Elasticsearch config error: {es_error}")
        
        return True
        
    except Exception as e:
        print(f"❌ Search integration test error: {e}")
        return False

def test_environment_switching():
    """Test environment-based configuration switching"""
    print("\n🌍 Testing Environment Switching")
    print("=" * 40)
    
    try:
        from config import get_config
        
        # Test different environments
        environments = ['development', 'production', 'testing', 'invalid_env']
        
        for env in environments:
            try:
                config_class = get_config(env)
                config_instance = config_class()
                print(f"✅ {env}: {config_class.__name__}")
                print(f"   Debug: {getattr(config_instance, 'DEBUG', 'Not set')}")
                print(f"   Testing: {getattr(config_instance, 'TESTING', 'Not set')}")
            except Exception as env_error:
                print(f"❌ {env}: Error - {env_error}")
        
        return True
        
    except Exception as e:
        print(f"❌ Environment switching test error: {e}")
        return False

def run_all_tests():
    """Run all configuration integration tests"""
    print("🚀 Configuration Integration Test Suite")
    print("=" * 50)
    print()
    
    tests = [
        test_config_module_structure,
        test_config_values,
        test_app_integration,
        test_redis_integration,
        test_search_integration,
        test_environment_switching
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 30)
    passed = sum(results)
    total = len(results)
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! Configuration integration is working correctly.")
    else:
        print("⚠️  Some tests failed. Please review the configuration integration.")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
