"""
DEPRECATED: FastAPI WebSocket Routes - NOT COMPATIBLE WITH FLASK
==============================================================

This file contains FastAPI WebSocket routes that are NOT compatible
with the Flask application. Use flask_socketio_manager.py instead.

Issues:
- Missing auth.py file (import error)
- Missing database.py file (import error)  
- FastAPI routes cannot be served by Flask app

Status: DEPRECATED - DO NOT IMPORT
Alternative: Use flask_socketio_manager.py for all WebSocket functionality

Date Deprecated: 2025-07-13
"""

"""
WebSocket Routes for FastAPI
Defines WebSocket endpoints and integrates with the connection manager
"""

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, Query
from fastapi.security import HTTPBearer
from typing import Optional
import logging
from websocket_manager import websocket_endpoint, manager
# from auth import get_current_user_optional  # FIXED: File does not exist
# from database import get_db  # FIXED: File does not exist
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)
security = HTTPBearer(auto_error=False)

router = APIRouter()

@router.websocket("/ws")
async def websocket_connect(
    websocket: WebSocket,
    token: Optional[str] = Query(None),
    session_id: Optional[str] = Query(None)
):
    """
    WebSocket connection endpoint
    Supports both authenticated users (with token) and guest users (with session_id)
    """
    user_id = None
    
    # Try to authenticate user if token is provided
    if token:
        try:
            # Verify token and get user
            db = next(get_db())
            user = await get_current_user_optional(token, db)
            if user:
                user_id = str(user.id)
        except Exception as e:
            logger.warning(f"WebSocket authentication failed: {e}")
            # Continue as guest user
    
    # Handle the WebSocket connection
    await websocket_endpoint(websocket, user_id, session_id)

@router.websocket("/ws/user/{user_id}")
async def websocket_user_connect(
    websocket: WebSocket,
    user_id: str,
    token: str = Query(...)
):
    """
    WebSocket connection endpoint for authenticated users
    Requires valid authentication token
    """
    try:
        # Verify token and user authorization
        db = next(get_db())
        current_user = await get_current_user_optional(token, db)
        
        if not current_user or str(current_user.id) != user_id:
            await websocket.close(code=4001, reason="Unauthorized")
            return
            
        # Handle the WebSocket connection
        await websocket_endpoint(websocket, user_id, None)
        
    except Exception as e:
        logger.error(f"WebSocket user connection error: {e}")
        await websocket.close(code=4000, reason="Connection error")

@router.websocket("/ws/guest/{session_id}")
async def websocket_guest_connect(
    websocket: WebSocket,
    session_id: str
):
    """
    WebSocket connection endpoint for guest users
    Uses session_id for identification
    """
    try:
        # Validate session_id format (basic validation)
        if not session_id or len(session_id) < 10:
            await websocket.close(code=4002, reason="Invalid session ID")
            return
            
        # Handle the WebSocket connection
        await websocket_endpoint(websocket, None, session_id)
        
    except Exception as e:
        logger.error(f"WebSocket guest connection error: {e}")
        await websocket.close(code=4000, reason="Connection error")

# API endpoints for triggering WebSocket events
@router.post("/api/websocket/broadcast")
async def broadcast_message(
    message: dict,
    current_user = Depends(get_current_user_optional)
):
    """
    API endpoint to broadcast messages to all connected clients
    Requires admin privileges
    """
    # Check if user has admin privileges
    if not current_user or not getattr(current_user, 'is_admin', False):
        return {"error": "Unauthorized"}
    
    await manager.broadcast(message)
    return {"status": "Message broadcasted successfully"}

@router.post("/api/websocket/notify-user/{user_id}")
async def notify_user(
    user_id: str,
    message: dict,
    current_user = Depends(get_current_user_optional)
):
    """
    API endpoint to send message to specific user
    Requires admin privileges or self-notification
    """
    # Check authorization
    if not current_user:
        return {"error": "Unauthorized"}
    
    is_admin = getattr(current_user, 'is_admin', False)
    is_self = str(current_user.id) == user_id
    
    if not (is_admin or is_self):
        return {"error": "Unauthorized"}
    
    await manager.send_to_user(message, user_id)
    return {"status": "Message sent successfully"}

@router.get("/api/websocket/connections")
async def get_connection_stats(
    current_user = Depends(get_current_user_optional)
):
    """
    API endpoint to get WebSocket connection statistics
    Requires admin privileges
    """
    if not current_user or not getattr(current_user, 'is_admin', False):
        return {"error": "Unauthorized"}
    
    stats = {
        "total_connections": len(manager.all_connections),
        "authenticated_users": len(manager.active_connections),
        "guest_sessions": len(manager.guest_connections),
        "active_user_connections": {
            user_id: len(connections) 
            for user_id, connections in manager.active_connections.items()
        },
        "active_guest_connections": {
            session_id: len(connections) 
            for session_id, connections in manager.guest_connections.items()
        }
    }
    
    return stats

# Health check endpoint for WebSocket service
@router.get("/api/websocket/health")
async def websocket_health_check():
    """
    Health check endpoint for WebSocket service
    """
    try:
        # Test Redis connection
        manager.redis_client.ping()
        redis_status = "healthy"
    except Exception as e:
        redis_status = f"unhealthy: {str(e)}"
    
    return {
        "status": "healthy",
        "websocket_connections": len(manager.all_connections),
        "redis_status": redis_status,
        "timestamp": "2024-01-01T00:00:00Z"  # This would be datetime.now().isoformat() in real implementation
    }

# Event trigger endpoints (for integration with other services)
@router.post("/api/websocket/events/cart-update")
async def trigger_cart_update(
    event_data: dict,
    current_user = Depends(get_current_user_optional)
):
    """
    Trigger cart update event via WebSocket
    """
    user_id = event_data.get("user_id")
    session_id = event_data.get("session_id")
    cart_data = event_data.get("cart_data", {})
    
    # Import the notification function
    from websocket_manager import notify_cart_update
    await notify_cart_update(user_id, session_id, cart_data)
    
    return {"status": "Cart update notification sent"}

@router.post("/api/websocket/events/inventory-update")
async def trigger_inventory_update(
    event_data: dict,
    current_user = Depends(get_current_user_optional)
):
    """
    Trigger inventory update event via WebSocket
    """
    if not current_user or not getattr(current_user, 'is_admin', False):
        return {"error": "Unauthorized"}
    
    product_id = event_data.get("product_id")
    new_stock = event_data.get("new_stock")
    
    if product_id is None or new_stock is None:
        return {"error": "Missing required fields: product_id, new_stock"}
    
    # Import the notification function
    from websocket_manager import notify_inventory_update
    await notify_inventory_update(product_id, new_stock)
    
    return {"status": "Inventory update notification sent"}

@router.post("/api/websocket/events/order-status")
async def trigger_order_status_update(
    event_data: dict,
    current_user = Depends(get_current_user_optional)
):
    """
    Trigger order status update event via WebSocket
    """
    if not current_user or not getattr(current_user, 'is_admin', False):
        return {"error": "Unauthorized"}
    
    user_id = event_data.get("user_id")
    order_id = event_data.get("order_id")
    status = event_data.get("status")
    
    if not all([user_id, order_id, status]):
        return {"error": "Missing required fields: user_id, order_id, status"}
    
    # Import the notification function
    from websocket_manager import notify_order_status_update
    await notify_order_status_update(user_id, order_id, status)
    
    return {"status": "Order status update notification sent"}

@router.post("/api/websocket/events/price-update")
async def trigger_price_update(
    event_data: dict,
    current_user = Depends(get_current_user_optional)
):
    """
    Trigger price update event via WebSocket
    """
    if not current_user or not getattr(current_user, 'is_admin', False):
        return {"error": "Unauthorized"}
    
    product_id = event_data.get("product_id")
    new_price = event_data.get("new_price")
    old_price = event_data.get("old_price")
    
    if not all([product_id is not None, new_price is not None, old_price is not None]):
        return {"error": "Missing required fields: product_id, new_price, old_price"}
    
    # Import the notification function
    from websocket_manager import notify_price_update
    await notify_price_update(product_id, new_price, old_price)
    
    return {"status": "Price update notification sent"}

# Include router in main FastAPI app
def include_websocket_routes(app):
    """
    Function to include WebSocket routes in the main FastAPI application
    """
    app.include_router(router, tags=["websocket"])
    
    # Add startup event to initialize WebSocket manager
    @app.on_event("startup")
    async def startup_websocket():
        logger.info("WebSocket manager initialized")
    
    # Add shutdown event to cleanup WebSocket connections
    @app.on_event("shutdown")
    async def shutdown_websocket():
        # Close all active connections
        for websocket in manager.all_connections.copy():
            try:
                await websocket.close()
            except Exception as e:
                logger.error(f"Error closing WebSocket connection: {e}")
        
        logger.info("WebSocket manager shutdown complete")
