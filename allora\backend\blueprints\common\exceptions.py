"""
Custom API Exceptions
====================

Standardized exception classes for consistent error handling.
"""

class APIException(Exception):
    """Base API exception class."""
    
    def __init__(self, message: str, status_code: int = 400, error_code: str = None):
        super().__init__(message)
        self.message = message
        self.status_code = status_code
        self.error_code = error_code

class ValidationError(APIException):
    """Raised when request validation fails."""
    
    def __init__(self, message: str = "Validation failed", details: dict = None):
        super().__init__(message, status_code=422, error_code="VALIDATION_ERROR")
        self.details = details or {}

class AuthenticationError(APIException):
    """Raised when authentication fails."""
    
    def __init__(self, message: str = "Authentication failed"):
        super().__init__(message, status_code=401, error_code="AUTHENTICATION_ERROR")

class AuthorizationError(APIException):
    """Raised when authorization fails."""
    
    def __init__(self, message: str = "Access forbidden"):
        super().__init__(message, status_code=403, error_code="AUTHORIZATION_ERROR")

class NotFoundError(APIException):
    """Raised when a resource is not found."""
    
    def __init__(self, message: str = "Resource not found"):
        super().__init__(message, status_code=404, error_code="NOT_FOUND")

class ConflictError(APIException):
    """Raised when there's a conflict with existing data."""
    
    def __init__(self, message: str = "Conflict with existing data"):
        super().__init__(message, status_code=409, error_code="CONFLICT")

class RateLimitError(APIException):
    """Raised when rate limit is exceeded."""
    
    def __init__(self, message: str = "Rate limit exceeded"):
        super().__init__(message, status_code=429, error_code="RATE_LIMIT_EXCEEDED")

class ServerError(APIException):
    """Raised for internal server errors."""
    
    def __init__(self, message: str = "Internal server error"):
        super().__init__(message, status_code=500, error_code="INTERNAL_SERVER_ERROR")
