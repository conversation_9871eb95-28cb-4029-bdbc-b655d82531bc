#!/usr/bin/env python3
"""
Test Script for Fixed Webhook and WebSocket Functionality
"""

import requests
import json
import time

def test_webhook_endpoints():
    """Test webhook endpoints"""
    print("🔍 Testing Webhook Endpoints...")
    
    base_url = "http://localhost:5000"
    
    # Test data
    test_data = {
        "carrier": "blue_dart",
        "tracking_number": "TEST123456",
        "status": "in_transit",
        "timestamp": "2025-07-13T10:00:00Z",
        "event_type": "status_update"
    }
    
    endpoints = [
        "/api/webhooks/test",
        "/api/webhooks/blue-dart",
        "/api/webhooks/delhivery",
        "/api/webhooks/fedex"
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.post(
                f"{base_url}{endpoint}",
                json=test_data,
                timeout=5
            )
            print(f"✅ {endpoint}: Status {response.status_code}")
            if response.status_code == 200:
                print(f"   Response: {response.json()}")
        except requests.exceptions.ConnectionError:
            print(f"⚠️  {endpoint}: Server not running")
        except Exception as e:
            print(f"❌ {endpoint}: Error {e}")

def test_socketio_endpoints():
    """Test SocketIO API endpoints"""
    print("\n🔍 Testing SocketIO Endpoints...")
    
    base_url = "http://localhost:5000"
    
    endpoints = [
        "/api/socketio/health",
        "/api/socketio/connections"
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            print(f"✅ {endpoint}: Status {response.status_code}")
            if response.status_code == 200:
                print(f"   Response: {response.json()}")
        except requests.exceptions.ConnectionError:
            print(f"⚠️  {endpoint}: Server not running")
        except Exception as e:
            print(f"❌ {endpoint}: Error {e}")

def main():
    """Main test function"""
    print("🧪 Testing Fixed Webhook and WebSocket Functionality")
    print("=" * 60)
    
    print("📋 Prerequisites:")
    print("1. Start server: python run_with_waitress.py")
    print("2. Ensure Redis is running")
    print("3. Ensure MySQL is running")
    print()
    
    # Wait for user confirmation
    input("Press Enter when server is running...")
    
    # Run tests
    test_webhook_endpoints()
    test_socketio_endpoints()
    
    print("\n✅ Testing completed!")

if __name__ == '__main__':
    main()
