#!/usr/bin/env python3
"""
Flask-SocketIO Manager Integration Test
======================================

Comprehensive test of Flask-SocketIO manager functionality and integration.
"""

import sys
import os
import time
import threading
import traceback
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_socketio_manager_import():
    """Test Flask-SocketIO manager import and initialization"""
    print("🔌 Testing Flask-SocketIO Manager Import")
    print("=" * 45)
    
    try:
        # Test import
        from flask_socketio_manager import (
            FlaskSocketIOManager, 
            socketio_manager, 
            init_socketio,
            get_socketio_instance,
            broadcast_inventory_update,
            broadcast_price_update,
            send_cart_update,
            send_order_status_update,
            send_notification,
            broadcast_to_admins
        )
        print("✅ All imports successful")
        
        # Test manager instance
        if socketio_manager:
            print("✅ Global socketio_manager instance available")
            print(f"   Type: {type(socketio_manager)}")
            print(f"   SocketIO instance: {socketio_manager.socketio}")
            print(f"   Redis client: {socketio_manager.redis_client}")
        else:
            print("❌ Global socketio_manager instance not available")
        
        # Test convenience functions
        functions = [
            broadcast_inventory_update,
            broadcast_price_update,
            send_cart_update,
            send_order_status_update,
            send_notification,
            broadcast_to_admins
        ]
        
        for func in functions:
            if callable(func):
                print(f"✅ Function {func.__name__}: Available")
            else:
                print(f"❌ Function {func.__name__}: Not callable")
        
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        traceback.print_exc()
        return False

def test_app_integration():
    """Test Flask app integration"""
    print("\n🌐 Testing Flask App Integration")
    print("=" * 40)
    
    try:
        from app import app, socketio
        
        print("✅ Flask app imported successfully")
        print(f"   App name: {app.name}")
        print(f"   App config: {app.config.get('ENV', 'unknown')}")
        
        if socketio:
            print("✅ SocketIO instance available in app")
            print(f"   SocketIO type: {type(socketio)}")
            print(f"   SocketIO async mode: {getattr(socketio, 'async_mode', 'unknown')}")
        else:
            print("❌ SocketIO instance not available in app")
        
        # Test app context
        with app.app_context():
            print("✅ App context established")
            
            # Test SocketIO manager access
            from flask_socketio_manager import get_socketio_instance
            socketio_instance = get_socketio_instance()
            
            if socketio_instance:
                print("✅ SocketIO instance accessible via get_socketio_instance()")
            else:
                print("❌ SocketIO instance not accessible")
        
        return True
        
    except Exception as e:
        print(f"❌ App integration test failed: {e}")
        traceback.print_exc()
        return False

def test_redis_connection():
    """Test Redis connection for pub/sub"""
    print("\n🔴 Testing Redis Connection")
    print("=" * 35)
    
    try:
        import redis
        
        # Test Redis connection
        redis_client = redis.Redis(
            host='localhost', 
            port=6379, 
            db=0, 
            decode_responses=True
        )
        
        # Test ping
        response = redis_client.ping()
        if response:
            print("✅ Redis connection: Working")
            print(f"   Host: localhost:6379")
            print(f"   Database: 0")
            
            # Test pub/sub
            redis_client.publish('test_channel', 'test_message')
            print("✅ Redis pub/sub: Working")
        else:
            print("❌ Redis connection: Failed")
        
        return True
        
    except Exception as e:
        print(f"❌ Redis test failed: {e}")
        return False

def test_broadcasting_functions():
    """Test broadcasting functions"""
    print("\n📡 Testing Broadcasting Functions")
    print("=" * 40)
    
    try:
        from app import app
        from flask_socketio_manager import (
            broadcast_inventory_update,
            broadcast_price_update,
            send_cart_update,
            send_notification,
            broadcast_to_admins
        )
        
        with app.app_context():
            print("✅ App context established")
            
            # Test inventory update
            try:
                broadcast_inventory_update(product_id=123, new_quantity=50, old_quantity=75)
                print("✅ broadcast_inventory_update: Function executed")
            except Exception as e:
                print(f"❌ broadcast_inventory_update: {e}")
            
            # Test price update
            try:
                broadcast_price_update(product_id=456, new_price=29.99, old_price=34.99)
                print("✅ broadcast_price_update: Function executed")
            except Exception as e:
                print(f"❌ broadcast_price_update: {e}")
            
            # Test cart update
            try:
                send_cart_update(user_id="user123", cart_data={"items": 3, "total": 89.97})
                print("✅ send_cart_update: Function executed")
            except Exception as e:
                print(f"❌ send_cart_update: {e}")
            
            # Test notification
            try:
                send_notification(user_id="user123", notification_data={"title": "Test", "message": "Hello"})
                print("✅ send_notification: Function executed")
            except Exception as e:
                print(f"❌ send_notification: {e}")
            
            # Test admin broadcast
            try:
                broadcast_to_admins({"type": "test", "message": "Admin test message"})
                print("✅ broadcast_to_admins: Function executed")
            except Exception as e:
                print(f"❌ broadcast_to_admins: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Broadcasting functions test failed: {e}")
        traceback.print_exc()
        return False

def test_event_handlers():
    """Test SocketIO event handlers"""
    print("\n🎯 Testing SocketIO Event Handlers")
    print("=" * 40)
    
    try:
        from app import app, socketio
        
        if not socketio:
            print("❌ SocketIO not available - cannot test event handlers")
            return False
        
        with app.app_context():
            print("✅ App context established")
            
            # Test event handler registration
            handlers = socketio.handlers.get('/', {})
            
            expected_events = ['connect', 'disconnect', 'ping', 'subscribe', 'heartbeat']
            
            for event in expected_events:
                if event in handlers:
                    print(f"✅ Event handler '{event}': Registered")
                else:
                    print(f"❌ Event handler '{event}': Not registered")
            
            print(f"   Total registered handlers: {len(handlers)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Event handlers test failed: {e}")
        traceback.print_exc()
        return False

def test_connection_stats():
    """Test connection statistics"""
    print("\n📊 Testing Connection Statistics")
    print("=" * 40)
    
    try:
        from flask_socketio_manager import socketio_manager
        
        if socketio_manager:
            stats = socketio_manager.get_connection_stats()
            print("✅ Connection stats retrieved")
            print(f"   Total connections: {stats['total_connections']}")
            print(f"   Authenticated users: {stats['authenticated_users']}")
            print(f"   Guest sessions: {stats['guest_sessions']}")
            print(f"   Admin sessions: {stats['admin_sessions']}")
            print(f"   Active users: {stats['active_users']}")
        else:
            print("❌ SocketIO manager not available")
        
        return True
        
    except Exception as e:
        print(f"❌ Connection stats test failed: {e}")
        return False

def test_files_using_socketio():
    """Test files that use Flask-SocketIO manager"""
    print("\n📁 Testing Files Using Flask-SocketIO")
    print("=" * 45)
    
    files_to_check = [
        'app.py',
        'run_with_waitress.py',
        'order_fulfillment/order_fulfillment_engine.py',
        'notification_service.py'
    ]
    
    for file_path in files_to_check:
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                if 'flask_socketio_manager' in content:
                    print(f"✅ {file_path}: Uses Flask-SocketIO manager")
                    
                    # Count usage patterns
                    imports = content.count('from flask_socketio_manager import')
                    broadcasts = content.count('broadcast_')
                    sends = content.count('send_')
                    
                    print(f"   Imports: {imports}, Broadcasts: {broadcasts}, Sends: {sends}")
                else:
                    print(f"⚠️  {file_path}: Does not use Flask-SocketIO manager")
            else:
                print(f"❌ {file_path}: File not found")
                
        except Exception as e:
            print(f"❌ {file_path}: Error reading file - {e}")
    
    return True

def main():
    """Run all Flask-SocketIO integration tests"""
    print("🚀 Flask-SocketIO Manager Integration Test")
    print("=" * 55)
    print()
    
    tests = [
        test_socketio_manager_import,
        test_app_integration,
        test_redis_connection,
        test_broadcasting_functions,
        test_event_handlers,
        test_connection_stats,
        test_files_using_socketio
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed: {e}")
            results.append(False)
    
    # Summary
    print("\n📊 Integration Test Results")
    print("=" * 35)
    passed = sum(results)
    total = len(results)
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! Flask-SocketIO is perfectly integrated!")
    elif passed >= total * 0.8:
        print("\n✅ MOSTLY WORKING! Flask-SocketIO is well integrated with minor issues.")
    else:
        print("\n⚠️  SOME ISSUES FOUND. Check the failed tests above.")
    
    # Integration assessment
    print("\n🎯 FLASK-SOCKETIO INTEGRATION ASSESSMENT")
    print("=" * 50)
    
    if passed >= 6:
        print("✅ STATUS: PERFECTLY INTEGRATED AND WORKING")
        print("✅ Real-time features: FULLY OPERATIONAL")
        print("✅ Broadcasting system: FUNCTIONAL")
        print("✅ Event handling: WORKING")
        print("✅ Redis pub/sub: CONNECTED")
        print("✅ Flask integration: SEAMLESS")
    elif passed >= 4:
        print("⚠️  STATUS: MOSTLY INTEGRATED WITH MINOR ISSUES")
        print("✅ Core functionality: WORKING")
        print("⚠️  Some features may need attention")
    else:
        print("❌ STATUS: INTEGRATION ISSUES DETECTED")
        print("❌ Requires troubleshooting")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
