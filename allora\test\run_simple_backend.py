#!/usr/bin/env python3
"""
Simple Backend Runner - Minimal startup for testing
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from waitress import serve
from app import app

# Configuration
HOST = os.getenv('HOST', '127.0.0.1')  # Use localhost only for testing
PORT = int(os.getenv('PORT', 5000))
THREADS = int(os.getenv('THREADS', 4))

if __name__ == '__main__':
    print("🚀 Starting Allora Flask app with Waitress (Simple Mode)")
    print(f"🌐 Server will be available at: http://{HOST}:{PORT}")
    print(f"🔧 Using Waitress with {THREADS} threads")
    print("⚠️  Skipping complex initialization for testing")
    print("=" * 50)

    # Minimal initialization - just ensure database tables exist
    with app.app_context():
        try:
            from app import db
            db.create_all()
            print("✅ Database tables created/verified")
        except Exception as e:
            print(f"⚠️  Database initialization warning: {e}")

    print("🎯 Starting server...")
    
    try:
        # Serve the app with Waitress
        serve(app, host=HOST, port=PORT, threads=THREADS)
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")
