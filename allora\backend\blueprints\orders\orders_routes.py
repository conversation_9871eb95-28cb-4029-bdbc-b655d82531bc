"""
Orders Routes
=============

Order and cart management endpoints with consistent response format.
"""

from flask import request
from datetime import datetime
import logging

from ..common.versioning import create_versioned_blueprint
from ..common.response_wrapper import (
    success_response, error_response, paginated_response, 
    not_found_response, validation_error_response
)
from ..common.auth_decorators import jwt_required_v2, rate_limit_v2, validate_content_type
from ..common.validators import (
    validate_json, validate_required_fields, validate_pagination,
    validate_quantity, validate_price
)

logger = logging.getLogger(__name__)

# Create versioned blueprint
orders_bp = create_versioned_blueprint('orders', __name__, url_prefix='/orders')

@orders_bp.route('', methods=['GET'])
@jwt_required_v2()
def get_orders(user):
    """
    Get user's orders with pagination.
    
    GET /api/v1/orders
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status')
        
        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=50)
        
        from app import Order, OrderItem, Product
        
        # Build query
        query = Order.query.filter_by(user_id=user.id)
        
        if status:
            query = query.filter_by(status=status)
        
        query = query.order_by(Order.created_at.desc())
        
        # Execute paginated query
        paginated_orders = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        # Format orders data
        orders_data = []
        for order in paginated_orders.items:
            order_items = OrderItem.query.filter_by(order_id=order.id).all()
            
            items_data = []
            for item in order_items:
                product = Product.query.get(item.product_id)
                items_data.append({
                    "id": item.id,
                    "product_id": item.product_id,
                    "product_name": product.name if product else "Unknown Product",
                    "quantity": item.quantity,
                    "price": float(item.price),
                    "total": float(item.price * item.quantity)
                })
            
            order_data = {
                "id": order.id,
                "order_number": getattr(order, 'order_number', f"ORD-{order.id}"),
                "status": order.status,
                "total_amount": float(order.total_amount),
                "items_count": len(items_data),
                "items": items_data,
                "shipping_address": order.shipping_address,
                "created_at": order.created_at.isoformat() if order.created_at else None,
                "updated_at": order.updated_at.isoformat() if order.updated_at else None
            }
            orders_data.append(order_data)
        
        return paginated_response(
            data=orders_data,
            page=page,
            per_page=per_page,
            total=paginated_orders.total,
            message="Orders retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get orders error: {e}")
        return error_response(
            message="Failed to retrieve orders",
            status_code=500,
            error_code="ORDERS_FETCH_FAILED"
        )

@orders_bp.route('', methods=['POST'])
@jwt_required_v2()
@validate_content_type()
def create_order(user):
    """
    Create a new order.
    
    POST /api/v1/orders
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data
        
        # Validate required fields
        required_fields = ['items', 'shipping_address']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp
        
        items = data['items']
        shipping_address = data['shipping_address']
        
        if not items or not isinstance(items, list):
            return validation_error_response(
                errors={"items": ["Items list is required and cannot be empty"]},
                message="Invalid items data"
            )
        
        # Validate items
        from app import db, Product, Order, OrderItem
        total_amount = 0
        validated_items = []
        
        for item in items:
            if not isinstance(item, dict):
                return validation_error_response(
                    errors={"items": ["Each item must be an object"]},
                    message="Invalid item format"
                )
            
            product_id = item.get('product_id')
            quantity = item.get('quantity')
            
            if not product_id or not quantity:
                return validation_error_response(
                    errors={"items": ["Each item must have product_id and quantity"]},
                    message="Missing item data"
                )
            
            # Validate quantity
            is_valid_qty, normalized_qty = validate_quantity(quantity)
            if not is_valid_qty or normalized_qty <= 0:
                return validation_error_response(
                    errors={"items": [f"Invalid quantity for product {product_id}"]},
                    message="Invalid quantity"
                )
            
            # Check product exists and has stock
            product = Product.query.get(product_id)
            if not product or not product.is_active:
                return validation_error_response(
                    errors={"items": [f"Product {product_id} not found or inactive"]},
                    message="Invalid product"
                )
            
            if product.stock_quantity < normalized_qty:
                return validation_error_response(
                    errors={"items": [f"Insufficient stock for product {product.name}"]},
                    message="Insufficient stock"
                )
            
            item_total = float(product.price) * normalized_qty
            total_amount += item_total
            
            validated_items.append({
                "product": product,
                "quantity": normalized_qty,
                "price": float(product.price),
                "total": item_total
            })
        
        # Create order
        new_order = Order(
            user_id=user.id,
            status='pending',
            total_amount=total_amount,
            shipping_address=shipping_address,
            created_at=datetime.utcnow()
        )
        
        db.session.add(new_order)
        db.session.flush()  # Get order ID
        
        # Create order items and update stock
        order_items_data = []
        for item in validated_items:
            order_item = OrderItem(
                order_id=new_order.id,
                product_id=item['product'].id,
                quantity=item['quantity'],
                price=item['price']
            )
            db.session.add(order_item)
            
            # Update product stock
            item['product'].stock_quantity -= item['quantity']
            
            order_items_data.append({
                "product_id": item['product'].id,
                "product_name": item['product'].name,
                "quantity": item['quantity'],
                "price": item['price'],
                "total": item['total']
            })
        
        db.session.commit()
        
        order_data = {
            "id": new_order.id,
            "order_number": f"ORD-{new_order.id}",
            "status": new_order.status,
            "total_amount": float(new_order.total_amount),
            "items": order_items_data,
            "shipping_address": new_order.shipping_address,
            "created_at": new_order.created_at.isoformat()
        }
        
        return success_response(
            data=order_data,
            message="Order created successfully",
            status_code=201
        )
        
    except Exception as e:
        logger.error(f"Create order error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to create order",
            status_code=500,
            error_code="ORDER_CREATION_FAILED"
        )

@orders_bp.route('/<int:order_id>', methods=['GET'])
@jwt_required_v2()
def get_order(user, order_id):
    """
    Get specific order details.
    
    GET /api/v1/orders/{order_id}
    """
    try:
        from app import Order, OrderItem, Product
        
        order = Order.query.filter_by(id=order_id, user_id=user.id).first()
        if not order:
            return not_found_response("Order", order_id)
        
        # Get order items
        order_items = OrderItem.query.filter_by(order_id=order.id).all()
        
        items_data = []
        for item in order_items:
            product = Product.query.get(item.product_id)
            items_data.append({
                "id": item.id,
                "product_id": item.product_id,
                "product_name": product.name if product else "Unknown Product",
                "product_image": product.images[0].image_url if product and product.images else None,
                "quantity": item.quantity,
                "price": float(item.price),
                "total": float(item.price * item.quantity)
            })
        
        order_data = {
            "id": order.id,
            "order_number": f"ORD-{order.id}",
            "status": order.status,
            "total_amount": float(order.total_amount),
            "items": items_data,
            "shipping_address": order.shipping_address,
            "created_at": order.created_at.isoformat() if order.created_at else None,
            "updated_at": order.updated_at.isoformat() if order.updated_at else None
        }
        
        return success_response(
            data=order_data,
            message="Order retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get order error: {e}")
        return error_response(
            message="Failed to retrieve order",
            status_code=500,
            error_code="ORDER_FETCH_FAILED"
        )
