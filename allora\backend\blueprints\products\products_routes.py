"""
Products Routes
===============

Product-related endpoints with consistent response format and URL patterns.
"""

from flask import request
import logging

from ..common.versioning import create_versioned_blueprint
from ..common.response_wrapper import (
    success_response, error_response, paginated_response, not_found_response
)
from ..common.auth_decorators import jwt_required_v2, optional_auth, rate_limit_v2
from ..common.validators import (
    validate_pagination, validate_sort_params, validate_price
)

logger = logging.getLogger(__name__)

# Create versioned blueprint
products_bp = create_versioned_blueprint('products', __name__, url_prefix='/products')

@products_bp.route('', methods=['GET'])
@rate_limit_v2(limit=120, window=60, per='ip')  # 120 requests per minute
def get_products():
    """
    Get products with advanced filtering, search, and pagination.

    GET /api/v1/products
    Query Parameters:
    - page: Page number (default: 1)
    - per_page: Items per page (default: 20, max: 50)
    - category: Filter by category
    - brand: Filter by brand
    - min_price: Minimum price filter
    - max_price: Maximum price filter
    - min_rating: Minimum rating filter
    - search: Search query (uses Elasticsearch if available)
    - q: Alternative search parameter
    - sort_by: Sort field (relevance, name, price, rating, created_at)
    - sort_order: Sort order (asc, desc)
    - in_stock_only: Filter to in-stock products only (true/false)
    - verified_sellers_only: Filter to verified sellers only (true/false)
    - seller_name: Filter by seller name
    """
    try:
        import time
        start_time = time.time()

        # Try to use Elasticsearch for advanced search if available
        try:
            from search_system.elasticsearch_search import get_search_engine

            # Check if this is a search request that would benefit from Elasticsearch
            search_query = request.args.get('search', '').strip()
            if search_query or request.args.get('q', '').strip():
                # Use Elasticsearch for text search
                search_engine = get_search_engine()

                # Map request parameters to Elasticsearch format
                filters = {}

                # Category filters
                if request.args.get('category'):
                    filters['categories'] = [cat.strip() for cat in request.args.get('category').split(',')]

                # Brand filters
                if request.args.get('brand'):
                    filters['brands'] = [brand.strip() for brand in request.args.get('brand').split(',')]

                # Price range filters
                if request.args.get('min_price'):
                    try:
                        filters['price_min'] = float(request.args.get('min_price'))
                    except ValueError:
                        pass

                if request.args.get('max_price'):
                    try:
                        filters['price_max'] = float(request.args.get('max_price'))
                    except ValueError:
                        pass

                # Rating filter
                if request.args.get('min_rating'):
                    try:
                        filters['min_rating'] = float(request.args.get('min_rating'))
                    except ValueError:
                        pass

                # Boolean filters
                if request.args.get('in_stock_only', '').lower() == 'true':
                    filters['in_stock_only'] = True

                if request.args.get('verified_sellers_only', '').lower() == 'true':
                    filters['verified_sellers_only'] = True

                # Seller filters
                if request.args.get('seller_name'):
                    filters['sellers'] = [request.args.get('seller_name').strip()]

                # Sorting parameters
                sort_by = request.args.get('sort_by', 'relevance')
                sort_order = request.args.get('sort_order', 'desc')

                # Pagination parameters
                try:
                    page = max(1, int(request.args.get('page', 1)))
                except ValueError:
                    page = 1

                try:
                    per_page = min(50, max(1, int(request.args.get('per_page', 20))))
                except ValueError:
                    per_page = 20

                # Execute Elasticsearch search
                query_text = search_query or request.args.get('q', '').strip()
                results = search_engine.search_products(
                    query=query_text,
                    filters=filters,
                    sort_by=sort_by,
                    sort_order=sort_order,
                    page=page,
                    per_page=per_page,
                    include_facets=True
                )

                # Format Elasticsearch results
                products_data = []
                for hit in results.get('hits', []):
                    source = hit.get('_source', {})
                    products_data.append({
                        "id": source.get('id'),
                        "name": source.get('name'),
                        "description": source.get('description'),
                        "price": float(source.get('price', 0)),
                        "category": source.get('category'),
                        "brand": source.get('brand'),
                        "stock_quantity": source.get('stock_quantity', 0),
                        "rating": source.get('average_rating'),
                        "image_url": source.get('image_url'),
                        "seller_name": source.get('seller_name'),
                        "created_at": source.get('created_at'),
                        "relevance_score": hit.get('_score')
                    })

                # Calculate execution time
                execution_time = time.time() - start_time

                return paginated_response(
                    data=products_data,
                    page=page,
                    per_page=per_page,
                    total=results.get('total', 0),
                    message="Products retrieved successfully via Elasticsearch",
                    meta={
                        "search_engine": "elasticsearch",
                        "execution_time": round(execution_time, 3),
                        "facets": results.get('facets', {}),
                        "query": query_text,
                        "filters_applied": filters
                    }
                )

        except ImportError:
            logger.info("Elasticsearch not available, falling back to database search")
        except Exception as es_error:
            logger.warning(f"Elasticsearch search failed: {es_error}, falling back to database")

        # Fallback to database search
        from app import db, Product, ProductImage, Seller, ProductReview

        # Get query parameters
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        category = request.args.get('category')
        brand = request.args.get('brand')
        min_price = request.args.get('min_price', type=float)
        max_price = request.args.get('max_price', type=float)
        min_rating = request.args.get('min_rating', type=float)
        search = request.args.get('search', '').strip() or request.args.get('q', '').strip()
        sort_by = request.args.get('sort_by', 'created_at')
        sort_order = request.args.get('sort_order', 'desc')
        in_stock_only = request.args.get('in_stock_only', '').lower() == 'true'
        verified_sellers_only = request.args.get('verified_sellers_only', '').lower() == 'true'
        seller_name = request.args.get('seller_name')

        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=50)

        # Build base query
        query = Product.query.filter_by(is_active=True)

        # Apply filters
        if category:
            query = query.filter(Product.category.ilike(f'%{category}%'))

        if brand:
            query = query.filter(Product.brand.ilike(f'%{brand}%'))

        if min_price is not None:
            query = query.filter(Product.price >= min_price)

        if max_price is not None:
            query = query.filter(Product.price <= max_price)

        if in_stock_only:
            query = query.filter(Product.stock_quantity > 0)

        if search:
            search_filter = f'%{search}%'
            query = query.filter(
                db.or_(
                    Product.name.ilike(search_filter),
                    Product.description.ilike(search_filter),
                    Product.category.ilike(search_filter),
                    Product.brand.ilike(search_filter)
                )
            )

        # Seller filters
        if seller_name or verified_sellers_only:
            query = query.join(Seller, Product.seller_id == Seller.id)

            if seller_name:
                query = query.filter(Seller.business_name.ilike(f'%{seller_name}%'))

            if verified_sellers_only:
                query = query.filter(Seller.is_verified == True)

        # Rating filter (requires subquery)
        if min_rating is not None:
            rating_subquery = db.session.query(
                ProductReview.product_id,
                db.func.avg(ProductReview.rating).label('avg_rating')
            ).group_by(ProductReview.product_id).subquery()

            query = query.join(rating_subquery, Product.id == rating_subquery.c.product_id)
            query = query.filter(rating_subquery.c.avg_rating >= min_rating)

        # Apply sorting
        if sort_by == 'name':
            order_by = Product.name.asc() if sort_order == 'asc' else Product.name.desc()
        elif sort_by == 'price':
            order_by = Product.price.asc() if sort_order == 'asc' else Product.price.desc()
        elif sort_by == 'rating':
            # Sort by average rating (requires join)
            rating_subquery = db.session.query(
                ProductReview.product_id,
                db.func.avg(ProductReview.rating).label('avg_rating')
            ).group_by(ProductReview.product_id).subquery()

            query = query.outerjoin(rating_subquery, Product.id == rating_subquery.c.product_id)
            order_by = rating_subquery.c.avg_rating.desc() if sort_order == 'desc' else rating_subquery.c.avg_rating.asc()
        else:  # created_at or relevance (fallback to created_at)
            order_by = Product.created_at.asc() if sort_order == 'asc' else Product.created_at.desc()

        query = query.order_by(order_by)

        # Execute paginated query
        paginated_products = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # Format products data
        products_data = []
        for product in paginated_products.items:
            # Get first image
            first_image = ProductImage.query.filter_by(product_id=product.id).first()

            # Get seller info
            seller = Seller.query.get(product.seller_id) if hasattr(product, 'seller_id') and product.seller_id else None

            # Get average rating
            avg_rating = db.session.query(
                db.func.avg(ProductReview.rating)
            ).filter_by(product_id=product.id).scalar()

            products_data.append({
                "id": product.id,
                "name": product.name,
                "description": product.description,
                "price": float(product.price),
                "category": product.category,
                "brand": getattr(product, 'brand', None),
                "stock_quantity": product.stock_quantity,
                "rating": round(float(avg_rating), 2) if avg_rating else None,
                "image_url": first_image.image_url if first_image else None,
                "seller": {
                    "id": seller.id if seller else None,
                    "name": seller.business_name if seller else None,
                    "is_verified": seller.is_verified if seller else False
                } if seller else None,
                "created_at": product.created_at.isoformat() if product.created_at else None
            })

        # Calculate execution time
        execution_time = time.time() - start_time

        return paginated_response(
            data=products_data,
            page=page,
            per_page=per_page,
            total=paginated_products.total,
            message="Products retrieved successfully",
            meta={
                "search_engine": "database",
                "execution_time": round(execution_time, 3),
                "filters_applied": {
                    "category": category,
                    "brand": brand,
                    "price_range": [min_price, max_price] if min_price or max_price else None,
                    "min_rating": min_rating,
                    "in_stock_only": in_stock_only,
                    "verified_sellers_only": verified_sellers_only,
                    "seller_name": seller_name,
                    "search": search
                }
            }
        )

    except Exception as e:
        logger.error(f"Get products error: {e}")
        return error_response(
            message="Failed to retrieve products",
            status_code=500,
            error_code="PRODUCTS_FETCH_FAILED"
        )

@products_bp.route('/<int:product_id>', methods=['GET'])
@rate_limit_v2(limit=60, window=60, per='ip')
def get_product(product_id):
    """
    Get single product details.
    
    GET /api/v1/products/{product_id}
    """
    try:
        from app import Product, ProductImage, ProductReview
        
        product = Product.query.filter_by(id=product_id, is_active=True).first()
        if not product:
            return not_found_response("Product", product_id)
        
        # Get product images
        images = ProductImage.query.filter_by(product_id=product_id)\
            .order_by(ProductImage.display_order.asc()).all()
        
        # Get recent reviews
        reviews = ProductReview.query.filter_by(product_id=product_id)\
            .order_by(ProductReview.created_at.desc()).limit(5).all()
        
        # Format product data
        product_data = {
            "id": product.id,
            "name": product.name,
            "description": product.description,
            "price": float(product.price),
            "category": product.category,
            "stock_quantity": product.stock_quantity,
            "sku": product.sku,
            "rating": getattr(product, 'rating', 0),
            "reviews_count": len(reviews),
            "images": [
                {
                    "id": img.id,
                    "url": img.image_url,
                    "alt_text": img.alt_text,
                    "is_primary": img.is_primary
                }
                for img in images
            ],
            "recent_reviews": [
                {
                    "id": review.id,
                    "rating": review.rating,
                    "comment": review.comment,
                    "user_name": f"{review.user.first_name} {review.user.last_name[0]}." if review.user else "Anonymous",
                    "created_at": review.created_at.isoformat() if review.created_at else None
                }
                for review in reviews
            ],
            "created_at": product.created_at.isoformat() if product.created_at else None,
            "updated_at": product.updated_at.isoformat() if product.updated_at else None
        }
        
        return success_response(
            data=product_data,
            message="Product retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get product error: {e}")
        return error_response(
            message="Failed to retrieve product",
            status_code=500,
            error_code="PRODUCT_FETCH_FAILED"
        )

@products_bp.route('/categories', methods=['GET'])
@rate_limit_v2(limit=60, window=60, per='ip')
def get_categories():
    """
    Get all product categories.
    
    GET /api/v1/products/categories
    """
    try:
        from app import db, Product
        
        # Get categories with product counts
        categories_query = db.session.query(
            Product.category,
            db.func.count(Product.id).label('product_count')
        ).filter(
            Product.is_active == True,
            Product.category.isnot(None)
        ).group_by(Product.category).order_by(Product.category)
        
        categories_data = []
        for category, count in categories_query.all():
            categories_data.append({
                "name": category,
                "product_count": count,
                "slug": category.lower().replace(' ', '-').replace('&', 'and')
            })
        
        return success_response(
            data={"categories": categories_data},
            message="Categories retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get categories error: {e}")
        return error_response(
            message="Failed to retrieve categories",
            status_code=500,
            error_code="CATEGORIES_FETCH_FAILED"
        )

@products_bp.route('/best-sellers', methods=['GET'])
@rate_limit_v2(limit=60, window=60, per='ip')
def get_best_sellers():
    """
    Get best selling products.
    
    GET /api/v1/products/best-sellers
    """
    try:
        limit = request.args.get('limit', 8, type=int)
        limit = min(limit, 50)  # Max 50 items
        
        from app import db, Product, Sales
        
        # Get best sellers based on sales data
        best_sellers_query = db.session.query(
            Product,
            db.func.sum(Sales.quantity).label('total_sold')
        ).join(
            Sales, Product.id == Sales.product_id
        ).filter(
            Product.is_active == True
        ).group_by(
            Product.id
        ).order_by(
            db.desc('total_sold')
        ).limit(limit)
        
        products_data = []
        for product, total_sold in best_sellers_query.all():
            product_data = {
                "id": product.id,
                "name": product.name,
                "price": float(product.price),
                "category": product.category,
                "image_url": product.images[0].image_url if product.images else None,
                "total_sold": int(total_sold),
                "rating": getattr(product, 'rating', 0)
            }
            products_data.append(product_data)
        
        return success_response(
            data={"products": products_data},
            message="Best sellers retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get best sellers error: {e}")
        return error_response(
            message="Failed to retrieve best sellers",
            status_code=500,
            error_code="BEST_SELLERS_FETCH_FAILED"
        )

@products_bp.route('/new-arrivals', methods=['GET'])
@rate_limit_v2(limit=60, window=60, per='ip')
def get_new_arrivals():
    """
    Get newest products.

    GET /api/v1/products/new-arrivals
    """
    try:
        limit = min(request.args.get('limit', 20, type=int), 50)

        from app import Product, ProductImage

        # Get newest products
        new_products = Product.query.filter_by(
            is_active=True
        ).order_by(
            Product.created_at.desc()
        ).limit(limit).all()

        products_data = []
        for product in new_products:
            first_image = ProductImage.query.filter_by(product_id=product.id).first()

            products_data.append({
                "id": product.id,
                "name": product.name,
                "description": product.description,
                "price": float(product.price),
                "category": product.category,
                "stock_quantity": product.stock_quantity,
                "image_url": first_image.image_url if first_image else None,
                "created_at": product.created_at.isoformat() if product.created_at else None
            })

        return success_response(
            data=products_data,
            message="New arrivals retrieved successfully",
            meta={"total_products": len(products_data)}
        )

    except Exception as e:
        logger.error(f"Get new arrivals error: {e}")
        return error_response(
            message="Failed to retrieve new arrivals",
            status_code=500,
            error_code="NEW_ARRIVALS_FETCH_FAILED"
        )

@products_bp.route('/batch', methods=['POST'])
@rate_limit_v2(limit=30, window=60, per='ip')
@validate_content_type()
def get_products_batch():
    """
    Get multiple products by IDs in batch.

    POST /api/v1/products/batch
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['product_ids']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        product_ids = data['product_ids']

        if not isinstance(product_ids, list) or len(product_ids) == 0:
            return validation_error_response(
                errors={"product_ids": ["Must be a non-empty list of product IDs"]},
                message="Invalid product IDs"
            )

        if len(product_ids) > 50:
            return validation_error_response(
                errors={"product_ids": ["Maximum 50 products allowed per batch request"]},
                message="Too many products requested"
            )

        from app import Product, ProductImage

        # Get products
        products = Product.query.filter(
            Product.id.in_(product_ids),
            Product.is_active == True
        ).all()

        products_data = []
        for product in products:
            first_image = ProductImage.query.filter_by(product_id=product.id).first()

            products_data.append({
                "id": product.id,
                "name": product.name,
                "description": product.description,
                "price": float(product.price),
                "category": product.category,
                "stock_quantity": product.stock_quantity,
                "image_url": first_image.image_url if first_image else None,
                "created_at": product.created_at.isoformat() if product.created_at else None
            })

        # Track which IDs were not found
        found_ids = [p["id"] for p in products_data]
        not_found_ids = [pid for pid in product_ids if pid not in found_ids]

        return success_response(
            data=products_data,
            message="Products retrieved successfully",
            meta={
                "requested_count": len(product_ids),
                "found_count": len(products_data),
                "not_found_ids": not_found_ids
            }
        )

    except Exception as e:
        logger.error(f"Get products batch error: {e}")
        return error_response(
            message="Failed to retrieve products batch",
            status_code=500,
            error_code="PRODUCTS_BATCH_FETCH_FAILED"
        )

@products_bp.route('/categories', methods=['GET'])
@rate_limit_v2(limit=60, window=60, per='ip')
def get_categories():
    """
    Get all product categories with product counts.

    GET /api/v1/products/categories
    """
    try:
        from app import db, Product

        # Get categories with product counts
        categories = db.session.query(
            Product.category,
            db.func.count(Product.id).label('product_count')
        ).filter(
            Product.is_active == True,
            Product.category.isnot(None)
        ).group_by(
            Product.category
        ).order_by(
            Product.category.asc()
        ).all()

        categories_data = []
        for category, count in categories:
            categories_data.append({
                "name": category,
                "product_count": int(count),
                "slug": category.lower().replace(' ', '-').replace('&', 'and')
            })

        return success_response(
            data=categories_data,
            message="Categories retrieved successfully",
            meta={"total_categories": len(categories_data)}
        )

    except Exception as e:
        logger.error(f"Get categories error: {e}")
        return error_response(
            message="Failed to retrieve categories",
            status_code=500,
            error_code="CATEGORIES_FETCH_FAILED"
        )

@products_bp.route('/categories/<category_name>/products', methods=['GET'])
@rate_limit_v2(limit=60, window=60, per='ip')
def get_products_by_category(category_name):
    """
    Get products by category with pagination and filtering.

    GET /api/v1/products/categories/{category_name}/products
    """
    try:
        # Decode category name
        category_name = category_name.replace('-', ' ').replace('and', '&')

        # Get query parameters
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        min_price = request.args.get('min_price', type=float)
        max_price = request.args.get('max_price', type=float)
        sort_by = request.args.get('sort_by', 'created_at')
        sort_order = request.args.get('sort_order', 'desc')

        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=50)

        from app import db, Product, ProductImage

        # Build query
        query = Product.query.filter(
            Product.category.ilike(f'%{category_name}%'),
            Product.is_active == True
        )

        # Apply price filters
        if min_price is not None:
            query = query.filter(Product.price >= min_price)

        if max_price is not None:
            query = query.filter(Product.price <= max_price)

        # Apply sorting
        if sort_by == 'name':
            order_by = Product.name.asc() if sort_order == 'asc' else Product.name.desc()
        elif sort_by == 'price':
            order_by = Product.price.asc() if sort_order == 'asc' else Product.price.desc()
        else:  # created_at
            order_by = Product.created_at.asc() if sort_order == 'asc' else Product.created_at.desc()

        query = query.order_by(order_by)

        # Execute paginated query
        paginated_products = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # Format products data
        products_data = []
        for product in paginated_products.items:
            first_image = ProductImage.query.filter_by(product_id=product.id).first()

            products_data.append({
                "id": product.id,
                "name": product.name,
                "description": product.description,
                "price": float(product.price),
                "category": product.category,
                "stock_quantity": product.stock_quantity,
                "image_url": first_image.image_url if first_image else None,
                "created_at": product.created_at.isoformat() if product.created_at else None
            })

        return paginated_response(
            data=products_data,
            page=page,
            per_page=per_page,
            total=paginated_products.total,
            message=f"Products in category '{category_name}' retrieved successfully",
            meta={
                "category": category_name,
                "filters_applied": {
                    "price_range": [min_price, max_price] if min_price or max_price else None
                }
            }
        )

    except Exception as e:
        logger.error(f"Get products by category error: {e}")
        return error_response(
            message="Failed to retrieve products by category",
            status_code=500,
            error_code="CATEGORY_PRODUCTS_FETCH_FAILED"
        )
