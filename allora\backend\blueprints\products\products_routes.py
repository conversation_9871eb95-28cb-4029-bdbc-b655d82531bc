"""
Products Routes
===============

Product-related endpoints with consistent response format and URL patterns.
"""

from flask import request
import logging

from ..common.versioning import create_versioned_blueprint
from ..common.response_wrapper import (
    success_response, error_response, paginated_response, not_found_response
)
from ..common.auth_decorators import jwt_required_v2, optional_auth, rate_limit_v2
from ..common.validators import (
    validate_pagination, validate_sort_params, validate_price
)

logger = logging.getLogger(__name__)

# Create versioned blueprint
products_bp = create_versioned_blueprint('products', __name__, url_prefix='/products')

@products_bp.route('', methods=['GET'])
@rate_limit_v2(limit=120, window=60, per='ip')  # 120 requests per minute
def get_products():
    """
    Get products with pagination, filtering, and sorting.
    
    GET /api/v1/products
    Query Parameters:
    - page: Page number (default: 1)
    - per_page: Items per page (default: 20, max: 100)
    - category: Filter by category
    - min_price: Minimum price filter
    - max_price: Maximum price filter
    - search: Search query
    - sort_by: Sort field (name, price, created_at, rating)
    - sort_order: Sort order (asc, desc)
    """
    try:
        # Get query parameters
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        category = request.args.get('category')
        min_price = request.args.get('min_price', type=float)
        max_price = request.args.get('max_price', type=float)
        search = request.args.get('search', '').strip()
        sort_by = request.args.get('sort_by', 'created_at')
        sort_order = request.args.get('sort_order', 'desc')
        
        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=100)
        
        # Validate sorting
        allowed_sort_fields = ['name', 'price', 'created_at', 'rating']
        sort_by, sort_order = validate_sort_params(sort_by, sort_order, allowed_sort_fields)
        
        # Build query
        from app import db, Product
        query = Product.query.filter_by(is_active=True)
        
        # Apply filters
        if category:
            query = query.filter(Product.category.ilike(f'%{category}%'))
        
        if min_price is not None:
            query = query.filter(Product.price >= min_price)
        
        if max_price is not None:
            query = query.filter(Product.price <= max_price)
        
        if search:
            search_filter = f'%{search}%'
            query = query.filter(
                db.or_(
                    Product.name.ilike(search_filter),
                    Product.description.ilike(search_filter),
                    Product.category.ilike(search_filter)
                )
            )
        
        # Apply sorting
        if sort_by == 'name':
            order_by = Product.name.asc() if sort_order == 'asc' else Product.name.desc()
        elif sort_by == 'price':
            order_by = Product.price.asc() if sort_order == 'asc' else Product.price.desc()
        elif sort_by == 'rating':
            # Assuming there's a rating field or calculated rating
            order_by = Product.rating.asc() if sort_order == 'asc' else Product.rating.desc()
        else:  # created_at
            order_by = Product.created_at.asc() if sort_order == 'asc' else Product.created_at.desc()
        
        query = query.order_by(order_by)
        
        # Execute paginated query
        paginated_products = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        # Format products data
        products_data = []
        for product in paginated_products.items:
            product_data = {
                "id": product.id,
                "name": product.name,
                "description": product.description,
                "price": float(product.price),
                "category": product.category,
                "stock_quantity": product.stock_quantity,
                "rating": getattr(product, 'rating', 0),
                "image_url": product.images[0].image_url if product.images else None,
                "created_at": product.created_at.isoformat() if product.created_at else None
            }
            products_data.append(product_data)
        
        return paginated_response(
            data=products_data,
            page=page,
            per_page=per_page,
            total=paginated_products.total,
            message="Products retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get products error: {e}")
        return error_response(
            message="Failed to retrieve products",
            status_code=500,
            error_code="PRODUCTS_FETCH_FAILED"
        )

@products_bp.route('/<int:product_id>', methods=['GET'])
@rate_limit_v2(limit=60, window=60, per='ip')
def get_product(product_id):
    """
    Get single product details.
    
    GET /api/v1/products/{product_id}
    """
    try:
        from app import Product, ProductImage, ProductReview
        
        product = Product.query.filter_by(id=product_id, is_active=True).first()
        if not product:
            return not_found_response("Product", product_id)
        
        # Get product images
        images = ProductImage.query.filter_by(product_id=product_id)\
            .order_by(ProductImage.display_order.asc()).all()
        
        # Get recent reviews
        reviews = ProductReview.query.filter_by(product_id=product_id)\
            .order_by(ProductReview.created_at.desc()).limit(5).all()
        
        # Format product data
        product_data = {
            "id": product.id,
            "name": product.name,
            "description": product.description,
            "price": float(product.price),
            "category": product.category,
            "stock_quantity": product.stock_quantity,
            "sku": product.sku,
            "rating": getattr(product, 'rating', 0),
            "reviews_count": len(reviews),
            "images": [
                {
                    "id": img.id,
                    "url": img.image_url,
                    "alt_text": img.alt_text,
                    "is_primary": img.is_primary
                }
                for img in images
            ],
            "recent_reviews": [
                {
                    "id": review.id,
                    "rating": review.rating,
                    "comment": review.comment,
                    "user_name": f"{review.user.first_name} {review.user.last_name[0]}." if review.user else "Anonymous",
                    "created_at": review.created_at.isoformat() if review.created_at else None
                }
                for review in reviews
            ],
            "created_at": product.created_at.isoformat() if product.created_at else None,
            "updated_at": product.updated_at.isoformat() if product.updated_at else None
        }
        
        return success_response(
            data=product_data,
            message="Product retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get product error: {e}")
        return error_response(
            message="Failed to retrieve product",
            status_code=500,
            error_code="PRODUCT_FETCH_FAILED"
        )

@products_bp.route('/categories', methods=['GET'])
@rate_limit_v2(limit=60, window=60, per='ip')
def get_categories():
    """
    Get all product categories.
    
    GET /api/v1/products/categories
    """
    try:
        from app import db, Product
        
        # Get categories with product counts
        categories_query = db.session.query(
            Product.category,
            db.func.count(Product.id).label('product_count')
        ).filter(
            Product.is_active == True,
            Product.category.isnot(None)
        ).group_by(Product.category).order_by(Product.category)
        
        categories_data = []
        for category, count in categories_query.all():
            categories_data.append({
                "name": category,
                "product_count": count,
                "slug": category.lower().replace(' ', '-').replace('&', 'and')
            })
        
        return success_response(
            data={"categories": categories_data},
            message="Categories retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get categories error: {e}")
        return error_response(
            message="Failed to retrieve categories",
            status_code=500,
            error_code="CATEGORIES_FETCH_FAILED"
        )

@products_bp.route('/best-sellers', methods=['GET'])
@rate_limit_v2(limit=60, window=60, per='ip')
def get_best_sellers():
    """
    Get best selling products.
    
    GET /api/v1/products/best-sellers
    """
    try:
        limit = request.args.get('limit', 8, type=int)
        limit = min(limit, 50)  # Max 50 items
        
        from app import db, Product, Sales
        
        # Get best sellers based on sales data
        best_sellers_query = db.session.query(
            Product,
            db.func.sum(Sales.quantity).label('total_sold')
        ).join(
            Sales, Product.id == Sales.product_id
        ).filter(
            Product.is_active == True
        ).group_by(
            Product.id
        ).order_by(
            db.desc('total_sold')
        ).limit(limit)
        
        products_data = []
        for product, total_sold in best_sellers_query.all():
            product_data = {
                "id": product.id,
                "name": product.name,
                "price": float(product.price),
                "category": product.category,
                "image_url": product.images[0].image_url if product.images else None,
                "total_sold": int(total_sold),
                "rating": getattr(product, 'rating', 0)
            }
            products_data.append(product_data)
        
        return success_response(
            data={"products": products_data},
            message="Best sellers retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get best sellers error: {e}")
        return error_response(
            message="Failed to retrieve best sellers",
            status_code=500,
            error_code="BEST_SELLERS_FETCH_FAILED"
        )
