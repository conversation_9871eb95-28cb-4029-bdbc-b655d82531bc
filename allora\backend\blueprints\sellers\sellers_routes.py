"""
Sellers Routes
==============

Seller management endpoints with consistent response format.
"""

from flask import request
from datetime import datetime, timedelta
import logging

from ..common.versioning import create_versioned_blueprint
from ..common.response_wrapper import (
    success_response, error_response, paginated_response, 
    not_found_response, validation_error_response
)
from ..common.auth_decorators import seller_required_v2, rate_limit_v2, validate_content_type
from ..common.validators import (
    validate_json, validate_required_fields, validate_pagination,
    validate_price
)

logger = logging.getLogger(__name__)

# Create versioned blueprint
sellers_bp = create_versioned_blueprint('sellers', __name__, url_prefix='/sellers')

# Seller Registration and Setup Endpoints

@sellers_bp.route('/register', methods=['POST'])
@validate_content_type()
@rate_limit_v2(limit=5, window=3600, per='ip')  # 5 registrations per hour per IP
def register_seller():
    """
    Register a new seller.

    POST /api/v1/sellers/register
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = [
            'business_name', 'business_email', 'business_phone',
            'business_address', 'business_type', 'contact_person_name'
        ]
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        from app import db, Seller, User
        from ..common.validators import validate_email, validate_phone

        business_name = data['business_name'].strip()
        business_email = data['business_email'].strip().lower()
        business_phone = data['business_phone'].strip()
        business_address = data['business_address']
        business_type = data['business_type']
        contact_person_name = data['contact_person_name'].strip()

        # Additional optional fields
        business_description = data.get('business_description', '').strip()
        website_url = data.get('website_url', '').strip()
        gst_number = data.get('gst_number', '').strip()

        # Validate email
        if not validate_email(business_email):
            return validation_error_response(
                errors={"business_email": ["Invalid email format"]},
                message="Invalid business email"
            )

        # Validate phone
        if not validate_phone(business_phone):
            return validation_error_response(
                errors={"business_phone": ["Invalid phone number format"]},
                message="Invalid business phone"
            )

        # Validate business type
        valid_business_types = ['individual', 'partnership', 'private_limited', 'public_limited', 'llp']
        if business_type not in valid_business_types:
            return validation_error_response(
                errors={"business_type": [f"Must be one of: {', '.join(valid_business_types)}"]},
                message="Invalid business type"
            )

        # Check if seller already exists
        existing_seller = Seller.query.filter(
            db.or_(
                Seller.business_email == business_email,
                Seller.business_name == business_name
            )
        ).first()

        if existing_seller:
            return error_response(
                message="Seller with this business name or email already exists",
                status_code=409,
                error_code="SELLER_EXISTS"
            )

        # Create new seller
        new_seller = Seller(
            business_name=business_name,
            business_email=business_email,
            business_phone=business_phone,
            business_address=business_address,
            business_type=business_type,
            contact_person_name=contact_person_name,
            business_description=business_description,
            website_url=website_url or None,
            gst_number=gst_number or None,
            status='pending',  # Requires admin approval
            is_verified=False,
            created_at=datetime.utcnow()
        )

        db.session.add(new_seller)
        db.session.commit()

        seller_data = {
            "id": new_seller.id,
            "business_name": new_seller.business_name,
            "business_email": new_seller.business_email,
            "status": new_seller.status,
            "created_at": new_seller.created_at.isoformat()
        }

        return success_response(
            data=seller_data,
            message="Seller registration submitted successfully. Awaiting admin approval.",
            status_code=201
        )

    except Exception as e:
        logger.error(f"Seller registration error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to register seller",
            status_code=500,
            error_code="SELLER_REGISTRATION_FAILED"
        )

@sellers_bp.route('/setup-password', methods=['POST'])
@validate_content_type()
@rate_limit_v2(limit=10, window=3600, per='ip')
def setup_seller_password():
    """
    Setup password for approved seller.

    POST /api/v1/sellers/setup-password
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['token', 'password']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        token = data['token']
        password = data['password']

        from app import db, Seller, User
        from werkzeug.security import generate_password_hash
        import jwt
        from flask import current_app

        # Validate password strength
        if len(password) < 8:
            return validation_error_response(
                errors={"password": ["Password must be at least 8 characters long"]},
                message="Password too short"
            )

        try:
            # Decode token
            payload = jwt.decode(token, current_app.config['SECRET_KEY'], algorithms=['HS256'])
            seller_id = payload.get('seller_id')

            if not seller_id:
                return error_response(
                    message="Invalid token",
                    status_code=400,
                    error_code="INVALID_TOKEN"
                )

        except jwt.ExpiredSignatureError:
            return error_response(
                message="Token has expired",
                status_code=400,
                error_code="TOKEN_EXPIRED"
            )
        except jwt.InvalidTokenError:
            return error_response(
                message="Invalid token",
                status_code=400,
                error_code="INVALID_TOKEN"
            )

        # Get seller
        seller = Seller.query.get(seller_id)
        if not seller or seller.status != 'approved':
            return error_response(
                message="Seller not found or not approved",
                status_code=404,
                error_code="SELLER_NOT_FOUND"
            )

        # Check if user account already exists
        if seller.user_id:
            return error_response(
                message="Password already set for this seller",
                status_code=409,
                error_code="PASSWORD_ALREADY_SET"
            )

        # Create user account
        new_user = User(
            email=seller.business_email,
            first_name=seller.contact_person_name.split()[0],
            last_name=' '.join(seller.contact_person_name.split()[1:]) if len(seller.contact_person_name.split()) > 1 else '',
            password_hash=generate_password_hash(password),
            is_active=True,
            email_verified=True,  # Auto-verify for approved sellers
            user_type='seller',
            created_at=datetime.utcnow()
        )

        db.session.add(new_user)
        db.session.flush()  # Get user ID

        # Link seller to user
        seller.user_id = new_user.id
        seller.is_verified = True
        seller.updated_at = datetime.utcnow()

        db.session.commit()

        return success_response(
            data={
                "seller_id": seller.id,
                "user_id": new_user.id,
                "business_name": seller.business_name,
                "email": new_user.email
            },
            message="Password setup completed successfully. You can now login."
        )

    except Exception as e:
        logger.error(f"Seller password setup error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to setup password",
            status_code=500,
            error_code="PASSWORD_SETUP_FAILED"
        )

# Seller Profile and Store Management Endpoints

@sellers_bp.route('/profile', methods=['GET'])
@seller_required_v2()
@rate_limit_v2(limit=60, window=60, per='user')
def get_seller_profile(user, seller):
    """
    Get seller profile information.

    GET /api/v1/sellers/profile
    """
    try:
        from app import db, Product, Order, SellerStore

        # Get seller store info
        store = SellerStore.query.filter_by(seller_id=seller.id).first()

        # Get basic statistics
        total_products = Product.query.filter_by(seller_id=seller.id, is_active=True).count()
        total_orders = Order.query.join(
            Product, Order.id == Product.id  # This would need proper join logic
        ).filter(Product.seller_id == seller.id).count()

        profile_data = {
            "id": seller.id,
            "business_name": seller.business_name,
            "business_email": seller.business_email,
            "business_phone": seller.business_phone,
            "business_address": seller.business_address,
            "business_type": seller.business_type,
            "contact_person_name": seller.contact_person_name,
            "business_description": seller.business_description,
            "website_url": seller.website_url,
            "gst_number": seller.gst_number,
            "status": seller.status,
            "is_verified": seller.is_verified,
            "commission_rate": getattr(seller, 'commission_rate', 10.0),
            "store": {
                "store_name": store.store_name if store else seller.business_name,
                "store_slug": store.store_slug if store else None,
                "store_description": store.store_description if store else None,
                "store_logo": store.store_logo if store else None,
                "store_banner": store.store_banner if store else None,
                "is_active": store.is_active if store else False
            } if store else None,
            "statistics": {
                "total_products": total_products,
                "total_orders": total_orders,
                "rating": getattr(seller, 'rating', 0.0),
                "total_reviews": getattr(seller, 'total_reviews', 0)
            },
            "created_at": seller.created_at.isoformat() if seller.created_at else None,
            "updated_at": seller.updated_at.isoformat() if seller.updated_at else None
        }

        return success_response(
            data=profile_data,
            message="Seller profile retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Get seller profile error: {e}")
        return error_response(
            message="Failed to retrieve seller profile",
            status_code=500,
            error_code="SELLER_PROFILE_FETCH_FAILED"
        )

@sellers_bp.route('/profile', methods=['PUT'])
@seller_required_v2()
@validate_content_type()
@rate_limit_v2(limit=10, window=300, per='user')  # 10 updates per 5 minutes
def update_seller_profile(user, seller):
    """
    Update seller profile information.

    PUT /api/v1/sellers/profile
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        from app import db
        from ..common.validators import validate_email, validate_phone

        # Fields that can be updated
        updatable_fields = {
            'business_phone': str,
            'business_address': dict,
            'contact_person_name': str,
            'business_description': str,
            'website_url': str,
            'gst_number': str
        }

        updated_fields = []

        for field, field_type in updatable_fields.items():
            if field in data:
                value = data[field]

                # Validate field type
                if value is not None:
                    if field_type == str and not isinstance(value, str):
                        value = str(value).strip()
                    elif field_type == dict and not isinstance(value, dict):
                        return validation_error_response(
                            errors={field: ["Must be a valid object"]},
                            message=f"Invalid {field} format"
                        )

                # Special validation for specific fields
                if field == 'business_phone' and value:
                    if not validate_phone(value):
                        return validation_error_response(
                            errors={"business_phone": ["Invalid phone number format"]},
                            message="Invalid business phone"
                        )

                if field == 'website_url' and value:
                    if not value.startswith(('http://', 'https://')):
                        return validation_error_response(
                            errors={"website_url": ["Must be a valid HTTP/HTTPS URL"]},
                            message="Invalid website URL"
                        )

                # Update seller field
                setattr(seller, field, value)
                updated_fields.append(field)

        if updated_fields:
            seller.updated_at = datetime.utcnow()
            db.session.commit()

            return success_response(
                data={
                    "updated_fields": updated_fields,
                    "seller_id": seller.id,
                    "updated_at": seller.updated_at.isoformat()
                },
                message="Seller profile updated successfully"
            )
        else:
            return success_response(
                data={"updated_fields": []},
                message="No changes made to profile"
            )

    except Exception as e:
        logger.error(f"Update seller profile error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to update seller profile",
            status_code=500,
            error_code="SELLER_PROFILE_UPDATE_FAILED"
        )

@sellers_bp.route('/store', methods=['GET'])
@seller_required_v2()
@rate_limit_v2(limit=60, window=60, per='user')
def get_seller_store(user, seller):
    """
    Get seller store information.

    GET /api/v1/sellers/store
    """
    try:
        from app import SellerStore, Product

        # Get or create store
        store = SellerStore.query.filter_by(seller_id=seller.id).first()

        if not store:
            # Create default store
            store = SellerStore(
                seller_id=seller.id,
                store_name=seller.business_name,
                store_slug=seller.business_name.lower().replace(' ', '-').replace('_', '-'),
                store_description=seller.business_description,
                is_active=True,
                created_at=datetime.utcnow()
            )
            db.session.add(store)
            db.session.commit()

        # Get store statistics
        total_products = Product.query.filter_by(seller_id=seller.id, is_active=True).count()

        store_data = {
            "id": store.id,
            "store_name": store.store_name,
            "store_slug": store.store_slug,
            "store_description": store.store_description,
            "store_logo": store.store_logo,
            "store_banner": store.store_banner,
            "contact_email": store.contact_email or seller.business_email,
            "contact_phone": store.contact_phone or seller.business_phone,
            "business_hours": store.business_hours or {},
            "social_links": store.social_links or {},
            "is_active": store.is_active,
            "statistics": {
                "total_products": total_products,
                "store_views": getattr(store, 'total_views', 0),
                "followers": getattr(store, 'followers_count', 0)
            },
            "created_at": store.created_at.isoformat() if store.created_at else None,
            "updated_at": store.updated_at.isoformat() if store.updated_at else None
        }

        return success_response(
            data=store_data,
            message="Store information retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Get seller store error: {e}")
        return error_response(
            message="Failed to retrieve store information",
            status_code=500,
            error_code="SELLER_STORE_FETCH_FAILED"
        )

@sellers_bp.route('/store', methods=['PUT'])
@seller_required_v2()
@validate_content_type()
@rate_limit_v2(limit=10, window=300, per='user')
def update_seller_store(user, seller):
    """
    Update seller store information.

    PUT /api/v1/sellers/store
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        from app import db, SellerStore

        # Get or create store
        store = SellerStore.query.filter_by(seller_id=seller.id).first()
        if not store:
            store = SellerStore(
                seller_id=seller.id,
                store_name=seller.business_name,
                store_slug=seller.business_name.lower().replace(' ', '-'),
                created_at=datetime.utcnow()
            )
            db.session.add(store)
            db.session.flush()

        # Fields that can be updated
        updatable_fields = {
            'store_name': str,
            'store_description': str,
            'store_logo': str,
            'store_banner': str,
            'contact_email': str,
            'contact_phone': str,
            'business_hours': dict,
            'social_links': dict
        }

        updated_fields = []

        for field, field_type in updatable_fields.items():
            if field in data:
                value = data[field]

                # Validate field type
                if value is not None:
                    if field_type == str and not isinstance(value, str):
                        value = str(value).strip()
                    elif field_type == dict and not isinstance(value, dict):
                        return validation_error_response(
                            errors={field: ["Must be a valid object"]},
                            message=f"Invalid {field} format"
                        )

                # Special validation
                if field == 'store_name' and value:
                    # Update store slug when name changes
                    new_slug = value.lower().replace(' ', '-').replace('_', '-')
                    # Check if slug is unique
                    existing_store = SellerStore.query.filter(
                        SellerStore.store_slug == new_slug,
                        SellerStore.id != store.id
                    ).first()

                    if existing_store:
                        new_slug = f"{new_slug}-{seller.id}"

                    store.store_slug = new_slug

                if field in ['contact_email', 'contact_phone'] and value:
                    from ..common.validators import validate_email, validate_phone
                    if field == 'contact_email' and not validate_email(value):
                        return validation_error_response(
                            errors={"contact_email": ["Invalid email format"]},
                            message="Invalid contact email"
                        )
                    elif field == 'contact_phone' and not validate_phone(value):
                        return validation_error_response(
                            errors={"contact_phone": ["Invalid phone format"]},
                            message="Invalid contact phone"
                        )

                # Update store field
                setattr(store, field, value)
                updated_fields.append(field)

        if updated_fields:
            store.updated_at = datetime.utcnow()
            db.session.commit()

            return success_response(
                data={
                    "updated_fields": updated_fields,
                    "store_id": store.id,
                    "store_slug": store.store_slug,
                    "updated_at": store.updated_at.isoformat()
                },
                message="Store information updated successfully"
            )
        else:
            return success_response(
                data={"updated_fields": []},
                message="No changes made to store"
            )

    except Exception as e:
        logger.error(f"Update seller store error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to update store information",
            status_code=500,
            error_code="SELLER_STORE_UPDATE_FAILED"
        )

# Seller Product Management Endpoints

@sellers_bp.route('/products', methods=['GET'])
@seller_required_v2()
@rate_limit_v2(limit=60, window=60, per='user')
def get_seller_products(user, seller):
    """
    Get seller's products with pagination and filtering.

    GET /api/v1/sellers/products
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status')  # active, inactive, pending
        category = request.args.get('category')
        search = request.args.get('search', '').strip()

        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=50)

        from app import db, Product, ProductImage

        # Build query
        query = Product.query.filter_by(seller_id=seller.id)

        # Apply filters
        if status:
            if status == 'active':
                query = query.filter_by(is_active=True)
            elif status == 'inactive':
                query = query.filter_by(is_active=False)
            elif status == 'pending':
                query = query.filter_by(status='pending')

        if category:
            query = query.filter(Product.category.ilike(f'%{category}%'))

        if search:
            query = query.filter(
                db.or_(
                    Product.name.ilike(f'%{search}%'),
                    Product.description.ilike(f'%{search}%'),
                    Product.sku.ilike(f'%{search}%')
                )
            )

        query = query.order_by(Product.created_at.desc())

        # Execute paginated query
        paginated_products = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # Format products data
        products_data = []
        for product in paginated_products.items:
            # Get first product image
            first_image = ProductImage.query.filter_by(product_id=product.id).first()

            products_data.append({
                "id": product.id,
                "name": product.name,
                "sku": product.sku,
                "price": float(product.price),
                "stock_quantity": product.stock_quantity,
                "category": product.category,
                "brand": getattr(product, 'brand', None),
                "is_active": product.is_active,
                "status": getattr(product, 'status', 'active'),
                "image_url": first_image.image_url if first_image else None,
                "total_sales": getattr(product, 'total_sales', 0),
                "average_rating": product.average_rating or 0.0,
                "total_reviews": product.total_reviews or 0,
                "created_at": product.created_at.isoformat() if product.created_at else None,
                "updated_at": product.updated_at.isoformat() if product.updated_at else None
            })

        return paginated_response(
            data=products_data,
            page=page,
            per_page=per_page,
            total=paginated_products.total,
            message="Seller products retrieved successfully",
            meta={
                "filters_applied": {
                    "status": status,
                    "category": category,
                    "search": search
                }
            }
        )

    except Exception as e:
        logger.error(f"Get seller products error: {e}")
        return error_response(
            message="Failed to retrieve seller products",
            status_code=500,
            error_code="SELLER_PRODUCTS_FETCH_FAILED"
        )

@sellers_bp.route('/products', methods=['POST'])
@seller_required_v2()
@validate_content_type()
@rate_limit_v2(limit=20, window=3600, per='user')  # 20 products per hour
def create_seller_product(user, seller):
    """
    Create a new product for seller.

    POST /api/v1/sellers/products
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['name', 'description', 'price', 'category', 'stock_quantity']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        from app import db, Product

        name = data['name'].strip()
        description = data['description'].strip()
        price = data['price']
        category = data['category'].strip()
        stock_quantity = data['stock_quantity']

        # Optional fields
        sku = data.get('sku', '').strip()
        brand = data.get('brand', '').strip()
        material = data.get('material', '').strip()
        image_url = data.get('image_url', '').strip()
        sustainability_score = data.get('sustainability_score', 0)

        # Validate price
        is_valid_price, normalized_price = validate_price(price)
        if not is_valid_price:
            return validation_error_response(
                errors={"price": ["Invalid price format"]},
                message="Invalid price"
            )

        # Validate stock quantity
        if not isinstance(stock_quantity, int) or stock_quantity < 0:
            return validation_error_response(
                errors={"stock_quantity": ["Stock quantity must be a non-negative integer"]},
                message="Invalid stock quantity"
            )

        # Validate sustainability score
        if sustainability_score and (not isinstance(sustainability_score, int) or sustainability_score < 0 or sustainability_score > 100):
            return validation_error_response(
                errors={"sustainability_score": ["Sustainability score must be between 0 and 100"]},
                message="Invalid sustainability score"
            )

        # Check if SKU already exists (if provided)
        if sku:
            existing_product = Product.query.filter_by(sku=sku).first()
            if existing_product:
                return error_response(
                    message="Product with this SKU already exists",
                    status_code=409,
                    error_code="SKU_EXISTS"
                )

        # Create new product
        new_product = Product(
            seller_id=seller.id,
            name=name,
            description=description,
            price=normalized_price,
            category=category,
            stock_quantity=stock_quantity,
            sku=sku or None,
            brand=brand or None,
            material=material or None,
            image=image_url or None,
            sustainability_score=sustainability_score,
            is_active=True,
            status='pending',  # Requires admin approval
            average_rating=0.0,
            total_reviews=0,
            created_at=datetime.utcnow()
        )

        db.session.add(new_product)
        db.session.commit()

        product_data = {
            "id": new_product.id,
            "name": new_product.name,
            "sku": new_product.sku,
            "price": float(new_product.price),
            "category": new_product.category,
            "stock_quantity": new_product.stock_quantity,
            "status": new_product.status,
            "created_at": new_product.created_at.isoformat()
        }

        return success_response(
            data=product_data,
            message="Product created successfully. Awaiting admin approval.",
            status_code=201
        )

    except Exception as e:
        logger.error(f"Create seller product error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to create product",
            status_code=500,
            error_code="SELLER_PRODUCT_CREATE_FAILED"
        )

@sellers_bp.route('/products/<int:product_id>', methods=['GET'])
@seller_required_v2()
@rate_limit_v2(limit=60, window=60, per='user')
def get_seller_product_detail(user, seller, product_id):
    """
    Get detailed information about seller's product.

    GET /api/v1/sellers/products/{product_id}
    """
    try:
        from app import Product, ProductImage, ProductReview

        # Get product
        product = Product.query.filter_by(
            id=product_id,
            seller_id=seller.id
        ).first()

        if not product:
            return not_found_response("Product", product_id)

        # Get product images
        images = ProductImage.query.filter_by(product_id=product.id).all()
        images_data = [
            {
                "id": img.id,
                "url": img.image_url,
                "alt_text": img.alt_text,
                "display_order": img.display_order or 0
            }
            for img in images
        ]

        # Get recent reviews
        recent_reviews = ProductReview.query.filter_by(
            product_id=product.id
        ).order_by(ProductReview.created_at.desc()).limit(5).all()

        reviews_data = []
        for review in recent_reviews:
            reviews_data.append({
                "id": review.id,
                "rating": review.rating,
                "comment": review.comment,
                "reviewer_name": "Anonymous",  # Privacy protection
                "created_at": review.created_at.isoformat() if review.created_at else None
            })

        product_data = {
            "id": product.id,
            "name": product.name,
            "description": product.description,
            "sku": product.sku,
            "price": float(product.price),
            "category": product.category,
            "brand": getattr(product, 'brand', None),
            "material": getattr(product, 'material', None),
            "stock_quantity": product.stock_quantity,
            "sustainability_score": getattr(product, 'sustainability_score', 0),
            "is_active": product.is_active,
            "status": getattr(product, 'status', 'active'),
            "images": images_data,
            "statistics": {
                "total_sales": getattr(product, 'total_sales', 0),
                "total_views": getattr(product, 'total_views', 0),
                "average_rating": product.average_rating or 0.0,
                "total_reviews": product.total_reviews or 0,
                "wishlist_count": getattr(product, 'wishlist_count', 0)
            },
            "recent_reviews": reviews_data,
            "created_at": product.created_at.isoformat() if product.created_at else None,
            "updated_at": product.updated_at.isoformat() if product.updated_at else None
        }

        return success_response(
            data=product_data,
            message="Product details retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Get seller product detail error: {e}")
        return error_response(
            message="Failed to retrieve product details",
            status_code=500,
            error_code="SELLER_PRODUCT_DETAIL_FETCH_FAILED"
        )

@sellers_bp.route('/products/<int:product_id>', methods=['PUT'])
@seller_required_v2()
@validate_content_type()
@rate_limit_v2(limit=30, window=3600, per='user')
def update_seller_product(user, seller, product_id):
    """
    Update seller's product.

    PUT /api/v1/sellers/products/{product_id}
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        from app import db, Product

        # Get product
        product = Product.query.filter_by(
            id=product_id,
            seller_id=seller.id
        ).first()

        if not product:
            return not_found_response("Product", product_id)

        # Fields that can be updated
        updatable_fields = {
            'name': str,
            'description': str,
            'price': float,
            'category': str,
            'brand': str,
            'material': str,
            'stock_quantity': int,
            'sustainability_score': int,
            'is_active': bool
        }

        updated_fields = []

        for field, field_type in updatable_fields.items():
            if field in data:
                value = data[field]

                # Validate field type and value
                if field == 'price' and value is not None:
                    is_valid_price, normalized_price = validate_price(value)
                    if not is_valid_price:
                        return validation_error_response(
                            errors={"price": ["Invalid price format"]},
                            message="Invalid price"
                        )
                    value = normalized_price

                elif field == 'stock_quantity' and value is not None:
                    if not isinstance(value, int) or value < 0:
                        return validation_error_response(
                            errors={"stock_quantity": ["Must be a non-negative integer"]},
                            message="Invalid stock quantity"
                        )

                elif field == 'sustainability_score' and value is not None:
                    if not isinstance(value, int) or value < 0 or value > 100:
                        return validation_error_response(
                            errors={"sustainability_score": ["Must be between 0 and 100"]},
                            message="Invalid sustainability score"
                        )

                elif field_type == str and value is not None:
                    value = str(value).strip()

                # Update product field
                setattr(product, field, value)
                updated_fields.append(field)

        if updated_fields:
            product.updated_at = datetime.utcnow()
            db.session.commit()

            return success_response(
                data={
                    "updated_fields": updated_fields,
                    "product_id": product.id,
                    "updated_at": product.updated_at.isoformat()
                },
                message="Product updated successfully"
            )
        else:
            return success_response(
                data={"updated_fields": []},
                message="No changes made to product"
            )

    except Exception as e:
        logger.error(f"Update seller product error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to update product",
            status_code=500,
            error_code="SELLER_PRODUCT_UPDATE_FAILED"
        )

@sellers_bp.route('/dashboard', methods=['GET'])
@seller_required_v2()
def get_seller_dashboard(user, seller):
    """
    Get seller dashboard overview with key metrics.
    
    GET /api/v1/sellers/dashboard
    """
    try:
        from app import db, Product, Order, OrderItem, Sales
        
        # Calculate date ranges
        today = datetime.utcnow().date()
        last_30_days = today - timedelta(days=30)
        
        # Product metrics
        total_products = Product.query.filter_by(
            seller_id=seller.id,
            is_active=True
        ).count()
        
        pending_products = Product.query.filter_by(
            seller_id=seller.id,
            status='pending'
        ).count()
        
        # Sales metrics
        seller_sales = db.session.query(
            db.func.sum(Sales.total_amount).label('total_revenue'),
            db.func.sum(Sales.quantity).label('total_sold')
        ).join(
            Product, Sales.product_id == Product.id
        ).filter(
            Product.seller_id == seller.id
        ).first()
        
        total_revenue = float(seller_sales.total_revenue or 0)
        total_sold = int(seller_sales.total_sold or 0)
        
        # Recent sales (last 30 days)
        recent_sales = db.session.query(
            db.func.sum(Sales.total_amount).label('recent_revenue'),
            db.func.sum(Sales.quantity).label('recent_sold')
        ).join(
            Product, Sales.product_id == Product.id
        ).filter(
            Product.seller_id == seller.id,
            Sales.created_at >= last_30_days
        ).first()
        
        recent_revenue = float(recent_sales.recent_revenue or 0)
        recent_sold = int(recent_sales.recent_sold or 0)
        
        # Order metrics
        seller_orders = db.session.query(
            Order.id,
            Order.status,
            Order.created_at
        ).join(
            OrderItem, Order.id == OrderItem.order_id
        ).join(
            Product, OrderItem.product_id == Product.id
        ).filter(
            Product.seller_id == seller.id
        ).distinct().all()
        
        total_orders = len(seller_orders)
        pending_orders = len([o for o in seller_orders if o.status == 'pending'])
        
        # Top selling products
        top_products = db.session.query(
            Product.id,
            Product.name,
            db.func.sum(Sales.quantity).label('total_sold'),
            db.func.sum(Sales.total_amount).label('revenue')
        ).join(
            Sales, Product.id == Sales.product_id
        ).filter(
            Product.seller_id == seller.id
        ).group_by(
            Product.id, Product.name
        ).order_by(
            db.desc('total_sold')
        ).limit(5).all()
        
        top_products_data = []
        for product_id, name, sold, revenue in top_products:
            top_products_data.append({
                "product_id": product_id,
                "name": name,
                "total_sold": int(sold),
                "revenue": float(revenue)
            })
        
        dashboard_data = {
            "seller_info": {
                "id": seller.id,
                "business_name": seller.business_name,
                "status": seller.status,
                "commission_rate": float(seller.commission_rate or 0)
            },
            "metrics": {
                "products": {
                    "total": total_products,
                    "pending": pending_products
                },
                "sales": {
                    "total_revenue": total_revenue,
                    "total_sold": total_sold,
                    "recent_revenue": recent_revenue,
                    "recent_sold": recent_sold
                },
                "orders": {
                    "total": total_orders,
                    "pending": pending_orders
                }
            },
            "top_products": top_products_data
        }
        
        return success_response(
            data=dashboard_data,
            message="Seller dashboard retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get seller dashboard error: {e}")
        return error_response(
            message="Failed to retrieve seller dashboard",
            status_code=500,
            error_code="SELLER_DASHBOARD_FAILED"
        )

@sellers_bp.route('/products', methods=['GET'])
@seller_required_v2()
def get_seller_products(user, seller):
    """
    Get seller's products with pagination and filtering.
    
    GET /api/v1/sellers/products
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status')  # active, pending, inactive
        search = request.args.get('search', '').strip()
        
        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=50)
        
        from app import Product
        
        # Build query
        query = Product.query.filter_by(seller_id=seller.id)
        
        if status:
            if status == 'active':
                query = query.filter_by(is_active=True, status='approved')
            elif status == 'pending':
                query = query.filter_by(status='pending')
            elif status == 'inactive':
                query = query.filter_by(is_active=False)
        
        if search:
            search_filter = f'%{search}%'
            query = query.filter(
                db.or_(
                    Product.name.ilike(search_filter),
                    Product.description.ilike(search_filter),
                    Product.category.ilike(search_filter)
                )
            )
        
        query = query.order_by(Product.created_at.desc())
        
        # Execute paginated query
        paginated_products = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        products_data = []
        for product in paginated_products.items:
            products_data.append({
                "id": product.id,
                "name": product.name,
                "description": product.description,
                "price": float(product.price),
                "category": product.category,
                "stock_quantity": product.stock_quantity,
                "status": getattr(product, 'status', 'active'),
                "is_active": product.is_active,
                "image_url": product.images[0].image_url if product.images else None,
                "created_at": product.created_at.isoformat() if product.created_at else None,
                "updated_at": product.updated_at.isoformat() if product.updated_at else None
            })
        
        return paginated_response(
            data=products_data,
            page=page,
            per_page=per_page,
            total=paginated_products.total,
            message="Seller products retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get seller products error: {e}")
        return error_response(
            message="Failed to retrieve seller products",
            status_code=500,
            error_code="SELLER_PRODUCTS_FETCH_FAILED"
        )

@sellers_bp.route('/products', methods=['POST'])
@seller_required_v2()
@validate_content_type()
@rate_limit_v2(limit=20, window=3600, per='user')  # 20 product creations per hour
def create_product(user, seller):
    """
    Create a new product as a seller.
    
    POST /api/v1/sellers/products
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data
        
        # Validate required fields
        required_fields = ['name', 'description', 'price', 'category', 'stock_quantity']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp
        
        name = data['name'].strip()
        description = data['description'].strip()
        price = data['price']
        category = data['category'].strip()
        stock_quantity = data['stock_quantity']
        sku = data.get('sku', '').strip()
        
        # Validate price
        is_valid_price, normalized_price = validate_price(price)
        if not is_valid_price:
            return validation_error_response(
                errors={"price": ["Invalid price format"]},
                message="Invalid price"
            )
        
        # Validate stock quantity
        if not isinstance(stock_quantity, int) or stock_quantity < 0:
            return validation_error_response(
                errors={"stock_quantity": ["Stock quantity must be a non-negative integer"]},
                message="Invalid stock quantity"
            )
        
        from app import db, Product
        
        # Check if SKU already exists (if provided)
        if sku:
            existing_product = Product.query.filter_by(sku=sku).first()
            if existing_product:
                return error_response(
                    message="Product with this SKU already exists",
                    status_code=409,
                    error_code="SKU_EXISTS"
                )
        
        # Create new product
        new_product = Product(
            seller_id=seller.id,
            name=name,
            description=description,
            price=normalized_price,
            category=category,
            stock_quantity=stock_quantity,
            sku=sku or None,
            status='pending',  # Requires admin approval
            is_active=False,   # Will be activated after approval
            created_at=datetime.utcnow()
        )
        
        db.session.add(new_product)
        db.session.commit()
        
        product_data = {
            "id": new_product.id,
            "name": new_product.name,
            "description": new_product.description,
            "price": float(new_product.price),
            "category": new_product.category,
            "stock_quantity": new_product.stock_quantity,
            "sku": new_product.sku,
            "status": new_product.status,
            "is_active": new_product.is_active,
            "created_at": new_product.created_at.isoformat()
        }
        
        return success_response(
            data=product_data,
            message="Product created successfully and submitted for approval",
            status_code=201
        )
        
    except Exception as e:
        logger.error(f"Create product error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to create product",
            status_code=500,
            error_code="PRODUCT_CREATION_FAILED"
        )

@sellers_bp.route('/orders', methods=['GET'])
@seller_required_v2()
def get_seller_orders(user, seller):
    """
    Get orders containing seller's products.
    
    GET /api/v1/sellers/orders
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status')
        
        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=50)
        
        from app import db, Order, OrderItem, Product, User
        
        # Build query for orders containing seller's products
        query = db.session.query(Order).join(
            OrderItem, Order.id == OrderItem.order_id
        ).join(
            Product, OrderItem.product_id == Product.id
        ).filter(
            Product.seller_id == seller.id
        )
        
        if status:
            query = query.filter(Order.status == status)
        
        query = query.distinct().order_by(Order.created_at.desc())
        
        # Execute paginated query
        paginated_orders = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        orders_data = []
        for order in paginated_orders.items:
            # Get customer info
            customer = User.query.get(order.user_id)
            
            # Get seller's items in this order
            seller_items = db.session.query(OrderItem).join(
                Product, OrderItem.product_id == Product.id
            ).filter(
                OrderItem.order_id == order.id,
                Product.seller_id == seller.id
            ).all()
            
            items_data = []
            seller_total = 0
            for item in seller_items:
                product = Product.query.get(item.product_id)
                item_total = float(item.price * item.quantity)
                seller_total += item_total
                
                items_data.append({
                    "product_id": item.product_id,
                    "product_name": product.name if product else "Unknown",
                    "quantity": item.quantity,
                    "price": float(item.price),
                    "total": item_total
                })
            
            orders_data.append({
                "id": order.id,
                "order_number": f"ORD-{order.id}",
                "status": order.status,
                "customer": {
                    "name": f"{customer.first_name} {customer.last_name}" if customer else "Unknown",
                    "email": customer.email if customer else None
                },
                "seller_items": items_data,
                "seller_total": seller_total,
                "created_at": order.created_at.isoformat() if order.created_at else None
            })
        
        return paginated_response(
            data=orders_data,
            page=page,
            per_page=per_page,
            total=paginated_orders.total,
            message="Seller orders retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get seller orders error: {e}")
        return error_response(
            message="Failed to retrieve seller orders",
            status_code=500,
            error_code="SELLER_ORDERS_FETCH_FAILED"
        )

@sellers_bp.route('/sign-in', methods=['POST'])
@rate_limit_v2(limit=5, window=300, per='ip', block_duration=900)
@validate_content_type()
def seller_sign_in():
    """
    Seller login endpoint.

    POST /api/v1/sellers/sign-in
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['email', 'password']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        email = data['email'].strip().lower()
        password = data['password']

        from app import db, User, Seller, bcrypt
        from flask_jwt_extended import create_access_token, create_refresh_token

        # Find user
        user = User.query.filter_by(email=email).first()

        if not user or not bcrypt.check_password_hash(user.password_hash, password):
            return unauthorized_response("Invalid email or password")

        if not user.is_active:
            return forbidden_response("Account is deactivated")

        # Check if user is a seller
        seller = Seller.query.filter_by(user_id=user.id).first()
        if not seller:
            return forbidden_response("Seller access required")

        if seller.status != 'approved':
            return forbidden_response("Seller account not approved")

        # Create tokens with seller role
        additional_claims = {"role": "seller", "seller_id": seller.id}
        access_token = create_access_token(
            identity=user.id,
            additional_claims=additional_claims
        )
        refresh_token = create_refresh_token(identity=user.id)

        # Update last login
        user.last_login = datetime.utcnow()
        db.session.commit()

        user_data = {
            "id": user.id,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "role": "seller",
            "seller_info": {
                "id": seller.id,
                "business_name": seller.business_name,
                "status": seller.status,
                "commission_rate": float(seller.commission_rate or 0)
            }
        }

        return success_response(
            data={
                "user": user_data,
                "access_token": access_token,
                "refresh_token": refresh_token
            },
            message="Seller login successful"
        )

    except Exception as e:
        logger.error(f"Seller login error: {e}")
        return error_response(
            message="Seller login failed",
            status_code=500,
            error_code="SELLER_LOGIN_FAILED"
        )
