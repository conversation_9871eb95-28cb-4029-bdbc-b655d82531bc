"""
Sellers Routes
==============

Seller management endpoints with consistent response format.
"""

from flask import request
from datetime import datetime, timedelta
import logging

from ..common.versioning import create_versioned_blueprint
from ..common.response_wrapper import (
    success_response, error_response, paginated_response, 
    not_found_response, validation_error_response
)
from ..common.auth_decorators import seller_required_v2, rate_limit_v2, validate_content_type
from ..common.validators import (
    validate_json, validate_required_fields, validate_pagination,
    validate_price
)

logger = logging.getLogger(__name__)

# Create versioned blueprint
sellers_bp = create_versioned_blueprint('sellers', __name__, url_prefix='/sellers')

@sellers_bp.route('/dashboard', methods=['GET'])
@seller_required_v2()
def get_seller_dashboard(user, seller):
    """
    Get seller dashboard overview with key metrics.
    
    GET /api/v1/sellers/dashboard
    """
    try:
        from app import db, Product, Order, OrderItem, Sales
        
        # Calculate date ranges
        today = datetime.utcnow().date()
        last_30_days = today - timedelta(days=30)
        
        # Product metrics
        total_products = Product.query.filter_by(
            seller_id=seller.id,
            is_active=True
        ).count()
        
        pending_products = Product.query.filter_by(
            seller_id=seller.id,
            status='pending'
        ).count()
        
        # Sales metrics
        seller_sales = db.session.query(
            db.func.sum(Sales.total_amount).label('total_revenue'),
            db.func.sum(Sales.quantity).label('total_sold')
        ).join(
            Product, Sales.product_id == Product.id
        ).filter(
            Product.seller_id == seller.id
        ).first()
        
        total_revenue = float(seller_sales.total_revenue or 0)
        total_sold = int(seller_sales.total_sold or 0)
        
        # Recent sales (last 30 days)
        recent_sales = db.session.query(
            db.func.sum(Sales.total_amount).label('recent_revenue'),
            db.func.sum(Sales.quantity).label('recent_sold')
        ).join(
            Product, Sales.product_id == Product.id
        ).filter(
            Product.seller_id == seller.id,
            Sales.created_at >= last_30_days
        ).first()
        
        recent_revenue = float(recent_sales.recent_revenue or 0)
        recent_sold = int(recent_sales.recent_sold or 0)
        
        # Order metrics
        seller_orders = db.session.query(
            Order.id,
            Order.status,
            Order.created_at
        ).join(
            OrderItem, Order.id == OrderItem.order_id
        ).join(
            Product, OrderItem.product_id == Product.id
        ).filter(
            Product.seller_id == seller.id
        ).distinct().all()
        
        total_orders = len(seller_orders)
        pending_orders = len([o for o in seller_orders if o.status == 'pending'])
        
        # Top selling products
        top_products = db.session.query(
            Product.id,
            Product.name,
            db.func.sum(Sales.quantity).label('total_sold'),
            db.func.sum(Sales.total_amount).label('revenue')
        ).join(
            Sales, Product.id == Sales.product_id
        ).filter(
            Product.seller_id == seller.id
        ).group_by(
            Product.id, Product.name
        ).order_by(
            db.desc('total_sold')
        ).limit(5).all()
        
        top_products_data = []
        for product_id, name, sold, revenue in top_products:
            top_products_data.append({
                "product_id": product_id,
                "name": name,
                "total_sold": int(sold),
                "revenue": float(revenue)
            })
        
        dashboard_data = {
            "seller_info": {
                "id": seller.id,
                "business_name": seller.business_name,
                "status": seller.status,
                "commission_rate": float(seller.commission_rate or 0)
            },
            "metrics": {
                "products": {
                    "total": total_products,
                    "pending": pending_products
                },
                "sales": {
                    "total_revenue": total_revenue,
                    "total_sold": total_sold,
                    "recent_revenue": recent_revenue,
                    "recent_sold": recent_sold
                },
                "orders": {
                    "total": total_orders,
                    "pending": pending_orders
                }
            },
            "top_products": top_products_data
        }
        
        return success_response(
            data=dashboard_data,
            message="Seller dashboard retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get seller dashboard error: {e}")
        return error_response(
            message="Failed to retrieve seller dashboard",
            status_code=500,
            error_code="SELLER_DASHBOARD_FAILED"
        )

@sellers_bp.route('/products', methods=['GET'])
@seller_required_v2()
def get_seller_products(user, seller):
    """
    Get seller's products with pagination and filtering.
    
    GET /api/v1/sellers/products
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status')  # active, pending, inactive
        search = request.args.get('search', '').strip()
        
        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=50)
        
        from app import Product
        
        # Build query
        query = Product.query.filter_by(seller_id=seller.id)
        
        if status:
            if status == 'active':
                query = query.filter_by(is_active=True, status='approved')
            elif status == 'pending':
                query = query.filter_by(status='pending')
            elif status == 'inactive':
                query = query.filter_by(is_active=False)
        
        if search:
            search_filter = f'%{search}%'
            query = query.filter(
                db.or_(
                    Product.name.ilike(search_filter),
                    Product.description.ilike(search_filter),
                    Product.category.ilike(search_filter)
                )
            )
        
        query = query.order_by(Product.created_at.desc())
        
        # Execute paginated query
        paginated_products = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        products_data = []
        for product in paginated_products.items:
            products_data.append({
                "id": product.id,
                "name": product.name,
                "description": product.description,
                "price": float(product.price),
                "category": product.category,
                "stock_quantity": product.stock_quantity,
                "status": getattr(product, 'status', 'active'),
                "is_active": product.is_active,
                "image_url": product.images[0].image_url if product.images else None,
                "created_at": product.created_at.isoformat() if product.created_at else None,
                "updated_at": product.updated_at.isoformat() if product.updated_at else None
            })
        
        return paginated_response(
            data=products_data,
            page=page,
            per_page=per_page,
            total=paginated_products.total,
            message="Seller products retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get seller products error: {e}")
        return error_response(
            message="Failed to retrieve seller products",
            status_code=500,
            error_code="SELLER_PRODUCTS_FETCH_FAILED"
        )

@sellers_bp.route('/products', methods=['POST'])
@seller_required_v2()
@validate_content_type()
@rate_limit_v2(limit=20, window=3600, per='user')  # 20 product creations per hour
def create_product(user, seller):
    """
    Create a new product as a seller.
    
    POST /api/v1/sellers/products
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data
        
        # Validate required fields
        required_fields = ['name', 'description', 'price', 'category', 'stock_quantity']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp
        
        name = data['name'].strip()
        description = data['description'].strip()
        price = data['price']
        category = data['category'].strip()
        stock_quantity = data['stock_quantity']
        sku = data.get('sku', '').strip()
        
        # Validate price
        is_valid_price, normalized_price = validate_price(price)
        if not is_valid_price:
            return validation_error_response(
                errors={"price": ["Invalid price format"]},
                message="Invalid price"
            )
        
        # Validate stock quantity
        if not isinstance(stock_quantity, int) or stock_quantity < 0:
            return validation_error_response(
                errors={"stock_quantity": ["Stock quantity must be a non-negative integer"]},
                message="Invalid stock quantity"
            )
        
        from app import db, Product
        
        # Check if SKU already exists (if provided)
        if sku:
            existing_product = Product.query.filter_by(sku=sku).first()
            if existing_product:
                return error_response(
                    message="Product with this SKU already exists",
                    status_code=409,
                    error_code="SKU_EXISTS"
                )
        
        # Create new product
        new_product = Product(
            seller_id=seller.id,
            name=name,
            description=description,
            price=normalized_price,
            category=category,
            stock_quantity=stock_quantity,
            sku=sku or None,
            status='pending',  # Requires admin approval
            is_active=False,   # Will be activated after approval
            created_at=datetime.utcnow()
        )
        
        db.session.add(new_product)
        db.session.commit()
        
        product_data = {
            "id": new_product.id,
            "name": new_product.name,
            "description": new_product.description,
            "price": float(new_product.price),
            "category": new_product.category,
            "stock_quantity": new_product.stock_quantity,
            "sku": new_product.sku,
            "status": new_product.status,
            "is_active": new_product.is_active,
            "created_at": new_product.created_at.isoformat()
        }
        
        return success_response(
            data=product_data,
            message="Product created successfully and submitted for approval",
            status_code=201
        )
        
    except Exception as e:
        logger.error(f"Create product error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to create product",
            status_code=500,
            error_code="PRODUCT_CREATION_FAILED"
        )

@sellers_bp.route('/orders', methods=['GET'])
@seller_required_v2()
def get_seller_orders(user, seller):
    """
    Get orders containing seller's products.
    
    GET /api/v1/sellers/orders
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status')
        
        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=50)
        
        from app import db, Order, OrderItem, Product, User
        
        # Build query for orders containing seller's products
        query = db.session.query(Order).join(
            OrderItem, Order.id == OrderItem.order_id
        ).join(
            Product, OrderItem.product_id == Product.id
        ).filter(
            Product.seller_id == seller.id
        )
        
        if status:
            query = query.filter(Order.status == status)
        
        query = query.distinct().order_by(Order.created_at.desc())
        
        # Execute paginated query
        paginated_orders = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        orders_data = []
        for order in paginated_orders.items:
            # Get customer info
            customer = User.query.get(order.user_id)
            
            # Get seller's items in this order
            seller_items = db.session.query(OrderItem).join(
                Product, OrderItem.product_id == Product.id
            ).filter(
                OrderItem.order_id == order.id,
                Product.seller_id == seller.id
            ).all()
            
            items_data = []
            seller_total = 0
            for item in seller_items:
                product = Product.query.get(item.product_id)
                item_total = float(item.price * item.quantity)
                seller_total += item_total
                
                items_data.append({
                    "product_id": item.product_id,
                    "product_name": product.name if product else "Unknown",
                    "quantity": item.quantity,
                    "price": float(item.price),
                    "total": item_total
                })
            
            orders_data.append({
                "id": order.id,
                "order_number": f"ORD-{order.id}",
                "status": order.status,
                "customer": {
                    "name": f"{customer.first_name} {customer.last_name}" if customer else "Unknown",
                    "email": customer.email if customer else None
                },
                "seller_items": items_data,
                "seller_total": seller_total,
                "created_at": order.created_at.isoformat() if order.created_at else None
            })
        
        return paginated_response(
            data=orders_data,
            page=page,
            per_page=per_page,
            total=paginated_orders.total,
            message="Seller orders retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get seller orders error: {e}")
        return error_response(
            message="Failed to retrieve seller orders",
            status_code=500,
            error_code="SELLER_ORDERS_FETCH_FAILED"
        )
