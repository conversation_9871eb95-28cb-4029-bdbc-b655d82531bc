from itsdangerous import URLSafeTimedSerializer
import os

# Same secret key as in app.py
SECRET_KEY = 'your-secret-key-here'
serializer = URLSafeTimedSerializer(SECRET_KEY)

# Generate token for user ID 1 (assuming this user exists)
user_id = 1
token = serializer.dumps(user_id)
print(f"Token for user {user_id}: {token}")

# Test token verification
try:
    decoded_user_id = serializer.loads(token, max_age=3600)
    print(f"Token verified successfully. User ID: {decoded_user_id}")
except Exception as e:
    print(f"Token verification failed: {e}")
