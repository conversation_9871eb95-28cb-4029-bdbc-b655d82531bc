from flask import Flask
from flask_sqlalchemy import SQLAlchemy
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db = SQLAlchemy(app)

# Simple test model
class TestModel(db.Model):
    __tablename__ = 'test_table'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)

@app.route('/')
def home():
    return "Database test app is working!"

if __name__ == '__main__':
    print("Testing database connection and model creation...")
    try:
        with app.app_context():
            print("Creating database tables...")
            db.create_all()
            print("✅ Database tables created successfully!")
            
            # Test inserting data
            test_record = TestModel(name='Test Record')
            db.session.add(test_record)
            db.session.commit()
            print("✅ Test record inserted successfully!")
            
            # Test querying data
            records = TestModel.query.all()
            print(f"✅ Found {len(records)} records in test table")
            
            # Clean up
            db.session.delete(test_record)
            db.session.commit()
            print("✅ Test record deleted successfully!")
            
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        import traceback
        traceback.print_exc()
    
    print("Starting Flask app...")
    app.run(debug=True, host='0.0.0.0', port=5002)
