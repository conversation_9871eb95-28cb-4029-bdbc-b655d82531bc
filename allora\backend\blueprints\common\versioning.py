"""
API Versioning Strategy
=======================

Implements consistent API versioning across all endpoints.
All new endpoints will use /api/v1/ prefix.
"""

from flask import Blueprint
from typing import Optional

# Current API version
CURRENT_API_VERSION = "v1"
API_VERSION_PREFIX = f"/api/{CURRENT_API_VERSION}"

def create_versioned_blueprint(
    name: str,
    import_name: str,
    url_prefix: Optional[str] = None,
    **kwargs
) -> Blueprint:
    """
    Create a Flask blueprint with versioned URL prefix.
    
    Args:
        name: Blueprint name
        import_name: Import name (usually __name__)
        url_prefix: Additional URL prefix after version
        **kwargs: Additional blueprint arguments
    
    Returns:
        Flask Blueprint with versioned URL prefix
    """
    if url_prefix:
        # Combine version prefix with blueprint prefix
        full_prefix = f"{API_VERSION_PREFIX}{url_prefix}"
    else:
        full_prefix = API_VERSION_PREFIX
    
    return Blueprint(
        name=name,
        import_name=import_name,
        url_prefix=full_prefix,
        **kwargs
    )

def get_api_version() -> str:
    """Get current API version."""
    return CURRENT_API_VERSION

def get_version_prefix() -> str:
    """Get current API version prefix."""
    return API_VERSION_PREFIX

# Version-specific configurations
VERSION_CONFIG = {
    "v1": {
        "supported": True,
        "deprecated": False,
        "sunset_date": None,
        "description": "Current stable API version"
    }
}

def is_version_supported(version: str) -> bool:
    """Check if API version is supported."""
    return version in VERSION_CONFIG and VERSION_CONFIG[version]["supported"]

def is_version_deprecated(version: str) -> bool:
    """Check if API version is deprecated."""
    return version in VERSION_CONFIG and VERSION_CONFIG[version]["deprecated"]
