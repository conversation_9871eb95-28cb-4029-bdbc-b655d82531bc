#!/usr/bin/env python3
"""
Redis Configuration Test Suite
==============================

Comprehensive testing for the Redis configuration functionality.
Tests all components, caching, sessions, and integration.

Author: Allora Development Team
Date: 2025-07-13
"""

import sys
import os
import time
import json
import uuid
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_redis_config_structure():
    """Test the Redis config module structure"""
    print("🔍 Testing Redis Config Structure")
    print("=" * 40)
    
    try:
        # Import Redis config module
        import redis_config
        
        print("✅ Redis config module imported successfully")
        
        # Test core classes
        required_classes = [
            'RedisConfig', 'RedisCache', 'RedisSessionManager'
        ]
        
        for class_name in required_classes:
            if hasattr(redis_config, class_name):
                print(f"   ✅ {class_name}: Found")
            else:
                print(f"   ❌ {class_name}: Missing")
        
        # Test factory functions
        factory_functions = [
            'get_redis_config', 'get_redis_cache', 'get_session_manager', 'test_redis_connection'
        ]
        
        for func_name in factory_functions:
            if hasattr(redis_config, func_name):
                print(f"   ✅ {func_name}: Found")
            else:
                print(f"   ❌ {func_name}: Missing")
        
        return True
        
    except ImportError as e:
        print(f"❌ Redis config import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Redis config structure test error: {e}")
        return False

def test_redis_connection():
    """Test Redis connection functionality"""
    print("\n🔗 Testing Redis Connection")
    print("=" * 35)
    
    try:
        from redis_config import get_redis_config, test_redis_connection
        
        # Test connection status
        connection_status = test_redis_connection()
        print("✅ Redis connection test completed")
        
        print(f"   Host: {connection_status['host']}")
        print(f"   Port: {connection_status['port']}")
        print(f"   Available: {connection_status['available']}")
        
        if connection_status['available']:
            info = connection_status['info']
            print(f"   Version: {info['version']}")
            print(f"   Memory Used: {info['memory_used']}")
            print(f"   Connected Clients: {info['connected_clients']}")
            print(f"   Uptime: {info['uptime']} seconds")
        else:
            print(f"   Error: {connection_status['error']}")
        
        # Test Redis config instance
        redis_config = get_redis_config()
        print("✅ Redis config instance created")
        
        # Test availability check
        is_available = redis_config.is_available()
        print(f"   ✅ Availability check: {is_available}")
        
        # Test client retrieval
        client = redis_config.get_client()
        if client:
            print("   ✅ Redis client: Retrieved successfully")
        else:
            print("   ⚠️  Redis client: Not available (fallback mode)")
        
        return True
        
    except Exception as e:
        print(f"❌ Redis connection test error: {e}")
        return False

def test_redis_cache():
    """Test Redis cache functionality"""
    print("\n💾 Testing Redis Cache")
    print("=" * 25)
    
    try:
        from redis_config import get_redis_cache
        
        # Get cache instance
        cache = get_redis_cache()
        print("✅ Redis cache instance created")
        
        # Test basic cache operations
        test_key = f"test_key_{int(time.time())}"
        test_value = {"message": "Hello Redis!", "timestamp": time.time()}
        
        # Test set operation
        set_result = cache.set(test_key, test_value, expire=60)
        print(f"   ✅ Cache set: {set_result}")
        
        # Test get operation
        retrieved_value = cache.get(test_key)
        print(f"   ✅ Cache get: {retrieved_value is not None}")
        
        if retrieved_value:
            print(f"      Retrieved: {retrieved_value}")
            data_matches = retrieved_value == test_value
            print(f"      Data matches: {data_matches}")
        
        # Test exists operation
        exists = cache.exists(test_key)
        print(f"   ✅ Cache exists: {exists}")
        
        # Test delete operation
        delete_result = cache.delete(test_key)
        print(f"   ✅ Cache delete: {delete_result}")
        
        # Verify deletion
        after_delete = cache.get(test_key)
        print(f"   ✅ After delete: {after_delete is None}")
        
        # Test with expiration
        expire_key = f"expire_test_{int(time.time())}"
        cache.set(expire_key, "expire_test", expire=2)
        print("   ✅ Set with 2-second expiration")
        
        # Check immediate retrieval
        immediate = cache.get(expire_key)
        print(f"   ✅ Immediate retrieval: {immediate is not None}")
        
        return True
        
    except Exception as e:
        print(f"❌ Redis cache test error: {e}")
        return False

def test_redis_sessions():
    """Test Redis session management"""
    print("\n👤 Testing Redis Sessions")
    print("=" * 30)
    
    try:
        from redis_config import get_session_manager
        
        # Get session manager
        session_manager = get_session_manager()
        print("✅ Redis session manager created")
        
        # Test session operations
        session_id = str(uuid.uuid4())
        session_data = {
            "user_id": 12345,
            "username": "test_user",
            "preferences": {"theme": "dark", "language": "en"},
            "login_time": time.time()
        }
        
        # Test set session
        set_result = session_manager.set_session(session_id, session_data, expire=3600)
        print(f"   ✅ Session set: {set_result}")
        
        # Test get session
        retrieved_session = session_manager.get_session(session_id)
        print(f"   ✅ Session get: {retrieved_session is not None}")
        
        if retrieved_session:
            print(f"      User ID: {retrieved_session.get('user_id')}")
            print(f"      Username: {retrieved_session.get('username')}")
            data_matches = retrieved_session['user_id'] == session_data['user_id']
            print(f"      Data matches: {data_matches}")
        
        # Test session deletion
        delete_result = session_manager.delete_session(session_id)
        print(f"   ✅ Session delete: {delete_result}")
        
        # Verify deletion
        after_delete = session_manager.get_session(session_id)
        print(f"   ✅ After delete: {after_delete is None}")
        
        return True
        
    except Exception as e:
        print(f"❌ Redis session test error: {e}")
        return False

def test_app_integration():
    """Test Redis integration with Flask app"""
    print("\n🔗 Testing App Integration")
    print("=" * 30)
    
    try:
        # Import Flask app
        from app import app, redis_config, redis_cache, session_manager
        
        print("✅ Flask app imported successfully")
        print("✅ Redis components imported from app")
        
        # Test Redis config from app
        if redis_config:
            print(f"   ✅ Redis config: {redis_config.host}:{redis_config.port}")
            print(f"   ✅ Redis available: {redis_config.is_available()}")
        else:
            print("   ❌ Redis config: Not available")
        
        # Test cache from app
        if redis_cache:
            print("   ✅ Redis cache: Available")
        else:
            print("   ❌ Redis cache: Not available")
        
        # Test session manager from app
        if session_manager:
            print("   ✅ Session manager: Available")
        else:
            print("   ❌ Session manager: Not available")
        
        return True
        
    except Exception as e:
        print(f"❌ App integration test error: {e}")
        return False

def test_api_endpoints():
    """Test Redis-related API endpoints"""
    print("\n🌐 Testing API Endpoints")
    print("=" * 30)
    
    try:
        import requests
        
        base_url = "http://127.0.0.1:5000"
        
        # Test endpoints
        endpoints = [
            {
                'name': 'Cache Test',
                'url': f"{base_url}/api/cache/test",
                'description': 'Test Redis cache functionality'
            },
            {
                'name': 'Session Test',
                'url': f"{base_url}/api/session/test",
                'description': 'Test Redis session management'
            },
            {
                'name': 'Health Check',
                'url': f"{base_url}/api/health",
                'description': 'System health including Redis status'
            }
        ]
        
        for endpoint in endpoints:
            print(f"🔍 Testing: {endpoint['name']}")
            print(f"   URL: {endpoint['url']}")
            
            try:
                response = requests.get(endpoint['url'], timeout=10)
                print(f"   ✅ Status Code: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"   ✅ Response Format: Valid JSON")
                        
                        # Check for Redis-specific fields
                        if 'redis_available' in data:
                            print(f"   ✅ Redis Available: {data['redis_available']}")
                        
                        if 'status' in data:
                            print(f"   ✅ Status: {data['status']}")
                        
                    except:
                        print(f"   ⚠️  Response: Non-JSON content")
                else:
                    print(f"   ⚠️  HTTP Status: {response.status_code}")
                    
            except requests.exceptions.ConnectionError:
                print(f"   ❌ Connection Error: Server not running")
            except requests.exceptions.Timeout:
                print(f"   ❌ Timeout Error: Request took too long")
            except Exception as e:
                print(f"   ❌ Error: {e}")
            
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ API endpoints test error: {e}")
        return False

def analyze_redis_system():
    """Analyze the Redis system's purpose and functionality"""
    print("\n📋 Redis System Analysis")
    print("=" * 30)
    
    print("🎯 PRIMARY PURPOSE:")
    print("   The Redis Configuration provides centralized Redis management")
    print("   for caching, session storage, and high-performance data access")
    print("   with automatic fallback mechanisms.")
    print()
    
    print("🔧 KEY FEATURES:")
    print("   1. Centralized Configuration")
    print("      • Single point of Redis connection management")
    print("      • Environment-based configuration")
    print("      • Connection pooling and health monitoring")
    print()
    
    print("   2. Redis Cache System")
    print("      • JSON serialization/deserialization")
    print("      • Automatic expiration handling")
    print("      • Fallback to in-memory cache")
    print()
    
    print("   3. Session Management")
    print("      • Hash-based session storage")
    print("      • Configurable session expiration")
    print("      • Fallback to in-memory sessions")
    print()
    
    print("   4. Fallback Mechanisms")
    print("      • Graceful degradation when Redis unavailable")
    print("      • In-memory cache and session fallbacks")
    print("      • Automatic cleanup of expired entries")
    print()
    
    print("📊 BUSINESS VALUE:")
    print("   • Improved application performance through caching")
    print("   • Scalable session management")
    print("   • High availability with fallback mechanisms")
    print("   • Centralized Redis configuration and monitoring")
    print("   • Reduced database load through intelligent caching")
    print()

def run_all_tests():
    """Run all Redis configuration tests"""
    print("🚀 Redis Configuration Test Suite")
    print("=" * 40)
    print()
    
    tests = [
        test_redis_config_structure,
        test_redis_connection,
        test_redis_cache,
        test_redis_sessions,
        test_app_integration
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    # Analyze Redis system
    analyze_redis_system()
    
    # Test API endpoints (requires running server)
    print("⚠️  Note: API endpoint testing requires the server to be running")
    print("   Start the server with: python run_with_waitress.py")
    print()
    
    try:
        test_api_endpoints()
    except Exception as e:
        print(f"❌ API endpoint testing failed: {e}")
        print("   This is expected if the server is not running")
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 30)
    passed = sum(results)
    total = len(results)
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! Redis configuration is properly integrated.")
    else:
        print("⚠️  Some tests failed. Please review the Redis configuration.")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
