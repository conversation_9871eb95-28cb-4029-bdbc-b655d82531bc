#!/usr/bin/env python3
"""
Logging Configuration Test Suite
===============================

Comprehensive testing for the logging configuration functionality.
Tests all components, handlers, and integration.

Author: Allora Development Team
Date: 2025-07-13
"""

import sys
import os
import logging
import tempfile
import json
from pathlib import Path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_logging_config_structure():
    """Test the logging config module structure"""
    print("🔍 Testing Logging Config Structure")
    print("=" * 45)
    
    try:
        # Import logging config module
        import logging_config
        
        print("✅ Logging config module imported successfully")
        
        # Test core classes
        required_classes = [
            'JSONFormatter', 'PerformanceFilter', 'ErrorFilter', 'AlloraLogger'
        ]
        
        for class_name in required_classes:
            if hasattr(logging_config, class_name):
                print(f"   ✅ {class_name}: Found")
            else:
                print(f"   ❌ {class_name}: Missing")
        
        # Test functions
        required_functions = [
            'get_logger', 'setup_application_logging', 'log_startup_info'
        ]
        
        for func_name in required_functions:
            if hasattr(logging_config, func_name):
                print(f"   ✅ {func_name}: Found")
            else:
                print(f"   ❌ {func_name}: Missing")
        
        # Test decorators
        decorators = ['log_performance', 'log_api_call']
        for decorator_name in decorators:
            if hasattr(logging_config, decorator_name):
                print(f"   ✅ {decorator_name} decorator: Found")
            else:
                print(f"   ❌ {decorator_name} decorator: Missing")
        
        # Test global instance
        if hasattr(logging_config, 'allora_logger'):
            print("   ✅ Global allora_logger instance: Found")
        else:
            print("   ❌ Global allora_logger instance: Missing")
        
        return True
        
    except ImportError as e:
        print(f"❌ Logging config import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Logging config structure test error: {e}")
        return False

def test_json_formatter():
    """Test JSON formatter functionality"""
    print("\n📝 Testing JSON Formatter")
    print("=" * 30)
    
    try:
        from logging_config import JSONFormatter
        
        # Create formatter
        formatter = JSONFormatter()
        print("✅ JSONFormatter created successfully")
        
        # Create test log record
        logger = logging.getLogger('test')
        record = logger.makeRecord(
            name='test_logger',
            level=logging.INFO,
            fn='test.py',
            lno=42,
            msg='Test message',
            args=(),
            exc_info=None
        )
        
        # Add extra fields
        record.user_id = 'user123'
        record.request_id = 'req456'
        record.ip_address = '***********'
        
        # Format record
        formatted = formatter.format(record)
        print("✅ Log record formatted successfully")
        
        # Parse JSON
        try:
            log_data = json.loads(formatted)
            print("✅ JSON format is valid")
            
            # Check required fields
            required_fields = ['timestamp', 'level', 'logger', 'message', 'module', 'function', 'line']
            for field in required_fields:
                if field in log_data:
                    print(f"   ✅ {field}: Present")
                else:
                    print(f"   ❌ {field}: Missing")
            
            # Check extra fields
            extra_fields = ['user_id', 'request_id', 'ip_address']
            for field in extra_fields:
                if field in log_data:
                    print(f"   ✅ {field}: {log_data[field]}")
                else:
                    print(f"   ❌ {field}: Missing")
            
        except json.JSONDecodeError:
            print("❌ Invalid JSON format")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ JSON formatter test error: {e}")
        return False

def test_log_filters():
    """Test logging filters"""
    print("\n🔍 Testing Log Filters")
    print("=" * 25)
    
    try:
        from logging_config import PerformanceFilter, ErrorFilter
        
        # Test PerformanceFilter
        perf_filter = PerformanceFilter()
        print("✅ PerformanceFilter created")
        
        # Create test records
        logger = logging.getLogger('test')
        
        # Performance record
        perf_record = logger.makeRecord('test', logging.INFO, 'test.py', 1, 'Test', (), None)
        perf_record.performance = True
        
        # Regular record
        regular_record = logger.makeRecord('test', logging.INFO, 'test.py', 1, 'Test', (), None)
        
        # Test filter
        if perf_filter.filter(perf_record):
            print("   ✅ Performance filter: Accepts performance records")
        else:
            print("   ❌ Performance filter: Rejects performance records")
        
        if not perf_filter.filter(regular_record):
            print("   ✅ Performance filter: Rejects regular records")
        else:
            print("   ❌ Performance filter: Accepts regular records")
        
        # Test ErrorFilter
        error_filter = ErrorFilter()
        print("✅ ErrorFilter created")
        
        # Error record
        error_record = logger.makeRecord('test', logging.ERROR, 'test.py', 1, 'Error', (), None)
        
        # Info record
        info_record = logger.makeRecord('test', logging.INFO, 'test.py', 1, 'Info', (), None)
        
        # Test filter
        if error_filter.filter(error_record):
            print("   ✅ Error filter: Accepts error records")
        else:
            print("   ❌ Error filter: Rejects error records")
        
        if not error_filter.filter(info_record):
            print("   ✅ Error filter: Rejects info records")
        else:
            print("   ❌ Error filter: Accepts info records")
        
        return True
        
    except Exception as e:
        print(f"❌ Log filters test error: {e}")
        return False

def test_allora_logger():
    """Test AlloraLogger functionality"""
    print("\n🚀 Testing AlloraLogger")
    print("=" * 25)
    
    try:
        from logging_config import AlloraLogger
        
        # Create temporary directory for test logs
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = os.path.join(temp_dir, 'test.log')
            
            # Create logger
            allora_logger = AlloraLogger('test_logger')
            print("✅ AlloraLogger created")
            
            # Setup logging
            logger = allora_logger.setup_logging(
                log_level='INFO',
                log_file=log_file,
                enable_json=False,
                enable_performance_logging=True
            )
            print("✅ Logging setup completed")
            
            # Test logging methods
            allora_logger.log_performance('test_operation', 0.123)
            print("   ✅ log_performance: Works")
            
            allora_logger.log_user_action('user123', 'login', {'ip': '***********'})
            print("   ✅ log_user_action: Works")
            
            allora_logger.log_api_request('GET', '/api/test', 'user123', '***********', 0.045)
            print("   ✅ log_api_request: Works")
            
            allora_logger.log_database_operation('SELECT', 'users', 0.012)
            print("   ✅ log_database_operation: Works")
            
            # Check if log files were created
            if os.path.exists(log_file):
                print("   ✅ Main log file: Created")
            else:
                print("   ❌ Main log file: Not created")
            
            error_file = log_file.replace('.log', '_errors.log')
            if os.path.exists(error_file):
                print("   ✅ Error log file: Created")
            else:
                print("   ❌ Error log file: Not created")
            
            perf_file = log_file.replace('.log', '_performance.log')
            if os.path.exists(perf_file):
                print("   ✅ Performance log file: Created")
            else:
                print("   ❌ Performance log file: Not created")
        
        return True
        
    except Exception as e:
        print(f"❌ AlloraLogger test error: {e}")
        return False

def test_app_integration():
    """Test logging integration with Flask app"""
    print("\n🔗 Testing App Integration")
    print("=" * 30)
    
    try:
        # Import Flask app
        from app import app, logger
        
        print("✅ Flask app imported successfully")
        print("✅ Logger from app imported successfully")
        
        # Test logger type
        if isinstance(logger, logging.Logger):
            print("   ✅ Logger is proper logging.Logger instance")
        else:
            print(f"   ❌ Logger is {type(logger)}, expected logging.Logger")
        
        # Test logging functionality
        logger.info("Test log message from integration test")
        print("   ✅ Logger.info: Works")
        
        logger.warning("Test warning message")
        print("   ✅ Logger.warning: Works")
        
        logger.error("Test error message")
        print("   ✅ Logger.error: Works")
        
        # Check if log files exist
        log_files = [
            'logs/allora.log',
            'logs/allora_errors.log',
            'logs/allora_performance.log'
        ]
        
        for log_file in log_files:
            if os.path.exists(log_file):
                print(f"   ✅ {log_file}: Exists")
            else:
                print(f"   ❌ {log_file}: Missing")
        
        return True
        
    except Exception as e:
        print(f"❌ App integration test error: {e}")
        return False

def test_logging_decorators():
    """Test logging decorators"""
    print("\n🎭 Testing Logging Decorators")
    print("=" * 35)
    
    try:
        from logging_config import log_performance
        
        print("✅ Logging decorators imported")
        
        # Test performance decorator
        @log_performance("test_function")
        def test_function():
            import time
            time.sleep(0.01)  # Small delay for testing
            return "test_result"
        
        result = test_function()
        print("   ✅ @log_performance decorator: Works")
        print(f"   ✅ Function result: {result}")
        
        # Test decorator with exception
        @log_performance("failing_function")
        def failing_function():
            raise ValueError("Test exception")
        
        try:
            failing_function()
        except ValueError:
            print("   ✅ @log_performance with exception: Handled correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Logging decorators test error: {e}")
        return False

def analyze_logging_system():
    """Analyze the logging system's purpose and functionality"""
    print("\n📋 Logging System Analysis")
    print("=" * 35)
    
    print("🎯 PRIMARY PURPOSE:")
    print("   The Logging Configuration provides comprehensive logging")
    print("   functionality for the Allora e-commerce platform with")
    print("   structured logging, performance tracking, and error monitoring.")
    print()
    
    print("🔧 KEY FEATURES:")
    print("   1. Multi-Handler Logging")
    print("      • Console output for development")
    print("      • File logging with rotation")
    print("      • Separate error log files")
    print("      • Performance-specific logging")
    print()
    
    print("   2. Structured Logging")
    print("      • JSON formatter for structured data")
    print("      • Custom fields (user_id, request_id, ip_address)")
    print("      • Contextual information tracking")
    print()
    
    print("   3. Performance Monitoring")
    print("      • Function execution time tracking")
    print("      • API request performance logging")
    print("      • Database operation timing")
    print("      • Custom performance metrics")
    print()
    
    print("   4. Error Tracking")
    print("      • Dedicated error log files")
    print("      • Exception information capture")
    print("      • Error filtering and categorization")
    print()
    
    print("📊 BUSINESS VALUE:")
    print("   • Comprehensive application monitoring")
    print("   • Performance optimization insights")
    print("   • Error tracking and debugging")
    print("   • Audit trail for user actions")
    print("   • Operational visibility and alerting")
    print()

def run_all_tests():
    """Run all logging configuration tests"""
    print("🚀 Logging Configuration Test Suite")
    print("=" * 45)
    print()
    
    tests = [
        test_logging_config_structure,
        test_json_formatter,
        test_log_filters,
        test_allora_logger,
        test_app_integration,
        test_logging_decorators
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    # Analyze logging system
    analyze_logging_system()
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 30)
    passed = sum(results)
    total = len(results)
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! Logging configuration is properly integrated.")
    else:
        print("⚠️  Some tests failed. Please review the logging configuration.")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
