#!/usr/bin/env python3
"""
Quick Database Clear Script for Allora E-commerce Project
=========================================================

This is a simplified version of the database clearing script for quick use.
It clears all data from all tables with minimal prompts.

Usage:
    python quick_clear_db.py

Author: Allora Development Team
Date: 2025-01-07
"""

import os
import sys
from datetime import datetime
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import text, inspect
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db = SQLAlchemy(app)

def quick_clear_database():
    """Quickly clear all data from the database"""
    print("🚀 Allora Quick Database Clear")
    print("="*50)
    print("⚠️  This will clear ALL data from ALL tables!")
    print("📋 Database structure will be preserved")
    
    # Simple confirmation
    response = input("\nContinue? (y/N): ")
    if response.lower() not in ['y', 'yes']:
        print("❌ Operation cancelled")
        return False
    
    try:
        with app.app_context():
            print("\n🔍 Discovering tables...")
            
            # Get all tables
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()
            tables = [table for table in tables if not table.startswith('alembic')]
            
            if not tables:
                print("❌ No tables found")
                return False
            
            print(f"📋 Found {len(tables)} tables to clear")
            
            # Clear all tables
            print("\n🧹 Clearing data...")
            cleared_count = 0
            failed_tables = []
            
            # Disable foreign key checks
            db.session.execute(text("SET FOREIGN_KEY_CHECKS = 0"))
            
            for table in tables:
                try:
                    db.session.execute(text(f"TRUNCATE TABLE `{table}`"))
                    print(f"  ✅ {table}")
                    cleared_count += 1
                except Exception as e:
                    print(f"  ❌ {table}: {str(e)[:50]}...")
                    failed_tables.append(table)
            
            # Re-enable foreign key checks
            db.session.execute(text("SET FOREIGN_KEY_CHECKS = 1"))
            db.session.commit()
            
            # Reset auto-increment counters
            print("\n🔄 Resetting auto-increment counters...")
            reset_count = 0
            for table in tables:
                try:
                    db.session.execute(text(f"ALTER TABLE `{table}` AUTO_INCREMENT = 1"))
                    reset_count += 1
                except:
                    pass  # Some tables might not have auto-increment
            
            db.session.commit()
            
            # Summary
            print("\n" + "="*50)
            print("📊 OPERATION SUMMARY")
            print("="*50)
            print(f"🕒 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"✅ Tables cleared: {cleared_count}")
            print(f"🔄 Auto-increment reset: {reset_count}")
            
            if failed_tables:
                print(f"❌ Failed tables: {len(failed_tables)}")
                print(f"   {', '.join(failed_tables)}")
            
            if cleared_count > 0:
                print("\n🎉 Database cleared successfully!")
                return True
            else:
                print("\n❌ No tables were cleared")
                return False
                
    except Exception as e:
        print(f"\n❌ Error during clearing: {e}")
        db.session.rollback()
        return False

def main():
    """Main function"""
    try:
        success = quick_clear_database()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n❌ Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
