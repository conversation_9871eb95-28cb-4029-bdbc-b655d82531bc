#!/usr/bin/env python3
"""
WebSocket Error Fix Script
=========================

Fixes import errors and compatibility issues in WebSocket files.

Issues Fixed:
1. Missing auth.py and database.py imports in websocket_routes.py
2. FastAPI incompatibility with Flask app
3. Deprecated file warnings

Author: Allora Development Team
Date: 2025-07-13
"""

import os
import shutil
from datetime import datetime

def fix_websocket_routes():
    """Fix websocket_routes.py import errors"""
    print("🔧 Fixing websocket_routes.py...")
    
    file_path = "websocket_routes.py"
    
    if not os.path.exists(file_path):
        print("⚠️  websocket_routes.py not found")
        return False
    
    # Read current content
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Add deprecation notice if not already present
    deprecation_notice = '''"""
DEPRECATED: FastAPI WebSocket Routes - NOT COMPATIBLE WITH FLASK
==============================================================

This file contains FastAPI WebSocket routes that are NOT compatible
with the Flask application. Use flask_socketio_manager.py instead.

Issues:
- Missing auth.py file (import error)
- Missing database.py file (import error)  
- FastAPI routes cannot be served by Flask app

Status: DEPRECATED - DO NOT IMPORT
Alternative: Use flask_socketio_manager.py for all WebSocket functionality

Date Deprecated: 2025-07-13
"""

'''
    
    if 'DEPRECATED' not in content:
        # Add deprecation notice at the top
        new_content = deprecation_notice + content
        
        # Comment out problematic imports
        fixes = [
            ('from auth import get_current_user_optional', '# from auth import get_current_user_optional  # FIXED: File does not exist'),
            ('from database import get_db', '# from database import get_db  # FIXED: File does not exist'),
        ]
        
        for old, new in fixes:
            new_content = new_content.replace(old, new)
        
        # Write fixed content
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ websocket_routes.py fixed - deprecated and imports commented")
        return True
    else:
        print("✅ websocket_routes.py already fixed")
        return True

def fix_websocket_manager():
    """Fix websocket_manager.py compatibility issues"""
    print("🔧 Fixing websocket_manager.py...")
    
    file_path = "websocket_manager.py"
    
    if not os.path.exists(file_path):
        print("⚠️  websocket_manager.py not found")
        return False
    
    # Read current content
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Add deprecation notice if not already present
    deprecation_notice = '''"""
DEPRECATED: FastAPI WebSocket Manager - USE FLASK-SOCKETIO INSTEAD
================================================================

This file uses FastAPI WebSocket which is incompatible with the Flask application.
Use flask_socketio_manager.py for Flask-SocketIO implementation instead.

Issues:
- FastAPI WebSocket not compatible with Flask app
- Cannot be served by Flask/Waitress server
- Requires separate FastAPI server

Status: DEPRECATED - USE flask_socketio_manager.py INSTEAD
Alternative: flask_socketio_manager.py provides all the same functionality

Date Deprecated: 2025-07-13
"""

'''
    
    if 'DEPRECATED' not in content:
        # Add deprecation notice at the top
        new_content = deprecation_notice + content
        
        # Write fixed content
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ websocket_manager.py fixed - deprecated notice added")
        return True
    else:
        print("✅ websocket_manager.py already fixed")
        return True

def verify_working_files():
    """Verify that working files are present and functional"""
    print("🔍 Verifying working files...")
    
    working_files = {
        'webhook_handlers.py': 'Webhook processing',
        'flask_socketio_manager.py': 'WebSocket functionality',
        'tracking_system.py': 'Tracking system integration'
    }
    
    all_present = True
    
    for file_path, description in working_files.items():
        if os.path.exists(file_path):
            print(f"✅ {file_path} - {description}")
        else:
            print(f"❌ {file_path} - {description} - MISSING!")
            all_present = False
    
    return all_present

def create_import_test():
    """Create a test script to verify imports work"""
    print("🧪 Creating import test...")
    
    test_content = '''#!/usr/bin/env python3
"""
Import Test for WebSocket and Webhook Files
==========================================

Tests that all working files can be imported without errors.
"""

def test_imports():
    """Test importing all working files"""
    results = {}
    
    # Test webhook_handlers
    try:
        import webhook_handlers
        results['webhook_handlers'] = "✅ SUCCESS"
    except Exception as e:
        results['webhook_handlers'] = f"❌ ERROR: {e}"
    
    # Test flask_socketio_manager
    try:
        import flask_socketio_manager
        results['flask_socketio_manager'] = "✅ SUCCESS"
    except Exception as e:
        results['flask_socketio_manager'] = f"❌ ERROR: {e}"
    
    # Test tracking_system
    try:
        import tracking_system
        results['tracking_system'] = "✅ SUCCESS"
    except Exception as e:
        results['tracking_system'] = f"❌ ERROR: {e}"
    
    # Test deprecated files (should not be imported)
    print("\\n🔍 Testing Deprecated Files (should not be imported):")
    
    try:
        import websocket_routes
        results['websocket_routes'] = "⚠️  IMPORTED (should be deprecated)"
    except Exception as e:
        results['websocket_routes'] = f"✅ BLOCKED: {e}"
    
    try:
        import websocket_manager
        results['websocket_manager'] = "⚠️  IMPORTED (should be deprecated)"
    except Exception as e:
        results['websocket_manager'] = f"✅ BLOCKED: {e}"
    
    return results

def main():
    """Main test function"""
    print("🧪 WebSocket and Webhook Import Test")
    print("=" * 50)
    
    results = test_imports()
    
    print("\\n📊 Import Test Results:")
    print("=" * 30)
    
    for module, result in results.items():
        print(f"{module}: {result}")
    
    # Count successes
    successes = sum(1 for result in results.values() if "✅ SUCCESS" in result)
    total_working = 3  # webhook_handlers, flask_socketio_manager, tracking_system
    
    print(f"\\n📈 Working Files: {successes}/{total_working}")
    
    if successes == total_working:
        print("🎉 All working files import successfully!")
    else:
        print("⚠️  Some working files have import issues")

if __name__ == '__main__':
    main()
'''
    
    with open('test_imports.py', 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    print("✅ Import test script created: test_imports.py")

def main():
    """Main fix function"""
    print("🔧 WebSocket Error Fix Script")
    print("=" * 40)
    
    fixes_applied = []
    
    # Fix websocket_routes.py
    if fix_websocket_routes():
        fixes_applied.append("websocket_routes.py")
    
    # Fix websocket_manager.py
    if fix_websocket_manager():
        fixes_applied.append("websocket_manager.py")
    
    # Verify working files
    working_files_ok = verify_working_files()
    
    # Create import test
    create_import_test()
    
    # Summary
    print("\\n" + "=" * 40)
    print("📊 Fix Summary")
    print("=" * 40)
    
    print(f"✅ Files Fixed: {len(fixes_applied)}")
    for file in fixes_applied:
        print(f"   - {file}")
    
    print(f"✅ Working Files: {'OK' if working_files_ok else 'ISSUES'}")
    print("✅ Import Test: Created")
    
    if len(fixes_applied) > 0 and working_files_ok:
        print("\\n🎉 All WebSocket errors fixed successfully!")
        print("\\n📋 Next Steps:")
        print("1. Run: python test_imports.py")
        print("2. Use flask_socketio_manager.py for WebSocket functionality")
        print("3. Use webhook_handlers.py for webhook processing")
        print("4. Ignore deprecated websocket_*.py files")
    else:
        print("\\n⚠️  Some issues remain - check file status above")
    
    return len(fixes_applied) > 0 and working_files_ok

if __name__ == '__main__':
    main()
