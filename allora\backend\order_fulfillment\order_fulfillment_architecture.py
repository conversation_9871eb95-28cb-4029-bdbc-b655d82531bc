"""
Order Fulfillment System Architecture
====================================

Comprehensive architecture for order fulfillment system with shipping carrier
integrations, tracking APIs, and automated fulfillment workflows.

Key Components:
1. Shipping Carrier Integration
2. Order Fulfillment Engine
3. Tracking and Notification System
4. Fulfillment APIs and Dashboard
5. Customer Tracking Interface

Database Models:
- ShippingCarrier
- Shipment
- TrackingEvent
- FulfillmentRule
- CarrierRate
- ShippingLabel
"""

from enum import Enum
from dataclasses import dataclass
from typing import List, Dict, Optional, Any
from datetime import datetime, date
import json

# ============================================================================
# ENUMS AND CONSTANTS
# ============================================================================

class ShippingCarrier(Enum):
    """Supported shipping carriers"""
    FEDEX = "fedex"
    UPS = "ups"
    DHL = "dhl"
    USPS = "usps"
    INDIA_POST = "india_post"
    SHIPROCKET = "shiprocket"
    DELHIVERY = "delhivery"
    EKART = "ekart"
    DTDC = "dtdc"
    ARAMEX = "aramex"

class ShipmentStatus(Enum):
    """Shipment status values"""
    PENDING = "pending"
    LABEL_CREATED = "label_created"
    PICKED_UP = "picked_up"
    IN_TRANSIT = "in_transit"
    OUT_FOR_DELIVERY = "out_for_delivery"
    DELIVERED = "delivered"
    EXCEPTION = "exception"
    RETURNED = "returned"
    CANCELLED = "cancelled"

class TrackingEventType(Enum):
    """Types of tracking events"""
    LABEL_CREATED = "label_created"
    PICKUP_SCHEDULED = "pickup_scheduled"
    PICKED_UP = "picked_up"
    DEPARTED_FACILITY = "departed_facility"
    ARRIVED_FACILITY = "arrived_facility"
    IN_TRANSIT = "in_transit"
    OUT_FOR_DELIVERY = "out_for_delivery"
    DELIVERY_ATTEMPTED = "delivery_attempted"
    DELIVERED = "delivered"
    EXCEPTION = "exception"
    RETURNED = "returned"
    CANCELLED = "cancelled"

class FulfillmentPriority(Enum):
    """Order fulfillment priority levels"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"
    EXPRESS = "express"

class PackageType(Enum):
    """Package types for shipping"""
    ENVELOPE = "envelope"
    SMALL_BOX = "small_box"
    MEDIUM_BOX = "medium_box"
    LARGE_BOX = "large_box"
    EXTRA_LARGE_BOX = "extra_large_box"
    TUBE = "tube"
    PAK = "pak"
    CUSTOM = "custom"

# ============================================================================
# DATA STRUCTURES
# ============================================================================

@dataclass
class Address:
    """Standardized address structure"""
    name: str
    company: Optional[str]
    address_line_1: str
    address_line_2: Optional[str]
    city: str
    state: str
    postal_code: str
    country: str
    phone: Optional[str]
    email: Optional[str]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'name': self.name,
            'company': self.company,
            'address_line_1': self.address_line_1,
            'address_line_2': self.address_line_2,
            'city': self.city,
            'state': self.state,
            'postal_code': self.postal_code,
            'country': self.country,
            'phone': self.phone,
            'email': self.email
        }

@dataclass
class Package:
    """Package information for shipping"""
    weight: float  # in kg
    length: float  # in cm
    width: float   # in cm
    height: float  # in cm
    package_type: PackageType
    declared_value: float  # in INR
    description: str
    
    def get_volume(self) -> float:
        """Calculate package volume in cubic cm"""
        return self.length * self.width * self.height
    
    def get_dimensional_weight(self) -> float:
        """Calculate dimensional weight (volume/5000 for most carriers)"""
        return self.get_volume() / 5000

@dataclass
class ShippingRate:
    """Shipping rate information"""
    carrier: ShippingCarrier
    service_type: str
    rate: float  # in INR
    currency: str
    estimated_days: int
    guaranteed: bool
    pickup_required: bool
    signature_required: bool
    insurance_included: bool
    tracking_included: bool
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'carrier': self.carrier.value,
            'service_type': self.service_type,
            'rate': self.rate,
            'currency': self.currency,
            'estimated_days': self.estimated_days,
            'guaranteed': self.guaranteed,
            'pickup_required': self.pickup_required,
            'signature_required': self.signature_required,
            'insurance_included': self.insurance_included,
            'tracking_included': self.tracking_included
        }

@dataclass
class TrackingEvent:
    """Tracking event information"""
    event_type: TrackingEventType
    status: str
    description: str
    location: Optional[str]
    timestamp: datetime
    carrier_code: Optional[str]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'event_type': self.event_type.value,
            'status': self.status,
            'description': self.description,
            'location': self.location,
            'timestamp': self.timestamp.isoformat(),
            'carrier_code': self.carrier_code
        }

@dataclass
class FulfillmentRequest:
    """Order fulfillment request"""
    order_id: int
    order_items: List[Dict[str, Any]]
    shipping_address: Address
    billing_address: Optional[Address]
    shipping_method: str
    priority: FulfillmentPriority
    special_instructions: Optional[str]
    insurance_required: bool
    signature_required: bool
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'order_id': self.order_id,
            'order_items': self.order_items,
            'shipping_address': self.shipping_address.to_dict(),
            'billing_address': self.billing_address.to_dict() if self.billing_address else None,
            'shipping_method': self.shipping_method,
            'priority': self.priority.value,
            'special_instructions': self.special_instructions,
            'insurance_required': self.insurance_required,
            'signature_required': self.signature_required
        }

# ============================================================================
# SYSTEM CONFIGURATION
# ============================================================================

class FulfillmentConfig:
    """System configuration for order fulfillment"""
    
    # Carrier API Endpoints
    CARRIER_ENDPOINTS = {
        ShippingCarrier.FEDEX: {
            'base_url': 'https://apis.fedex.com',
            'rate_endpoint': '/rate/v1/rates/quotes',
            'ship_endpoint': '/ship/v1/shipments',
            'track_endpoint': '/track/v1/trackingnumbers',
            'pickup_endpoint': '/pickup/v1/pickups'
        },
        ShippingCarrier.UPS: {
            'base_url': 'https://onlinetools.ups.com/api',
            'rate_endpoint': '/rating/v1/rate',
            'ship_endpoint': '/shipments/v1/ship',
            'track_endpoint': '/track/v1/details',
            'pickup_endpoint': '/pickup/v1/pickups'
        },
        ShippingCarrier.DHL: {
            'base_url': 'https://express.api.dhl.com',
            'rate_endpoint': '/mydhlapi/rates',
            'ship_endpoint': '/mydhlapi/shipments',
            'track_endpoint': '/mydhlapi/shipments/track',
            'pickup_endpoint': '/mydhlapi/pickups'
        },
        ShippingCarrier.SHIPROCKET: {
            'base_url': 'https://apiv2.shiprocket.in/v1',
            'staging_url': 'https://staging-apiv2.shiprocket.in/v1',
            'auth_endpoint': '/external/auth/login',
            'rate_endpoint': '/external/courier/serviceability/',
            'ship_endpoint': '/external/orders/create/adhoc',
            'track_endpoint': '/external/courier/track/awb/',
            'pickup_endpoint': '/external/courier/generate/pickup',
            'awb_endpoint': '/external/courier/assign/awb',
            'cancel_endpoint': '/external/orders/cancel'
        },
        ShippingCarrier.DELHIVERY: {
            'base_url': 'https://track.delhivery.com/api',
            'rate_endpoint': '/kinko/v1/invoice/charges',
            'ship_endpoint': '/cmu/create.json',
            'track_endpoint': '/v1/packages/json',
            'pickup_endpoint': '/fm/request/new'
        }
    }
    
    # Default shipping rules
    DEFAULT_FULFILLMENT_RULES = {
        'auto_fulfill_threshold': 1000.0,  # Auto-fulfill orders below this amount
        'require_approval_threshold': 10000.0,  # Require approval above this amount
        'default_carrier': ShippingCarrier.SHIPROCKET,
        'backup_carrier': ShippingCarrier.DELHIVERY,
        'max_package_weight': 30.0,  # kg
        'max_package_dimensions': {'length': 120, 'width': 80, 'height': 80},  # cm
        'insurance_threshold': 5000.0,  # Auto-insure orders above this amount
        'signature_threshold': 2000.0,  # Require signature above this amount
    }

    # Notification settings
    NOTIFICATION_SETTINGS = {
        'send_shipping_confirmation': True,
        'send_tracking_updates': True,
        'send_delivery_confirmation': True,
        'send_exception_alerts': True,
        'email_templates': {
            'shipping_confirmation': 'shipping_confirmation.html',
            'tracking_update': 'tracking_update.html',
            'delivery_confirmation': 'delivery_confirmation.html',
            'exception_alert': 'exception_alert.html'
        }
    }

    # Webhook endpoints for carrier updates
    WEBHOOK_ENDPOINTS = {
        'fedex': '/api/webhooks/fedex/tracking',
        'ups': '/api/webhooks/ups/tracking',
        'dhl': '/api/webhooks/dhl/tracking',
        'blue_dart': '/api/webhooks/bluedart/tracking',
        'delhivery': '/api/webhooks/delhivery/tracking'
    }

# ============================================================================
# API SPECIFICATIONS
# ============================================================================

class FulfillmentAPISpec:
    """API endpoint specifications for fulfillment system"""

    ENDPOINTS = {
        # Order Fulfillment
        'create_fulfillment': {
            'method': 'POST',
            'path': '/api/fulfillment/orders',
            'description': 'Create new order fulfillment request'
        },
        'get_fulfillment': {
            'method': 'GET',
            'path': '/api/fulfillment/orders/{order_id}',
            'description': 'Get order fulfillment details'
        },
        'update_fulfillment': {
            'method': 'PUT',
            'path': '/api/fulfillment/orders/{order_id}',
            'description': 'Update order fulfillment status'
        },
        'cancel_fulfillment': {
            'method': 'DELETE',
            'path': '/api/fulfillment/orders/{order_id}',
            'description': 'Cancel order fulfillment'
        },

        # Shipping Rates
        'get_shipping_rates': {
            'method': 'POST',
            'path': '/api/fulfillment/rates',
            'description': 'Get shipping rates from multiple carriers'
        },
        'compare_rates': {
            'method': 'POST',
            'path': '/api/fulfillment/rates/compare',
            'description': 'Compare rates across carriers'
        },

        # Shipment Management
        'create_shipment': {
            'method': 'POST',
            'path': '/api/fulfillment/shipments',
            'description': 'Create new shipment and generate label'
        },
        'get_shipment': {
            'method': 'GET',
            'path': '/api/fulfillment/shipments/{shipment_id}',
            'description': 'Get shipment details'
        },
        'track_shipment': {
            'method': 'GET',
            'path': '/api/fulfillment/shipments/{shipment_id}/track',
            'description': 'Get shipment tracking information'
        },
        'cancel_shipment': {
            'method': 'DELETE',
            'path': '/api/fulfillment/shipments/{shipment_id}',
            'description': 'Cancel shipment'
        },

        # Tracking
        'track_by_number': {
            'method': 'GET',
            'path': '/api/fulfillment/track/{tracking_number}',
            'description': 'Track shipment by tracking number'
        },
        'bulk_track': {
            'method': 'POST',
            'path': '/api/fulfillment/track/bulk',
            'description': 'Track multiple shipments'
        },

        # Carrier Management
        'get_carriers': {
            'method': 'GET',
            'path': '/api/fulfillment/carriers',
            'description': 'Get available shipping carriers'
        },
        'get_carrier_services': {
            'method': 'GET',
            'path': '/api/fulfillment/carriers/{carrier}/services',
            'description': 'Get services for specific carrier'
        },

        # Webhooks
        'carrier_webhook': {
            'method': 'POST',
            'path': '/api/fulfillment/webhooks/{carrier}',
            'description': 'Handle carrier webhook updates'
        },

        # Analytics
        'fulfillment_analytics': {
            'method': 'GET',
            'path': '/api/fulfillment/analytics',
            'description': 'Get fulfillment analytics and reports'
        },
        'carrier_performance': {
            'method': 'GET',
            'path': '/api/fulfillment/analytics/carriers',
            'description': 'Get carrier performance metrics'
        }
    }

# ============================================================================
# ERROR HANDLING
# ============================================================================

class FulfillmentError(Exception):
    """Base exception for fulfillment system"""
    pass

class CarrierAPIError(FulfillmentError):
    """Carrier API related errors"""
    pass

class RateCalculationError(FulfillmentError):
    """Rate calculation errors"""
    pass

class ShipmentCreationError(FulfillmentError):
    """Shipment creation errors"""
    pass

class TrackingError(FulfillmentError):
    """Tracking related errors"""
    pass

# ============================================================================
# SYSTEM METRICS
# ============================================================================

class FulfillmentMetrics:
    """Key metrics for fulfillment system monitoring"""

    PERFORMANCE_METRICS = [
        'order_processing_time',
        'label_generation_time',
        'pickup_scheduling_time',
        'delivery_time',
        'fulfillment_accuracy',
        'carrier_performance',
        'cost_efficiency',
        'customer_satisfaction'
    ]

    BUSINESS_METRICS = [
        'total_orders_fulfilled',
        'fulfillment_cost',
        'shipping_revenue',
        'delivery_success_rate',
        'return_rate',
        'damage_rate',
        'customer_complaints',
        'carrier_disputes'
    ]

    OPERATIONAL_METRICS = [
        'api_response_time',
        'webhook_processing_time',
        'tracking_update_frequency',
        'system_uptime',
        'error_rate',
        'retry_rate',
        'queue_processing_time',
        'notification_delivery_rate'
    ]
