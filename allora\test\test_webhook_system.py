#!/usr/bin/env python3
"""
Webhook System Test Suite
=========================

Comprehensive testing for the webhook functionality in the Allora project.
Tests all webhook endpoints, security, and integration.

Author: Allora Development Team
Date: 2025-07-13
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json
import hmac
import hashlib
from datetime import datetime

def test_webhook_system_structure():
    """Test the webhook system module structure"""
    print("🔍 Testing Webhook System Structure")
    print("=" * 45)
    
    try:
        # Import webhook handlers module
        import webhook_handlers
        
        print("✅ Webhook handlers module imported successfully")
        
        # Test core classes
        required_classes = [
            'WebhookCarrier', 'WebhookConfig', 'WebhookSecurity', 
            'WebhookEventProcessor'
        ]
        
        for class_name in required_classes:
            if hasattr(webhook_handlers, class_name):
                print(f"   ✅ {class_name}: Found")
            else:
                print(f"   ❌ {class_name}: Missing")
        
        # Test configurations
        if hasattr(webhook_handlers, 'WEBHOOK_CONFIGS'):
            configs = webhook_handlers.WEBHOOK_CONFIGS
            print(f"   ✅ WEBHOOK_CONFIGS: {len(configs)} carriers configured")
            for carrier in configs:
                print(f"      • {carrier.value}: Configured")
        else:
            print("   ❌ WEBHOOK_CONFIGS: Missing")
        
        # Test blueprint
        if hasattr(webhook_handlers, 'webhook_bp'):
            print("   ✅ Webhook blueprint: Found")
        else:
            print("   ❌ Webhook blueprint: Missing")
        
        return True
        
    except ImportError as e:
        print(f"❌ Webhook handlers import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Webhook system structure test error: {e}")
        return False

def test_webhook_security():
    """Test webhook security functionality"""
    print("\n🔒 Testing Webhook Security")
    print("=" * 35)
    
    try:
        from webhook_handlers import WebhookSecurity
        
        print("✅ WebhookSecurity class imported")
        
        # Test signature generation and verification
        test_payload = b'{"test": "data"}'
        test_secret = "test_secret_key"
        
        # Test HMAC SHA256 signature
        signature = WebhookSecurity.generate_signature(test_payload, test_secret, "hmac_sha256")
        print(f"   ✅ Signature generation: {signature[:20]}...")
        
        # Test signature verification
        is_valid = WebhookSecurity.verify_signature(test_payload, signature, test_secret, "hmac_sha256")
        print(f"   ✅ Signature verification: {is_valid}")
        
        # Test invalid signature
        invalid_signature = "invalid_signature"
        is_invalid = WebhookSecurity.verify_signature(test_payload, invalid_signature, test_secret, "hmac_sha256")
        print(f"   ✅ Invalid signature rejection: {not is_invalid}")
        
        # Test different signature methods
        methods = ["hmac_sha256", "hmac_sha1"]
        for method in methods:
            try:
                sig = WebhookSecurity.generate_signature(test_payload, test_secret, method)
                print(f"   ✅ {method}: Supported")
            except Exception as e:
                print(f"   ❌ {method}: Error - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Webhook security test error: {e}")
        return False

def test_webhook_endpoints():
    """Test webhook endpoints"""
    print("\n🌐 Testing Webhook Endpoints")
    print("=" * 35)
    
    base_url = "http://127.0.0.1:5000"
    
    # Test endpoints for each carrier
    carriers = [
        {
            'name': 'Blue Dart',
            'url': f"{base_url}/api/webhooks/blue-dart",
            'signature_header': 'X-BlueDart-Signature',
            'test_payload': {
                'tracking_number': 'BD123456789',
                'status': 'in_transit',
                'event_type': 'status_update',
                'timestamp': '2025-07-13 20:00:00',
                'location': 'Mumbai Hub'
            }
        },
        {
            'name': 'Delhivery',
            'url': f"{base_url}/api/webhooks/delhivery",
            'signature_header': 'X-Delhivery-Signature',
            'test_payload': {
                'waybill': 'DL987654321',
                'status': 'out_for_delivery',
                'event': 'status_change',
                'updated_at': '2025-07-13 20:00:00',
                'location': 'Delhi Hub'
            }
        },
        {
            'name': 'FedEx',
            'url': f"{base_url}/api/webhooks/fedex",
            'signature_header': 'X-FedEx-Signature',
            'test_payload': {
                'trackingNumber': 'FX555666777',
                'statusDescription': 'delivered',
                'eventType': 'delivery',
                'eventDateTime': '2025-07-13T20:00:00',
                'location': 'Bangalore Hub'
            }
        },
        {
            'name': 'Test Webhook',
            'url': f"{base_url}/api/webhooks/test",
            'signature_header': None,
            'test_payload': {
                'carrier': 'blue_dart',
                'tracking_number': 'TEST123456',
                'status': 'delivered',
                'timestamp': '2025-07-13 20:00:00'
            }
        }
    ]
    
    for carrier in carriers:
        print(f"🔍 Testing: {carrier['name']}")
        print(f"   URL: {carrier['url']}")
        
        try:
            payload = json.dumps(carrier['test_payload'])
            headers = {'Content-Type': 'application/json'}
            
            # Add signature header if required
            if carrier['signature_header']:
                # Generate test signature (this will fail verification but tests endpoint accessibility)
                test_signature = "test_signature_for_endpoint_testing"
                headers[carrier['signature_header']] = test_signature
            
            response = requests.post(carrier['url'], data=payload, headers=headers, timeout=10)
            print(f"   ✅ Status Code: {response.status_code}")
            
            if response.status_code in [200, 400, 401]:  # Expected responses
                try:
                    data = response.json()
                    print(f"   ✅ Response Format: Valid JSON")
                    if 'error' in data:
                        print(f"   ⚠️  Response: {data['error']}")
                    elif 'success' in data:
                        print(f"   ✅ Success: {data['success']}")
                except:
                    print(f"   ⚠️  Response: Non-JSON content")
            else:
                print(f"   ⚠️  Unexpected status: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print(f"   ❌ Connection Error: Server not running at {base_url}")
        except requests.exceptions.Timeout:
            print(f"   ❌ Timeout Error: Request took too long")
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        print()

def test_webhook_integration():
    """Test webhook integration with main Flask app"""
    print("\n🔗 Testing Webhook Integration")
    print("=" * 40)
    
    try:
        # Import Flask app
        from app import app
        
        print("✅ Flask app imported successfully")
        
        # Test webhook blueprint registration
        blueprints = [bp.name for bp in app.blueprints.values()]
        if 'webhooks' in blueprints:
            print("   ✅ Webhooks blueprint: Registered")
        else:
            print("   ❌ Webhooks blueprint: Not registered")
        
        # Test webhook routes
        webhook_routes = []
        for rule in app.url_map.iter_rules():
            if '/api/webhooks' in rule.rule:
                webhook_routes.append(f"{rule.rule} [{', '.join(rule.methods)}]")
        
        if webhook_routes:
            print("   ✅ Webhook routes registered:")
            for route in webhook_routes:
                print(f"      • {route}")
        else:
            print("   ❌ No webhook routes found")
        
        # Test database models
        with app.app_context():
            from app import TrackingEvent
            print("   ✅ TrackingEvent model: Available for webhook data storage")
            
            # Check webhook-specific fields
            webhook_fields = ['webhook_id', 'raw_data']
            for field in webhook_fields:
                if hasattr(TrackingEvent, field):
                    print(f"   ✅ TrackingEvent.{field}: Found")
                else:
                    print(f"   ❌ TrackingEvent.{field}: Missing")
        
        return True
        
    except Exception as e:
        print(f"❌ Webhook integration test error: {e}")
        return False

def test_fulfillment_webhooks():
    """Test fulfillment-related webhook endpoints"""
    print("\n📦 Testing Fulfillment Webhooks")
    print("=" * 40)
    
    base_url = "http://127.0.0.1:5000"
    
    # Test fulfillment webhook endpoints
    fulfillment_webhooks = [
        {
            'name': 'Blue Dart Tracking Webhook',
            'url': f"{base_url}/api/fulfillment/webhooks/blue_dart/tracking",
            'test_payload': {
                'tracking_number': 'BD123456789',
                'status': 'delivered',
                'location': 'Mumbai',
                'timestamp': '2025-07-13 20:00:00'
            }
        },
        {
            'name': 'Inventory Webhook',
            'url': f"{base_url}/api/webhooks/inventory/1",
            'test_payload': {
                'product_id': 'PROD123',
                'quantity': 50,
                'location': 'warehouse_1'
            }
        }
    ]
    
    for webhook in fulfillment_webhooks:
        print(f"🔍 Testing: {webhook['name']}")
        print(f"   URL: {webhook['url']}")
        
        try:
            response = requests.post(
                webhook['url'], 
                json=webhook['test_payload'], 
                timeout=10
            )
            print(f"   ✅ Status Code: {response.status_code}")
            
            if response.status_code in [200, 400, 404]:  # Expected responses
                try:
                    data = response.json()
                    print(f"   ✅ Response Format: Valid JSON")
                    if 'error' in data:
                        print(f"   ⚠️  Response: {data['error']}")
                    elif 'success' in data:
                        print(f"   ✅ Success: {data['success']}")
                except:
                    print(f"   ⚠️  Response: Non-JSON content")
            else:
                print(f"   ⚠️  Status: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print(f"   ❌ Connection Error: Server not running")
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        print()

def analyze_webhook_system():
    """Analyze the webhook system's purpose and functionality"""
    print("\n📋 Webhook System Analysis")
    print("=" * 35)
    
    print("🎯 PRIMARY PURPOSE:")
    print("   The Webhook System provides real-time event processing")
    print("   for external carrier notifications and system integrations.")
    print()
    
    print("🔧 KEY COMPONENTS:")
    print("   1. Carrier Webhooks (webhook_handlers.py)")
    print("      • Blue Dart webhook processing")
    print("      • Delhivery webhook processing") 
    print("      • FedEx webhook processing")
    print("      • Secure signature verification")
    print("      • Event deduplication")
    print()
    
    print("   2. Fulfillment Webhooks (fulfillment_api.py)")
    print("      • Tracking update webhooks")
    print("      • Carrier-specific data parsing")
    print("      • Database integration")
    print("      • Status synchronization")
    print()
    
    print("   3. Inventory Webhooks (app.py)")
    print("      • External channel inventory updates")
    print("      • Multi-channel synchronization")
    print("      • Real-time stock management")
    print()
    
    print("🔒 SECURITY FEATURES:")
    print("   • HMAC signature verification")
    print("   • Carrier-specific secret keys")
    print("   • Request validation and sanitization")
    print("   • Error handling and logging")
    print()
    
    print("🌐 SUPPORTED CARRIERS:")
    print("   • Blue Dart Express")
    print("   • Delhivery")
    print("   • FedEx")
    print("   • Test endpoint for development")
    print()
    
    print("📊 BUSINESS VALUE:")
    print("   • Real-time shipment status updates")
    print("   • Automated tracking synchronization")
    print("   • Reduced manual intervention")
    print("   • Improved customer experience")
    print("   • Multi-channel inventory management")
    print()

def run_all_tests():
    """Run all webhook system tests"""
    print("🚀 Webhook System Test Suite")
    print("=" * 40)
    print()
    
    tests = [
        test_webhook_system_structure,
        test_webhook_security,
        test_webhook_integration
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    # Analyze webhook system
    analyze_webhook_system()
    
    # Test webhook endpoints (requires running server)
    print("⚠️  Note: Endpoint testing requires the server to be running")
    print("   Start the server with: python run_with_waitress.py")
    print()
    
    try:
        test_webhook_endpoints()
        test_fulfillment_webhooks()
    except Exception as e:
        print(f"❌ Endpoint testing failed: {e}")
        print("   This is expected if the server is not running")
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 30)
    passed = sum(results)
    total = len(results)
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! Webhook system is properly integrated.")
    else:
        print("⚠️  Some tests failed. Please review the webhook system.")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
