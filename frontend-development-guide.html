<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Allora E-commerce Frontend Development Guide</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            margin: -20px -20px 40px -20px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .nav {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 30px;
            position: sticky;
            top: 20px;
            z-index: 100;
        }
        
        .nav ul {
            list-style: none;
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .nav a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            padding: 8px 15px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        
        .nav a:hover {
            background: #667eea;
            color: white;
        }
        
        .section {
            margin-bottom: 40px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid #667eea;
        }
        
        .section h2 {
            color: #667eea;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .section h3 {
            color: #764ba2;
            margin: 20px 0 15px 0;
            font-size: 1.3em;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            position: relative;
        }
        
        .code-block::before {
            content: attr(data-lang);
            position: absolute;
            top: 5px;
            right: 10px;
            background: #667eea;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 0.8em;
        }
        
        .api-endpoint {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin: 10px 0;
            overflow: hidden;
        }
        
        .api-header {
            background: #667eea;
            color: white;
            padding: 10px 15px;
            font-weight: bold;
        }
        
        .api-body {
            padding: 15px;
        }
        
        .method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 0.9em;
            margin-right: 10px;
        }
        
        .method.get { background: #48bb78; color: white; }
        .method.post { background: #4299e1; color: white; }
        .method.put { background: #ed8936; color: white; }
        .method.delete { background: #f56565; color: white; }
        
        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .tech-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .tech-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .tech-card h4 {
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        
        .feature-card h4 {
            color: #764ba2;
            margin-bottom: 10px;
        }
        
        .step {
            background: white;
            margin: 15px 0;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .step-number {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .alert.info {
            background: #e6f3ff;
            border-left: 4px solid #4299e1;
            color: #2b6cb0;
        }
        
        .alert.warning {
            background: #fffbeb;
            border-left: 4px solid #ed8936;
            color: #c05621;
        }
        
        .alert.success {
            background: #f0fff4;
            border-left: 4px solid #48bb78;
            color: #2f855a;
        }
        
        @media (max-width: 768px) {
            .nav ul {
                flex-direction: column;
            }
            
            .tech-stack {
                grid-template-columns: 1fr;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Allora E-commerce Frontend Development Guide</h1>
            <p>Complete step-by-step guide to building a modern frontend for the Allora e-commerce platform</p>
        </div>
        
        <nav class="nav">
            <ul>
                <li><a href="#overview">Overview</a></li>
                <li><a href="#architecture">Architecture</a></li>
                <li><a href="#tech-stack">Tech Stack</a></li>
                <li><a href="#setup">Setup</a></li>
                <li><a href="#api-endpoints">API Endpoints</a></li>
                <li><a href="#database">Database</a></li>
                <li><a href="#features">Features</a></li>
                <li><a href="#implementation">Implementation</a></li>
                <li><a href="#deployment">Deployment</a></li>
            </ul>
        </nav>
        
        <section id="overview" class="section">
            <h2>📋 System Overview</h2>
            <p>The Allora e-commerce platform is a comprehensive, enterprise-grade solution built with Flask (Python) backend featuring:</p>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🛍️ Core E-commerce</h4>
                    <p>Product catalog, shopping cart, order management, payment processing, and user accounts</p>
                </div>
                <div class="feature-card">
                    <h4>🚚 Order Fulfillment</h4>
                    <p>Advanced shipping integration with multiple carriers, tracking, and automated fulfillment</p>
                </div>
                <div class="feature-card">
                    <h4>🔄 RMA System</h4>
                    <p>Complete Return Merchandise Authorization system with approval workflows</p>
                </div>
                <div class="feature-card">
                    <h4>👥 Community Features</h4>
                    <p>Social commerce with posts, comments, likes, hashtags, and community insights</p>
                </div>
                <div class="feature-card">
                    <h4>🔍 Advanced Search</h4>
                    <p>Elasticsearch-powered search with analytics, visual search, and AI recommendations</p>
                </div>
                <div class="feature-card">
                    <h4>🌱 Sustainability</h4>
                    <p>Environmental impact tracking, CO2 savings, and green initiatives dashboard</p>
                </div>
                <div class="feature-card">
                    <h4>📊 Analytics & ML</h4>
                    <p>Advanced analytics, inventory prediction, price trends, and recommendation engine</p>
                </div>
                <div class="feature-card">
                    <h4>🏪 Multi-vendor</h4>
                    <p>Seller management, commission tracking, and multi-store functionality</p>
                </div>
            </div>
        </section>
        
        <section id="architecture" class="section">
            <h2>🏗️ System Architecture</h2>
            
            <div class="alert info">
                <strong>Backend Architecture:</strong> The system uses a modular Flask architecture with blueprints for different features, SQLAlchemy for ORM, Redis for caching, and Elasticsearch for search.
            </div>
            
            <h3>Backend Components</h3>
            <ul>
                <li><strong>Flask Application:</strong> Main app.py with 15,000+ lines of comprehensive functionality</li>
                <li><strong>Database:</strong> PostgreSQL with 60+ models covering all business domains</li>
                <li><strong>Authentication:</strong> JWT-based auth with OAuth support (Google, etc.)</li>
                <li><strong>Real-time:</strong> WebSocket support for live updates</li>
                <li><strong>Search:</strong> Elasticsearch integration for advanced search capabilities</li>
                <li><strong>ML Models:</strong> TensorFlow-based recommendation and prediction systems</li>
                <li><strong>External APIs:</strong> Shiprocket for shipping, payment gateways</li>
            </ul>
            
            <h3>Recommended Frontend Architecture</h3>
            <div class="code-block" data-lang="text">
frontend/
├── src/
│   ├── components/          # Reusable UI components
│   ├── pages/              # Page components
│   ├── services/           # API service layer
│   ├── store/              # State management
│   ├── hooks/              # Custom React hooks
│   ├── utils/              # Utility functions
│   ├── types/              # TypeScript type definitions
│   └── assets/             # Static assets
├── public/                 # Public assets
└── tests/                  # Test files
            </div>
        </section>

        <section id="tech-stack" class="section">
            <h2>🛠️ Recommended Technology Stack</h2>

            <div class="tech-stack">
                <div class="tech-card">
                    <h4>⚛️ React 18+</h4>
                    <p>Modern React with hooks, concurrent features, and TypeScript support</p>
                </div>
                <div class="tech-card">
                    <h4>📘 TypeScript</h4>
                    <p>Type safety and better developer experience</p>
                </div>
                <div class="tech-card">
                    <h4>🎨 Tailwind CSS</h4>
                    <p>Utility-first CSS framework for rapid UI development</p>
                </div>
                <div class="tech-card">
                    <h4>🗃️ Redux Toolkit</h4>
                    <p>State management for complex application state</p>
                </div>
                <div class="tech-card">
                    <h4>🔄 React Query</h4>
                    <p>Server state management and caching</p>
                </div>
                <div class="tech-card">
                    <h4>🚦 React Router</h4>
                    <p>Client-side routing and navigation</p>
                </div>
                <div class="tech-card">
                    <h4>📡 Axios</h4>
                    <p>HTTP client for API communication</p>
                </div>
                <div class="tech-card">
                    <h4>🔌 Socket.IO</h4>
                    <p>Real-time communication with WebSocket support</p>
                </div>
                <div class="tech-card">
                    <h4>📋 React Hook Form</h4>
                    <p>Performant forms with easy validation</p>
                </div>
                <div class="tech-card">
                    <h4>🎭 Framer Motion</h4>
                    <p>Animation library for smooth interactions</p>
                </div>
                <div class="tech-card">
                    <h4>📊 Chart.js</h4>
                    <p>Data visualization for analytics dashboards</p>
                </div>
                <div class="tech-card">
                    <h4>⚡ Vite</h4>
                    <p>Fast build tool and development server</p>
                </div>
            </div>
        </section>

        <section id="setup" class="section">
            <h2>🚀 Project Setup</h2>

            <div class="step">
                <span class="step-number">1</span>
                <h3>Initialize React Project</h3>
                <div class="code-block" data-lang="bash">
npm create vite@latest allora-frontend -- --template react-ts
cd allora-frontend
npm install
                </div>
            </div>

            <div class="step">
                <span class="step-number">2</span>
                <h3>Install Dependencies</h3>
                <div class="code-block" data-lang="bash">
# Core dependencies
npm install @reduxjs/toolkit react-redux react-router-dom
npm install @tanstack/react-query axios
npm install react-hook-form @hookform/resolvers yup
npm install socket.io-client

# UI and styling
npm install tailwindcss @headlessui/react @heroicons/react
npm install framer-motion react-hot-toast
npm install chart.js react-chartjs-2

# Development dependencies
npm install -D @types/node @typescript-eslint/eslint-plugin
npm install -D @typescript-eslint/parser eslint-plugin-react-hooks
npm install -D prettier eslint-config-prettier
                </div>
            </div>

            <div class="step">
                <span class="step-number">3</span>
                <h3>Configure Tailwind CSS</h3>
                <div class="code-block" data-lang="bash">
npx tailwindcss init -p
                </div>
                <div class="code-block" data-lang="javascript">
// tailwind.config.js
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#f0f9ff',
          500: '#667eea',
          600: '#764ba2',
          700: '#553c9a',
        }
      }
    },
  },
  plugins: [],
}
                </div>
            </div>

            <div class="step">
                <span class="step-number">4</span>
                <h3>Environment Configuration</h3>
                <div class="code-block" data-lang="bash">
# .env.local
VITE_API_BASE_URL=http://localhost:5000
VITE_WEBSOCKET_URL=ws://localhost:5000
VITE_APP_NAME=Allora E-commerce
VITE_GOOGLE_CLIENT_ID=your_google_client_id
VITE_STRIPE_PUBLIC_KEY=your_stripe_public_key
                </div>
            </div>
        </section>

        <section id="api-endpoints" class="section">
            <h2>🔗 API Endpoints Reference</h2>

            <div class="alert info">
                <strong>Base URL:</strong> http://localhost:5000/api<br>
                <strong>Authentication:</strong> Bearer token in Authorization header
            </div>

            <h3>Authentication Endpoints</h3>
            <div class="api-endpoint">
                <div class="api-header">
                    <span class="method post">POST</span> /api/signup
                </div>
                <div class="api-body">
                    <strong>Description:</strong> User registration<br>
                    <strong>Body:</strong> { email, password, first_name, last_name }<br>
                    <strong>Response:</strong> { access_token, user }
                </div>
            </div>

            <div class="api-endpoint">
                <div class="api-header">
                    <span class="method post">POST</span> /api/login
                </div>
                <div class="api-body">
                    <strong>Description:</strong> User login<br>
                    <strong>Body:</strong> { email, password }<br>
                    <strong>Response:</strong> { access_token, user }
                </div>
            </div>

            <div class="api-endpoint">
                <div class="api-header">
                    <span class="method post">POST</span> /api/auth/refresh
                </div>
                <div class="api-body">
                    <strong>Description:</strong> Refresh access token<br>
                    <strong>Headers:</strong> Authorization: Bearer {refresh_token}<br>
                    <strong>Response:</strong> { access_token }
                </div>
            </div>

            <div class="api-endpoint">
                <div class="api-header">
                    <span class="method get">GET</span> /api/oauth/providers
                </div>
                <div class="api-body">
                    <strong>Description:</strong> Get available OAuth providers<br>
                    <strong>Response:</strong> { providers: ['google', 'facebook'] }
                </div>
            </div>

            <h3>Product Endpoints</h3>
            <div class="api-endpoint">
                <div class="api-header">
                    <span class="method get">GET</span> /api/products
                </div>
                <div class="api-body">
                    <strong>Description:</strong> Get products with pagination and filters<br>
                    <strong>Query Params:</strong> page, per_page, category, min_price, max_price, search<br>
                    <strong>Response:</strong> { products: [], total, page, per_page }
                </div>
            </div>

            <div class="api-endpoint">
                <div class="api-header">
                    <span class="method get">GET</span> /api/products/{id}
                </div>
                <div class="api-body">
                    <strong>Description:</strong> Get product details<br>
                    <strong>Response:</strong> { product, reviews, related_products }
                </div>
            </div>

            <div class="api-endpoint">
                <div class="api-header">
                    <span class="method get">GET</span> /api/categories
                </div>
                <div class="api-body">
                    <strong>Description:</strong> Get product categories<br>
                    <strong>Response:</strong> { categories: [] }
                </div>
            </div>

            <div class="api-endpoint">
                <div class="api-header">
                    <span class="method get">GET</span> /api/products/best-sellers
                </div>
                <div class="api-body">
                    <strong>Description:</strong> Get best-selling products<br>
                    <strong>Response:</strong> { products: [] }
                </div>
            </div>
        </section>

        <section id="database" class="section">
            <h2>🗄️ Database Models & Relationships</h2>

            <div class="alert info">
                <strong>Database:</strong> PostgreSQL with SQLAlchemy ORM<br>
                <strong>Total Models:</strong> 60+ comprehensive business models
            </div>

            <h3>Core Models</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>👤 User Model</h4>
                    <p><strong>Fields:</strong> id, email, password_hash, first_name, last_name, phone, is_active, created_at</p>
                    <p><strong>Relationships:</strong> addresses, orders, wishlist, cart_items, reviews</p>
                </div>
                <div class="feature-card">
                    <h4>🛍️ Product Model</h4>
                    <p><strong>Fields:</strong> id, name, description, price, category, stock_quantity, sku, images</p>
                    <p><strong>Relationships:</strong> reviews, variants, images, seller, order_items</p>
                </div>
                <div class="feature-card">
                    <h4>📦 Order Model</h4>
                    <p><strong>Fields:</strong> id, user_id, status, total_amount, shipping_address, created_at</p>
                    <p><strong>Relationships:</strong> order_items, user, shipments, payments</p>
                </div>
                <div class="feature-card">
                    <h4>🏪 Seller Model</h4>
                    <p><strong>Fields:</strong> id, business_name, email, phone, address, commission_rate</p>
                    <p><strong>Relationships:</strong> products, orders, payouts, store</p>
                </div>
            </div>

            <h3>Advanced Models</h3>
            <div class="code-block" data-lang="python">
# Community Models
CommunityPost, PostComment, PostLike, Hashtag, PostHashtag, CommunityStats

# RMA (Return) Models
RMARequest, RMAItem, RMATimeline, RMADocument, RMAApproval, RMARule

# Fulfillment Models
ShippingCarrier, Shipment, TrackingEvent, FulfillmentRule, CarrierRate

# Analytics Models
Sales, SearchAnalytics, VisualSearchAnalytics, UserInteractionLog

# Payment Models
PaymentGateway, PaymentTransaction, Invoice, Refund

# Support Models
SupportTicket, SupportMessage, SupportAttachment
            </div>

            <h3>Key Relationships</h3>
            <ul>
                <li><strong>User → Orders:</strong> One-to-many relationship</li>
                <li><strong>Order → OrderItems:</strong> One-to-many relationship</li>
                <li><strong>Product → ProductImages:</strong> One-to-many relationship</li>
                <li><strong>User → Wishlist:</strong> Many-to-many through Wishlist model</li>
                <li><strong>Product → Reviews:</strong> One-to-many relationship</li>
                <li><strong>Order → Shipments:</strong> One-to-many relationship</li>
                <li><strong>RMARequest → RMAItems:</strong> One-to-many relationship</li>
            </ul>
        </section>

        <section id="features" class="section">
            <h2>✨ Frontend Features to Implement</h2>

            <h3>🛒 E-commerce Core Features</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>Product Catalog</h4>
                    <ul>
                        <li>Product listing with filters</li>
                        <li>Product detail pages</li>
                        <li>Image galleries</li>
                        <li>Product variants</li>
                        <li>Reviews and ratings</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>Shopping Cart</h4>
                    <ul>
                        <li>Add/remove items</li>
                        <li>Quantity updates</li>
                        <li>Cart persistence</li>
                        <li>Guest cart support</li>
                        <li>Cart abandonment recovery</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>Checkout Process</h4>
                    <ul>
                        <li>Multi-step checkout</li>
                        <li>Address management</li>
                        <li>Payment integration</li>
                        <li>Order confirmation</li>
                        <li>Email notifications</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>User Account</h4>
                    <ul>
                        <li>Registration/login</li>
                        <li>Profile management</li>
                        <li>Order history</li>
                        <li>Address book</li>
                        <li>Wishlist</li>
                    </ul>
                </div>
            </div>

            <h3>🔍 Advanced Search Features</h3>
            <div class="code-block" data-lang="javascript">
// Search API Integration
const searchProducts = async (query, filters) => {
  const response = await axios.get('/api/search', {
    params: {
      q: query,
      category: filters.category,
      min_price: filters.minPrice,
      max_price: filters.maxPrice,
      sort_by: filters.sortBy,
      page: filters.page
    }
  });
  return response.data;
};

// Visual Search
const visualSearch = async (imageFile) => {
  const formData = new FormData();
  formData.append('image', imageFile);

  const response = await axios.post('/api/search/visual', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  });
  return response.data;
};
            </div>

            <h3>👥 Community Features</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>Social Commerce</h4>
                    <ul>
                        <li>Community posts</li>
                        <li>Comments and likes</li>
                        <li>Hashtag system</li>
                        <li>User-generated content</li>
                        <li>Product recommendations</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h4>Community Dashboard</h4>
                    <ul>
                        <li>Trending topics</li>
                        <li>Community stats</li>
                        <li>Top contributors</li>
                        <li>Recent highlights</li>
                        <li>Engagement metrics</li>
                    </ul>
                </div>
            </div>

            <h3>🚚 Order Fulfillment Features</h3>
            <div class="api-endpoint">
                <div class="api-header">
                    <span class="method get">GET</span> /api/fulfillment/track/{tracking_number}
                </div>
                <div class="api-body">
                    <strong>Description:</strong> Track shipment by tracking number<br>
                    <strong>Response:</strong> { tracking_events, current_status, estimated_delivery }
                </div>
            </div>

            <div class="api-endpoint">
                <div class="api-header">
                    <span class="method post">POST</span> /api/fulfillment/rates
                </div>
                <div class="api-body">
                    <strong>Description:</strong> Get shipping rates from multiple carriers<br>
                    <strong>Body:</strong> { origin, destination, packages }<br>
                    <strong>Response:</strong> { rates: [{ carrier, service, cost, delivery_time }] }
                </div>
            </div>
        </section>

        <section id="implementation" class="section">
            <h2>💻 Implementation Guide</h2>

            <h3>🔧 API Service Layer</h3>
            <div class="code-block" data-lang="typescript">
// src/services/api.ts
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

export const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for auth token
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('access_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor for token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Handle token refresh or redirect to login
      localStorage.removeItem('access_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);
            </div>

            <h3>🏪 Product Service</h3>
            <div class="code-block" data-lang="typescript">
// src/services/productService.ts
import { api } from './api';

export interface Product {
  id: number;
  name: string;
  description: string;
  price: number;
  category: string;
  stock_quantity: number;
  images: string[];
  rating: number;
  reviews_count: number;
}

export interface ProductFilters {
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  search?: string;
  page?: number;
  perPage?: number;
  sortBy?: 'price' | 'rating' | 'name' | 'created_at';
  sortOrder?: 'asc' | 'desc';
}

export const productService = {
  async getProducts(filters: ProductFilters = {}) {
    const response = await api.get('/products', { params: filters });
    return response.data;
  },

  async getProduct(id: number) {
    const response = await api.get(`/products/${id}`);
    return response.data;
  },

  async getCategories() {
    const response = await api.get('/categories');
    return response.data;
  },

  async getBestSellers() {
    const response = await api.get('/products/best-sellers');
    return response.data;
  },

  async getNewArrivals() {
    const response = await api.get('/products/new-arrivals');
    return response.data;
  },

  async searchProducts(query: string, filters: ProductFilters = {}) {
    const response = await api.get('/search', {
      params: { q: query, ...filters }
    });
    return response.data;
  }
};
            </div>

            <h3>🛒 Cart Management</h3>
            <div class="code-block" data-lang="typescript">
// src/store/cartSlice.ts
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface CartItem {
  id: number;
  product_id: number;
  name: string;
  price: number;
  quantity: number;
  image: string;
}

interface CartState {
  items: CartItem[];
  total: number;
  isLoading: boolean;
}

const initialState: CartState = {
  items: [],
  total: 0,
  isLoading: false,
};

const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    addToCart: (state, action: PayloadAction<CartItem>) => {
      const existingItem = state.items.find(
        item => item.product_id === action.payload.product_id
      );

      if (existingItem) {
        existingItem.quantity += action.payload.quantity;
      } else {
        state.items.push(action.payload);
      }

      state.total = state.items.reduce(
        (sum, item) => sum + (item.price * item.quantity), 0
      );
    },

    removeFromCart: (state, action: PayloadAction<number>) => {
      state.items = state.items.filter(
        item => item.product_id !== action.payload
      );
      state.total = state.items.reduce(
        (sum, item) => sum + (item.price * item.quantity), 0
      );
    },

    updateQuantity: (state, action: PayloadAction<{id: number, quantity: number}>) => {
      const item = state.items.find(
        item => item.product_id === action.payload.id
      );
      if (item) {
        item.quantity = action.payload.quantity;
        state.total = state.items.reduce(
          (sum, item) => sum + (item.price * item.quantity), 0
        );
      }
    },

    clearCart: (state) => {
      state.items = [];
      state.total = 0;
    }
  }
});

export const { addToCart, removeFromCart, updateQuantity, clearCart } = cartSlice.actions;
export default cartSlice.reducer;
            </div>
        </section>

        <section id="deployment" class="section">
            <h2>🚀 Deployment Guide</h2>

            <h3>📦 Build Configuration</h3>
            <div class="code-block" data-lang="json">
{
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "test": "vitest",
    "test:ui": "vitest --ui"
  }
}
            </div>

            <h3>🌐 Environment Setup</h3>
            <div class="step">
                <span class="step-number">1</span>
                <h3>Production Environment Variables</h3>
                <div class="code-block" data-lang="bash">
# .env.production
VITE_API_BASE_URL=https://api.allora.com
VITE_WEBSOCKET_URL=wss://api.allora.com
VITE_APP_NAME=Allora E-commerce
VITE_GOOGLE_CLIENT_ID=prod_google_client_id
VITE_STRIPE_PUBLIC_KEY=pk_live_stripe_key
                </div>
            </div>

            <h3>☁️ Deployment Options</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🔷 Vercel (Recommended)</h4>
                    <div class="code-block" data-lang="bash">
npm install -g vercel
vercel --prod
                    </div>
                </div>
                <div class="feature-card">
                    <h4>📦 Netlify</h4>
                    <div class="code-block" data-lang="bash">
npm run build
# Upload dist/ folder to Netlify
                    </div>
                </div>
                <div class="feature-card">
                    <h4>🐳 Docker</h4>
                    <div class="code-block" data-lang="dockerfile">
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "run", "preview"]
                    </div>
                </div>
            </div>

            <div class="alert success">
                <strong>🎉 Congratulations!</strong> You now have a comprehensive guide to build a modern frontend for the Allora e-commerce platform. The backend provides extensive APIs for all e-commerce functionality including products, orders, payments, shipping, returns, community features, and advanced analytics.
            </div>

            <div class="alert warning">
                <strong>⚠️ Important Notes:</strong>
                <ul>
                    <li>Ensure proper error handling for all API calls</li>
                    <li>Implement proper loading states and user feedback</li>
                    <li>Add comprehensive testing for critical user flows</li>
                    <li>Optimize for mobile responsiveness</li>
                    <li>Implement proper SEO optimization</li>
                    <li>Add analytics tracking for user behavior</li>
                </ul>
            </div>

            <h3>📚 Additional Resources</h3>
            <ul>
                <li><strong>Backend API:</strong> Located at <code>allora/backend/app.py</code> (15,000+ lines)</li>
                <li><strong>Database Models:</strong> 60+ models covering all business domains</li>
                <li><strong>WebSocket Support:</strong> Real-time features available</li>
                <li><strong>ML Models:</strong> Recommendation and prediction systems</li>
                <li><strong>Search System:</strong> Elasticsearch-powered advanced search</li>
                <li><strong>Order Fulfillment:</strong> Complete shipping and tracking system</li>
                <li><strong>RMA System:</strong> Return merchandise authorization</li>
                <li><strong>Community Features:</strong> Social commerce capabilities</li>
            </ul>
        </section>

        <footer style="text-align: center; padding: 40px 0; color: #666; border-top: 1px solid #e2e8f0; margin-top: 40px;">
            <p>🚀 <strong>Allora E-commerce Platform</strong> - Complete Frontend Development Guide</p>
            <p>Built with ❤️ for modern e-commerce development</p>
        </footer>
    </div>
</body>
</html>
