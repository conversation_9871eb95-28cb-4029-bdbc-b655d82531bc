#!/usr/bin/env python3
"""
Populate Elasticsearch with products from database
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from datetime import datetime
import json

def populate_products():
    """Populate Elasticsearch with products from database"""
    print("📦 POPULATING ELASTICSEARCH WITH PRODUCTS")
    print("="*80)
    
    try:
        from elasticsearch_manager import get_elasticsearch_sync_manager
        from app import Product
        
        with app.app_context():
            # Get sync manager
            sync_manager = get_elasticsearch_sync_manager()
            
            # Get all products from database
            products = Product.query.all()
            
            if products:
                print(f"📋 Found {len(products)} products in database")
                
                indexed_count = 0
                error_count = 0
                
                # Index products
                for product in products:
                    try:
                        success = sync_manager.sync_product_create(product.id)
                        if success:
                            print(f"✅ Indexed: {product.name} (ID: {product.id})")
                            indexed_count += 1
                        else:
                            print(f"❌ Failed to index {product.name}: Sync returned False")
                            error_count += 1
                    except Exception as e:
                        print(f"❌ Failed to index {product.name}: {e}")
                        error_count += 1
                
                print(f"\n📊 INDEXING SUMMARY:")
                print(f"✅ Successfully indexed: {indexed_count} products")
                print(f"❌ Failed to index: {error_count} products")
                print(f"📈 Success rate: {(indexed_count/(indexed_count+error_count))*100:.1f}%")
                
                return indexed_count > 0
            else:
                print("⚠️  No products found in database")
                return False
        
    except Exception as e:
        print(f"❌ Product indexing error: {e}")
        return False

def test_populated_search():
    """Test search functionality with populated data"""
    print("\n🔍 TESTING SEARCH WITH POPULATED DATA")
    print("="*80)
    
    try:
        from elasticsearch_search import get_search_engine
        
        # Get search engine
        search_engine = get_search_engine()
        
        # Test searches
        test_queries = ['shirt', 'eco', 'sustainable', 'organic', 'cotton']
        
        for query in test_queries:
            try:
                results = search_engine.search_products(query=query, per_page=3)
                
                total_results = results.get('total', 0)
                products = results.get('products', [])
                
                print(f"🔍 '{query}': {total_results} results")
                
                if products:
                    for i, product in enumerate(products[:2], 1):  # Show top 2
                        name = product.get('name', 'N/A')
                        price = product.get('price', 0)
                        print(f"   {i}. {name} - ₹{price}")
                
            except Exception as e:
                print(f"❌ Search '{query}' failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Search test error: {e}")
        return False

def verify_elasticsearch_status():
    """Verify final Elasticsearch status"""
    print("\n📊 ELASTICSEARCH STATUS VERIFICATION")
    print("="*80)
    
    try:
        from elasticsearch_config import get_elasticsearch_client, get_product_index_name
        
        es_client = get_elasticsearch_client()
        
        if es_client is None:
            print("❌ Elasticsearch client not available")
            return False
        
        # Check index status
        product_index = get_product_index_name()
        
        # Get document count
        doc_count = es_client.count(index=product_index)['count']
        print(f"📦 Products in index: {doc_count}")
        
        # Get index stats
        stats = es_client.indices.stats(index=product_index)
        index_size = stats['indices'][product_index]['total']['store']['size_in_bytes']
        print(f"💾 Index size: {index_size / 1024:.2f} KB")
        
        # Test search performance
        import time
        start_time = time.time()
        
        search_result = es_client.search(
            index=product_index,
            body={"query": {"match_all": {}}, "size": 1}
        )
        
        search_time = (time.time() - start_time) * 1000
        print(f"⚡ Search performance: {search_time:.2f}ms")
        
        return doc_count > 0
        
    except Exception as e:
        print(f"❌ Status verification error: {e}")
        return False

def main():
    print("🚀 Elasticsearch Population Script")
    print("="*80)
    
    # Step 1: Populate products
    population_success = populate_products()
    
    if population_success:
        # Step 2: Test search functionality
        test_populated_search()
        
        # Step 3: Verify status
        verify_elasticsearch_status()
        
        print(f"\n🎉 ELASTICSEARCH POPULATION COMPLETED!")
        print("="*80)
        print("✅ Products indexed successfully")
        print("✅ Search functionality working")
        print("✅ Elasticsearch fully operational")
        
        print(f"\n💡 YOU CAN NOW:")
        print("1. 🔍 Search products via API: GET /api/search?q=shirt")
        print("2. 📊 Use filters: GET /api/search?q=eco&category=clothing")
        print("3. 🖼️  Visual search: POST /api/visual-search (with image)")
        print("4. 💡 Get suggestions: GET /api/search/suggestions?q=eco")
        print("5. 📈 View analytics: GET /api/search/analytics")
        
    else:
        print(f"\n⚠️  ELASTICSEARCH POPULATION INCOMPLETE")
        print("="*80)
        print("❌ No products were indexed")
        print("💡 Make sure you have products in your database first")
        print("💡 Run: python setup_sample_data.py to add sample products")

if __name__ == '__main__':
    main()
