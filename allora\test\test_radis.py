import redis
import os
from dotenv import load_dotenv

load_dotenv()

def test_redis_connections():
    try:
        # Test main Redis connection
        r_main = redis.Redis(host='localhost', port=6379, db=0)
        r_main.set('test', 'main_connection')
        print(f"Main Redis: {r_main.get('test').decode()}")
        
        # Test session Redis
        r_session = redis.Redis(host='localhost', port=6379, db=1)
        r_session.set('session_test', 'session_connection')
        print(f"Session Redis: {r_session.get('session_test').decode()}")
        
        # Test cache Redis
        r_cache = redis.Redis(host='localhost', port=6379, db=2)
        r_cache.set('cache_test', 'cache_connection')
        print(f"Cache Redis: {r_cache.get('cache_test').decode()}")

        # Test rate limit Redis
        r_rate_limit = redis.Redis(host='localhost', port=6379, db=3)
        r_rate_limit.set('rate_limit_test', 'rate_limit_connection')
        print(f"Rate Limit Redis: {r_rate_limit.get('rate_limit_test').decode()}")
        
        print("✅ All Redis connections successful")
        return True
        
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        return False

if __name__ == "__main__":
    test_redis_connections()
