#!/usr/bin/env python3
"""
Quick Backend Test - Minimal test to check if backend is responsive
"""

import socket
import sys
import time

def test_port_connection(host='localhost', port=5000, timeout=5):
    """Test if port is accessible"""
    try:
        print(f"🔍 Testing connection to {host}:{port}")
        
        # Create socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        
        # Try to connect
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"✅ Port {port} is open and accepting connections")
            return True
        else:
            print(f"❌ Port {port} is not accessible (error code: {result})")
            return False
            
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        return False

def test_http_request():
    """Test basic HTTP request without external libraries"""
    try:
        print("🔍 Testing basic HTTP request")
        
        # Create socket for HTTP request
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        
        # Connect
        sock.connect(('localhost', 5000))
        
        # Send HTTP request
        request = "GET /api/health HTTP/1.1\r\nHost: localhost:5000\r\nConnection: close\r\n\r\n"
        sock.send(request.encode())
        
        # Receive response
        response = b""
        while True:
            try:
                data = sock.recv(1024)
                if not data:
                    break
                response += data
            except socket.timeout:
                break
        
        sock.close()
        
        # Parse response
        response_str = response.decode('utf-8', errors='ignore')
        
        if response_str:
            lines = response_str.split('\n')
            status_line = lines[0] if lines else "No response"
            print(f"📡 HTTP Response: {status_line}")
            
            if "200 OK" in status_line:
                print("✅ Backend is responding to HTTP requests")
                return True
            else:
                print(f"❌ Backend responded with: {status_line}")
                return False
        else:
            print("❌ No HTTP response received")
            return False
            
    except Exception as e:
        print(f"❌ HTTP test failed: {e}")
        return False

def main():
    print("🚀 Quick Backend Test")
    print("=" * 40)
    
    # Test 1: Port connectivity
    port_ok = test_port_connection()
    print()
    
    if not port_ok:
        print("🔧 Backend is not running or not accessible on port 5000")
        print("Try starting the backend:")
        print("  cd allora/backend")
        print("  python run_with_waitress.py")
        sys.exit(1)
    
    # Test 2: HTTP request
    http_ok = test_http_request()
    print()
    
    if http_ok:
        print("🎉 Backend is working correctly!")
        print("The issue might be with CORS or specific endpoints.")
        print("Try opening your test.html file now.")
    else:
        print("⚠️  Backend is running but not responding properly")
        print("This could indicate:")
        print("  - Backend is hanging during startup")
        print("  - Database connection issues")
        print("  - Import/dependency problems")
        print("  - Memory/resource issues")

if __name__ == "__main__":
    main()
