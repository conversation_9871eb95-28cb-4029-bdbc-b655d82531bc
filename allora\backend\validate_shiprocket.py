#!/usr/bin/env python3
"""
Quick Shiprocket Validation Script
=================================

Quick validation script to check if Shiprocket is properly configured.

Author: Allora Development Team
Date: 2025-07-14
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def validate_shiprocket():
    """Quick validation of Shiprocket configuration"""
    
    print("🚚 Quick Shiprocket Validation")
    print("=" * 40)
    
    # Check required environment variables
    email = os.getenv('SHIPROCKET_EMAIL')
    password = os.getenv('SHIPROCKET_PASSWORD')
    
    if not email or not password:
        print("❌ Shiprocket credentials not configured")
        print("\n📋 Required steps:")
        print("1. Run: python shiprocket_env_setup.py")
        print("2. Add your Shiprocket email and password")
        print("3. Run this validation again")
        return False
    
    print(f"✅ Email: {email}")
    print(f"✅ Password: {'*' * len(password)}")
    
    # Check configuration
    sandbox = os.getenv('SHIPROCKET_SANDBOX', 'true').lower() == 'true'
    pickup_location = os.getenv('SHIPROCKET_DEFAULT_PICKUP_LOCATION', 'primary')
    
    print(f"✅ Sandbox Mode: {sandbox}")
    print(f"✅ Pickup Location: {pickup_location}")
    
    # Check features
    features = {
        'COD': os.getenv('SHIPROCKET_ENABLE_COD', 'true').lower() == 'true',
        'International': os.getenv('SHIPROCKET_ENABLE_INTERNATIONAL', 'true').lower() == 'true',
        'Auto AWB': os.getenv('SHIPROCKET_AUTO_AWB', 'true').lower() == 'true'
    }
    
    print("\n🔧 Features:")
    for feature, enabled in features.items():
        status = "✅" if enabled else "⚪"
        print(f"  {status} {feature}: {enabled}")
    
    print("\n✅ Shiprocket configuration looks good!")
    print("\n📋 Next steps:")
    print("1. Test API connection: python test_shiprocket_integration.py")
    print("2. Create test shipment through your application")
    print("3. Monitor shipments in Shiprocket dashboard")
    
    return True

if __name__ == "__main__":
    validate_shiprocket()
