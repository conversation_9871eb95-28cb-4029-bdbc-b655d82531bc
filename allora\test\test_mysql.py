import mysql.connector
from mysql.connector import Error

try:
    # Test MySQL connection
    connection = mysql.connector.connect(
        host='localhost',
        user='root',
        password='Parul.2001'
    )
    
    if connection.is_connected():
        print("✅ MySQL connection successful")
        
        # Check if database exists
        cursor = connection.cursor()
        cursor.execute("SHOW DATABASES LIKE 'allora_db'")
        result = cursor.fetchone()
        
        if result:
            print("✅ Database 'allora_db' exists")
        else:
            print("❌ Database 'allora_db' does not exist")
            print("Creating database 'allora_db'...")
            cursor.execute("CREATE DATABASE allora_db")
            print("✅ Database 'allora_db' created successfully")
        
        cursor.close()
        
except Error as e:
    print(f"❌ MySQL connection failed: {e}")
    
finally:
    if 'connection' in locals() and connection.is_connected():
        connection.close()
        print("MySQL connection closed")
