"""
Admin Routes
============

Admin management endpoints with consistent response format and proper authorization.
"""

from flask import request
from datetime import datetime, timedelta
import logging

from ..common.versioning import create_versioned_blueprint
from ..common.response_wrapper import (
    success_response, error_response, paginated_response, 
    not_found_response, validation_error_response
)
from ..common.auth_decorators import admin_required_v2, rate_limit_v2, validate_content_type
from ..common.validators import (
    validate_json, validate_required_fields, validate_pagination,
    validate_email
)

logger = logging.getLogger(__name__)

# Create versioned blueprint
admin_bp = create_versioned_blueprint('admin', __name__, url_prefix='/admin')

@admin_bp.route('/dashboard', methods=['GET'])
@admin_required_v2()
def get_dashboard_overview(user, admin_user):
    """
    Get admin dashboard overview with key metrics.
    
    GET /api/v1/admin/dashboard
    """
    try:
        from app import db, User, Product, Order, CommunityPost, Sales
        
        # Calculate date ranges
        today = datetime.utcnow().date()
        yesterday = today - timedelta(days=1)
        last_30_days = today - timedelta(days=30)
        
        # User metrics
        total_users = User.query.filter_by(is_active=True).count()
        new_users_today = User.query.filter(
            db.func.date(User.created_at) == today
        ).count()
        new_users_yesterday = User.query.filter(
            db.func.date(User.created_at) == yesterday
        ).count()
        
        # Product metrics
        total_products = Product.query.filter_by(is_active=True).count()
        low_stock_products = Product.query.filter(
            Product.stock_quantity < 10,
            Product.stock_quantity > 0,
            Product.is_active == True
        ).count()
        out_of_stock_products = Product.query.filter(
            Product.stock_quantity == 0,
            Product.is_active == True
        ).count()
        
        # Order metrics
        total_orders = Order.query.count()
        pending_orders = Order.query.filter_by(status='pending').count()
        orders_today = Order.query.filter(
            db.func.date(Order.created_at) == today
        ).count()
        
        # Sales metrics
        total_revenue = db.session.query(
            db.func.sum(Sales.total_amount)
        ).scalar() or 0
        
        revenue_last_30_days = db.session.query(
            db.func.sum(Sales.total_amount)
        ).filter(
            Sales.created_at >= last_30_days
        ).scalar() or 0
        
        # Community metrics
        total_posts = CommunityPost.query.filter_by(is_active=True).count()
        posts_today = CommunityPost.query.filter(
            db.func.date(CommunityPost.created_at) == today,
            CommunityPost.is_active == True
        ).count()
        
        # Recent activity
        recent_orders = Order.query.order_by(
            Order.created_at.desc()
        ).limit(5).all()
        
        recent_users = User.query.filter_by(is_active=True).order_by(
            User.created_at.desc()
        ).limit(5).all()
        
        dashboard_data = {
            "metrics": {
                "users": {
                    "total": total_users,
                    "new_today": new_users_today,
                    "growth": new_users_today - new_users_yesterday
                },
                "products": {
                    "total": total_products,
                    "low_stock": low_stock_products,
                    "out_of_stock": out_of_stock_products
                },
                "orders": {
                    "total": total_orders,
                    "pending": pending_orders,
                    "today": orders_today
                },
                "revenue": {
                    "total": float(total_revenue),
                    "last_30_days": float(revenue_last_30_days)
                },
                "community": {
                    "total_posts": total_posts,
                    "posts_today": posts_today
                }
            },
            "recent_activity": {
                "orders": [
                    {
                        "id": order.id,
                        "user_id": order.user_id,
                        "status": order.status,
                        "total": float(order.total_amount),
                        "created_at": order.created_at.isoformat() if order.created_at else None
                    }
                    for order in recent_orders
                ],
                "users": [
                    {
                        "id": user.id,
                        "name": f"{user.first_name} {user.last_name}",
                        "email": user.email,
                        "created_at": user.created_at.isoformat() if user.created_at else None
                    }
                    for user in recent_users
                ]
            }
        }
        
        return success_response(
            data=dashboard_data,
            message="Dashboard overview retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get dashboard overview error: {e}")
        return error_response(
            message="Failed to retrieve dashboard overview",
            status_code=500,
            error_code="DASHBOARD_OVERVIEW_FAILED"
        )

@admin_bp.route('/users', methods=['GET'])
@admin_required_v2('user_management')
def get_users(user, admin_user):
    """
    Get users with pagination and filtering for admin management.
    
    GET /api/v1/admin/users
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', '').strip()
        status = request.args.get('status')  # active, inactive
        
        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=100)
        
        from app import User
        
        # Build query
        query = User.query
        
        if search:
            search_filter = f'%{search}%'
            query = query.filter(
                db.or_(
                    User.first_name.ilike(search_filter),
                    User.last_name.ilike(search_filter),
                    User.email.ilike(search_filter)
                )
            )
        
        if status == 'active':
            query = query.filter_by(is_active=True)
        elif status == 'inactive':
            query = query.filter_by(is_active=False)
        
        query = query.order_by(User.created_at.desc())
        
        # Execute paginated query
        paginated_users = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        users_data = []
        for user_item in paginated_users.items:
            users_data.append({
                "id": user_item.id,
                "first_name": user_item.first_name,
                "last_name": user_item.last_name,
                "email": user_item.email,
                "phone": getattr(user_item, 'phone', None),
                "is_active": user_item.is_active,
                "email_verified": getattr(user_item, 'email_verified', False),
                "created_at": user_item.created_at.isoformat() if user_item.created_at else None,
                "last_login": user_item.last_login.isoformat() if getattr(user_item, 'last_login', None) else None
            })
        
        return paginated_response(
            data=users_data,
            page=page,
            per_page=per_page,
            total=paginated_users.total,
            message="Users retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get users error: {e}")
        return error_response(
            message="Failed to retrieve users",
            status_code=500,
            error_code="USERS_FETCH_FAILED"
        )

@admin_bp.route('/users/<int:user_id>/status', methods=['PUT'])
@admin_required_v2('user_management')
@validate_content_type()
def update_user_status(user, admin_user, user_id):
    """
    Update user account status (activate/deactivate).
    
    PUT /api/v1/admin/users/{user_id}/status
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data
        
        # Validate required fields
        required_fields = ['is_active']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp
        
        is_active = data['is_active']
        reason = data.get('reason', '')
        
        if not isinstance(is_active, bool):
            return validation_error_response(
                errors={"is_active": ["Must be a boolean value"]},
                message="Invalid status value"
            )
        
        from app import db, User
        
        # Find user
        target_user = User.query.get(user_id)
        if not target_user:
            return not_found_response("User", user_id)
        
        # Prevent admin from deactivating themselves
        if target_user.id == user.id:
            return error_response(
                message="Cannot modify your own account status",
                status_code=400,
                error_code="SELF_MODIFICATION_NOT_ALLOWED"
            )
        
        # Update user status
        old_status = target_user.is_active
        target_user.is_active = is_active
        target_user.updated_at = datetime.utcnow()
        
        # Log the action (you might want to create an AdminActionLog model)
        action_type = "activate" if is_active else "deactivate"
        logger.info(f"Admin {user.id} {action_type}d user {user_id}. Reason: {reason}")
        
        db.session.commit()
        
        user_data = {
            "id": target_user.id,
            "name": f"{target_user.first_name} {target_user.last_name}",
            "email": target_user.email,
            "is_active": target_user.is_active,
            "status_changed": old_status != is_active,
            "updated_at": target_user.updated_at.isoformat()
        }
        
        return success_response(
            data=user_data,
            message=f"User account {'activated' if is_active else 'deactivated'} successfully"
        )
        
    except Exception as e:
        logger.error(f"Update user status error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to update user status",
            status_code=500,
            error_code="USER_STATUS_UPDATE_FAILED"
        )

@admin_bp.route('/products/pending', methods=['GET'])
@admin_required_v2('content_management')
def get_pending_products(user, admin_user):
    """
    Get products pending admin approval.
    
    GET /api/v1/admin/products/pending
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=50)
        
        from app import Product, User
        
        # Get pending products (assuming there's a status field)
        query = Product.query.filter_by(
            status='pending'  # Assuming products have a status field
        ).order_by(Product.created_at.desc())
        
        # Execute paginated query
        paginated_products = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        products_data = []
        for product in paginated_products.items:
            # Get seller info if available
            seller_info = None
            if hasattr(product, 'seller_id') and product.seller_id:
                from app import Seller
                seller = Seller.query.get(product.seller_id)
                if seller:
                    seller_user = User.query.get(seller.user_id)
                    seller_info = {
                        "id": seller.id,
                        "business_name": seller.business_name,
                        "contact_email": seller_user.email if seller_user else None
                    }
            
            products_data.append({
                "id": product.id,
                "name": product.name,
                "description": product.description,
                "price": float(product.price),
                "category": product.category,
                "stock_quantity": product.stock_quantity,
                "seller": seller_info,
                "created_at": product.created_at.isoformat() if product.created_at else None
            })
        
        return paginated_response(
            data=products_data,
            page=page,
            per_page=per_page,
            total=paginated_products.total,
            message="Pending products retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get pending products error: {e}")
        return error_response(
            message="Failed to retrieve pending products",
            status_code=500,
            error_code="PENDING_PRODUCTS_FETCH_FAILED"
        )

@admin_bp.route('/system/health', methods=['GET'])
@admin_required_v2()
def get_system_health(user, admin_user):
    """
    Get system health status and metrics.
    
    GET /api/v1/admin/system/health
    """
    try:
        from app import db
        import psutil
        import os
        
        # Database health
        try:
            db.session.execute('SELECT 1')
            db_status = "healthy"
            db_error = None
        except Exception as e:
            db_status = "unhealthy"
            db_error = str(e)
        
        # System metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # Application metrics
        uptime = datetime.utcnow() - datetime.fromtimestamp(psutil.Process(os.getpid()).create_time())
        
        health_data = {
            "status": "healthy" if db_status == "healthy" else "degraded",
            "timestamp": datetime.utcnow().isoformat(),
            "services": {
                "database": {
                    "status": db_status,
                    "error": db_error
                },
                "application": {
                    "status": "healthy",
                    "uptime_seconds": int(uptime.total_seconds())
                }
            },
            "system_metrics": {
                "cpu_percent": cpu_percent,
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "percent": memory.percent
                },
                "disk": {
                    "total": disk.total,
                    "free": disk.free,
                    "percent": (disk.used / disk.total) * 100
                }
            }
        }
        
        return success_response(
            data=health_data,
            message="System health retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get system health error: {e}")
        return error_response(
            message="Failed to retrieve system health",
            status_code=500,
            error_code="SYSTEM_HEALTH_FAILED"
        )

@admin_bp.route('/sign-in', methods=['POST'])
@rate_limit_v2(limit=3, window=300, per='ip', block_duration=1800)
@validate_content_type()
def admin_sign_in():
    """
    Admin login endpoint.

    POST /api/v1/admin/sign-in
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['email', 'password']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        email = data['email'].strip().lower()
        password = data['password']

        from app import db, User, AdminUser, bcrypt
        from flask_jwt_extended import create_access_token, create_refresh_token

        # Find user
        user = User.query.filter_by(email=email).first()

        if not user or not bcrypt.check_password_hash(user.password_hash, password):
            return unauthorized_response("Invalid email or password")

        if not user.is_active:
            return forbidden_response("Account is deactivated")

        # Check if user is an admin
        admin_user = AdminUser.query.filter_by(user_id=user.id).first()
        if not admin_user:
            return forbidden_response("Admin access required")

        # Create tokens with admin role
        additional_claims = {"role": "admin", "admin_id": admin_user.id}
        access_token = create_access_token(
            identity=user.id,
            additional_claims=additional_claims
        )
        refresh_token = create_refresh_token(identity=user.id)

        # Update last login
        user.last_login = datetime.utcnow()
        db.session.commit()

        user_data = {
            "id": user.id,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "role": "admin",
            "admin_permissions": {
                "user_management": getattr(admin_user, 'user_management', False),
                "content_management": getattr(admin_user, 'content_management', False),
                "system_management": getattr(admin_user, 'system_management', False),
                "analytics_access": getattr(admin_user, 'analytics_access', False)
            }
        }

        return success_response(
            data={
                "user": user_data,
                "access_token": access_token,
                "refresh_token": refresh_token
            },
            message="Admin login successful"
        )

    except Exception as e:
        logger.error(f"Admin login error: {e}")
        return error_response(
            message="Admin login failed",
            status_code=500,
            error_code="ADMIN_LOGIN_FAILED"
        )

# Admin User Management Endpoints

@admin_bp.route('/users', methods=['GET'])
@admin_required_v2()
@rate_limit_v2(limit=60, window=60, per='user')
def get_users(user, admin_user):
    """
    Get all users with pagination and filtering.

    GET /api/v1/admin/users
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status')  # active, inactive, suspended
        user_type = request.args.get('user_type')  # customer, seller, admin
        search = request.args.get('search', '').strip()
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')

        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=100)

        from app import db, User, Order, Seller

        # Build query
        query = User.query

        # Apply filters
        if status:
            if status == 'active':
                query = query.filter_by(is_active=True)
            elif status == 'inactive':
                query = query.filter_by(is_active=False)
            elif status == 'suspended':
                query = query.filter_by(is_suspended=True)

        if user_type:
            query = query.filter_by(user_type=user_type)

        if search:
            query = query.filter(
                db.or_(
                    User.email.ilike(f'%{search}%'),
                    User.first_name.ilike(f'%{search}%'),
                    User.last_name.ilike(f'%{search}%')
                )
            )

        if date_from:
            try:
                from_date = datetime.strptime(date_from, '%Y-%m-%d')
                query = query.filter(User.created_at >= from_date)
            except ValueError:
                return validation_error_response(
                    errors={"date_from": ["Invalid date format. Use YYYY-MM-DD"]},
                    message="Invalid date format"
                )

        if date_to:
            try:
                to_date = datetime.strptime(date_to, '%Y-%m-%d')
                query = query.filter(User.created_at <= to_date)
            except ValueError:
                return validation_error_response(
                    errors={"date_to": ["Invalid date format. Use YYYY-MM-DD"]},
                    message="Invalid date format"
                )

        query = query.order_by(User.created_at.desc())

        # Execute paginated query
        paginated_users = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # Format users data
        users_data = []
        for user_obj in paginated_users.items:
            # Get user statistics
            total_orders = Order.query.filter_by(user_id=user_obj.id).count()
            total_spent = db.session.query(
                db.func.sum(Order.total_amount)
            ).filter(
                Order.user_id == user_obj.id,
                Order.status.in_(['completed', 'delivered'])
            ).scalar() or 0.0

            # Check if user is a seller
            seller_info = None
            if user_obj.user_type == 'seller':
                seller = Seller.query.filter_by(user_id=user_obj.id).first()
                if seller:
                    seller_info = {
                        "seller_id": seller.id,
                        "business_name": seller.business_name,
                        "status": seller.status,
                        "is_verified": seller.is_verified
                    }

            users_data.append({
                "id": user_obj.id,
                "email": user_obj.email,
                "first_name": user_obj.first_name,
                "last_name": user_obj.last_name,
                "user_type": getattr(user_obj, 'user_type', 'customer'),
                "is_active": user_obj.is_active,
                "is_suspended": getattr(user_obj, 'is_suspended', False),
                "email_verified": getattr(user_obj, 'email_verified', False),
                "phone_verified": getattr(user_obj, 'phone_verified', False),
                "seller_info": seller_info,
                "statistics": {
                    "total_orders": total_orders,
                    "total_spent": float(total_spent),
                    "last_login": getattr(user_obj, 'last_login', None)
                },
                "created_at": user_obj.created_at.isoformat() if user_obj.created_at else None,
                "updated_at": user_obj.updated_at.isoformat() if user_obj.updated_at else None
            })

        return paginated_response(
            data=users_data,
            page=page,
            per_page=per_page,
            total=paginated_users.total,
            message="Users retrieved successfully",
            meta={
                "filters_applied": {
                    "status": status,
                    "user_type": user_type,
                    "search": search,
                    "date_from": date_from,
                    "date_to": date_to
                }
            }
        )

    except Exception as e:
        logger.error(f"Get users error: {e}")
        return error_response(
            message="Failed to retrieve users",
            status_code=500,
            error_code="ADMIN_USERS_FETCH_FAILED"
        )

@admin_bp.route('/users/<int:user_id>/status', methods=['PUT'])
@admin_required_v2()
@validate_content_type()
@rate_limit_v2(limit=30, window=300, per='user')
def update_user_status(user, admin_user, user_id):
    """
    Update user status (activate, deactivate, suspend).

    PUT /api/v1/admin/users/{user_id}/status
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['action']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        action = data['action']
        reason = data.get('reason', '').strip()

        # Validate action
        valid_actions = ['activate', 'deactivate', 'suspend', 'unsuspend']
        if action not in valid_actions:
            return validation_error_response(
                errors={"action": [f"Must be one of: {', '.join(valid_actions)}"]},
                message="Invalid action"
            )

        from app import db, User

        # Get user
        target_user = User.query.get(user_id)
        if not target_user:
            return not_found_response("User", user_id)

        # Prevent admin from modifying their own account
        if target_user.id == user.id:
            return error_response(
                message="Cannot modify your own account status",
                status_code=403,
                error_code="SELF_MODIFICATION_FORBIDDEN"
            )

        # Apply action
        if action == 'activate':
            target_user.is_active = True
            target_user.is_suspended = False
        elif action == 'deactivate':
            target_user.is_active = False
        elif action == 'suspend':
            target_user.is_suspended = True
        elif action == 'unsuspend':
            target_user.is_suspended = False

        target_user.updated_at = datetime.utcnow()

        # Log admin action
        from app import AdminActionLog
        action_log = AdminActionLog(
            admin_user_id=user.id,
            action_type='user_status_update',
            target_type='user',
            target_id=user_id,
            details={
                'action': action,
                'reason': reason,
                'previous_status': {
                    'is_active': target_user.is_active,
                    'is_suspended': getattr(target_user, 'is_suspended', False)
                }
            },
            created_at=datetime.utcnow()
        )

        db.session.add(action_log)
        db.session.commit()

        return success_response(
            data={
                "user_id": user_id,
                "action": action,
                "new_status": {
                    "is_active": target_user.is_active,
                    "is_suspended": getattr(target_user, 'is_suspended', False)
                },
                "updated_at": target_user.updated_at.isoformat()
            },
            message=f"User {action} successful"
        )

    except Exception as e:
        logger.error(f"Update user status error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to update user status",
            status_code=500,
            error_code="ADMIN_USER_STATUS_UPDATE_FAILED"
        )

# Admin Seller Management Endpoints

@admin_bp.route('/sellers', methods=['GET'])
@admin_required_v2()
@rate_limit_v2(limit=60, window=60, per='user')
def get_sellers(user, admin_user):
    """
    Get all sellers with pagination and filtering.

    GET /api/v1/admin/sellers
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status')  # pending, approved, rejected, suspended
        verification_status = request.args.get('verification_status')  # verified, unverified
        search = request.args.get('search', '').strip()

        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=100)

        from app import db, Seller, User, Product, Order, SellerStore

        # Build query
        query = Seller.query

        # Apply filters
        if status:
            query = query.filter_by(status=status)

        if verification_status:
            if verification_status == 'verified':
                query = query.filter_by(is_verified=True)
            elif verification_status == 'unverified':
                query = query.filter_by(is_verified=False)

        if search:
            query = query.filter(
                db.or_(
                    Seller.business_name.ilike(f'%{search}%'),
                    Seller.business_email.ilike(f'%{search}%'),
                    Seller.contact_person_name.ilike(f'%{search}%')
                )
            )

        query = query.order_by(Seller.created_at.desc())

        # Execute paginated query
        paginated_sellers = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # Format sellers data
        sellers_data = []
        for seller in paginated_sellers.items:
            # Get seller statistics
            total_products = Product.query.filter_by(seller_id=seller.id, is_active=True).count()
            total_orders = db.session.query(Order).join(
                Product, Order.id == Product.id  # This would need proper OrderItem join
            ).filter(Product.seller_id == seller.id).count()

            # Get user info
            user_info = None
            if seller.user_id:
                seller_user = User.query.get(seller.user_id)
                if seller_user:
                    user_info = {
                        "user_id": seller_user.id,
                        "email": seller_user.email,
                        "is_active": seller_user.is_active,
                        "last_login": getattr(seller_user, 'last_login', None)
                    }

            # Get store info
            store = SellerStore.query.filter_by(seller_id=seller.id).first()
            store_info = None
            if store:
                store_info = {
                    "store_name": store.store_name,
                    "store_slug": store.store_slug,
                    "is_active": store.is_active
                }

            sellers_data.append({
                "id": seller.id,
                "business_name": seller.business_name,
                "business_email": seller.business_email,
                "business_phone": seller.business_phone,
                "business_type": seller.business_type,
                "contact_person_name": seller.contact_person_name,
                "status": seller.status,
                "is_verified": seller.is_verified,
                "commission_rate": getattr(seller, 'commission_rate', 10.0),
                "user_info": user_info,
                "store_info": store_info,
                "statistics": {
                    "total_products": total_products,
                    "total_orders": total_orders,
                    "rating": getattr(seller, 'rating', 0.0),
                    "total_reviews": getattr(seller, 'total_reviews', 0)
                },
                "created_at": seller.created_at.isoformat() if seller.created_at else None,
                "updated_at": seller.updated_at.isoformat() if seller.updated_at else None
            })

        return paginated_response(
            data=sellers_data,
            page=page,
            per_page=per_page,
            total=paginated_sellers.total,
            message="Sellers retrieved successfully",
            meta={
                "filters_applied": {
                    "status": status,
                    "verification_status": verification_status,
                    "search": search
                }
            }
        )

    except Exception as e:
        logger.error(f"Get sellers error: {e}")
        return error_response(
            message="Failed to retrieve sellers",
            status_code=500,
            error_code="ADMIN_SELLERS_FETCH_FAILED"
        )

@admin_bp.route('/sellers/<int:seller_id>', methods=['GET'])
@admin_required_v2()
@rate_limit_v2(limit=60, window=60, per='user')
def get_seller_details(user, admin_user, seller_id):
    """
    Get detailed seller information.

    GET /api/v1/admin/sellers/{seller_id}
    """
    try:
        from app import db, Seller, User, Product, Order, SellerStore, AdminActionLog

        # Get seller
        seller = Seller.query.get(seller_id)
        if not seller:
            return not_found_response("Seller", seller_id)

        # Get user info
        user_info = None
        if seller.user_id:
            seller_user = User.query.get(seller.user_id)
            if seller_user:
                user_info = {
                    "user_id": seller_user.id,
                    "email": seller_user.email,
                    "first_name": seller_user.first_name,
                    "last_name": seller_user.last_name,
                    "is_active": seller_user.is_active,
                    "email_verified": getattr(seller_user, 'email_verified', False),
                    "created_at": seller_user.created_at.isoformat() if seller_user.created_at else None,
                    "last_login": getattr(seller_user, 'last_login', None)
                }

        # Get store info
        store = SellerStore.query.filter_by(seller_id=seller.id).first()
        store_info = None
        if store:
            store_info = {
                "id": store.id,
                "store_name": store.store_name,
                "store_slug": store.store_slug,
                "store_description": store.store_description,
                "store_logo": store.store_logo,
                "store_banner": store.store_banner,
                "is_active": store.is_active,
                "created_at": store.created_at.isoformat() if store.created_at else None
            }

        # Get detailed statistics
        total_products = Product.query.filter_by(seller_id=seller.id).count()
        active_products = Product.query.filter_by(seller_id=seller.id, is_active=True).count()

        # Get recent admin actions on this seller
        recent_actions = AdminActionLog.query.filter_by(
            target_type='seller',
            target_id=seller_id
        ).order_by(AdminActionLog.created_at.desc()).limit(10).all()

        actions_data = []
        for action in recent_actions:
            admin_user_obj = User.query.get(action.admin_user_id)
            actions_data.append({
                "id": action.id,
                "action_type": action.action_type,
                "admin_user": f"{admin_user_obj.first_name} {admin_user_obj.last_name}" if admin_user_obj else "Unknown",
                "details": action.details or {},
                "created_at": action.created_at.isoformat() if action.created_at else None
            })

        seller_data = {
            "id": seller.id,
            "business_name": seller.business_name,
            "business_email": seller.business_email,
            "business_phone": seller.business_phone,
            "business_address": seller.business_address,
            "business_type": seller.business_type,
            "contact_person_name": seller.contact_person_name,
            "business_description": seller.business_description,
            "website_url": seller.website_url,
            "gst_number": seller.gst_number,
            "status": seller.status,
            "is_verified": seller.is_verified,
            "commission_rate": getattr(seller, 'commission_rate', 10.0),
            "user_info": user_info,
            "store_info": store_info,
            "statistics": {
                "total_products": total_products,
                "active_products": active_products,
                "rating": getattr(seller, 'rating', 0.0),
                "total_reviews": getattr(seller, 'total_reviews', 0),
                "total_sales": getattr(seller, 'total_sales', 0)
            },
            "recent_actions": actions_data,
            "created_at": seller.created_at.isoformat() if seller.created_at else None,
            "updated_at": seller.updated_at.isoformat() if seller.updated_at else None
        }

        return success_response(
            data=seller_data,
            message="Seller details retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Get seller details error: {e}")
        return error_response(
            message="Failed to retrieve seller details",
            status_code=500,
            error_code="ADMIN_SELLER_DETAILS_FETCH_FAILED"
        )

@admin_bp.route('/sellers/<int:seller_id>/status', methods=['PUT'])
@admin_required_v2()
@validate_content_type()
@rate_limit_v2(limit=30, window=300, per='user')
def update_seller_status(user, admin_user, seller_id):
    """
    Update seller status (approve, reject, suspend).

    PUT /api/v1/admin/sellers/{seller_id}/status
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['status']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        new_status = data['status']
        reason = data.get('reason', '').strip()

        # Validate status
        valid_statuses = ['pending', 'approved', 'rejected', 'suspended']
        if new_status not in valid_statuses:
            return validation_error_response(
                errors={"status": [f"Must be one of: {', '.join(valid_statuses)}"]},
                message="Invalid status"
            )

        from app import db, Seller, User
        import jwt
        from flask import current_app

        # Get seller
        seller = Seller.query.get(seller_id)
        if not seller:
            return not_found_response("Seller", seller_id)

        old_status = seller.status
        seller.status = new_status
        seller.updated_at = datetime.utcnow()

        # Handle approval workflow
        if new_status == 'approved' and old_status != 'approved':
            # Generate password setup token for new sellers
            if not seller.user_id:
                token_payload = {
                    'seller_id': seller.id,
                    'exp': datetime.utcnow() + timedelta(days=7)  # 7 days to setup password
                }
                setup_token = jwt.encode(token_payload, current_app.config['SECRET_KEY'], algorithm='HS256')

                # TODO: Send email with setup token
                # send_seller_approval_email(seller, setup_token)

        # Log admin action
        from app import AdminActionLog
        action_log = AdminActionLog(
            admin_user_id=user.id,
            action_type='seller_status_update',
            target_type='seller',
            target_id=seller_id,
            details={
                'old_status': old_status,
                'new_status': new_status,
                'reason': reason
            },
            created_at=datetime.utcnow()
        )

        db.session.add(action_log)
        db.session.commit()

        return success_response(
            data={
                "seller_id": seller_id,
                "old_status": old_status,
                "new_status": new_status,
                "updated_at": seller.updated_at.isoformat()
            },
            message=f"Seller status updated to {new_status}"
        )

    except Exception as e:
        logger.error(f"Update seller status error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to update seller status",
            status_code=500,
            error_code="ADMIN_SELLER_STATUS_UPDATE_FAILED"
        )

@admin_bp.route('/sellers/<int:seller_id>/commission', methods=['PUT'])
@admin_required_v2()
@validate_content_type()
@rate_limit_v2(limit=30, window=300, per='user')
def update_seller_commission(user, admin_user, seller_id):
    """
    Update seller commission rate.

    PUT /api/v1/admin/sellers/{seller_id}/commission
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['commission_rate']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        commission_rate = data['commission_rate']
        reason = data.get('reason', '').strip()

        # Validate commission rate
        if not isinstance(commission_rate, (int, float)) or commission_rate < 0 or commission_rate > 50:
            return validation_error_response(
                errors={"commission_rate": ["Commission rate must be between 0 and 50"]},
                message="Invalid commission rate"
            )

        from app import db, Seller

        # Get seller
        seller = Seller.query.get(seller_id)
        if not seller:
            return not_found_response("Seller", seller_id)

        old_commission_rate = getattr(seller, 'commission_rate', 10.0)
        seller.commission_rate = commission_rate
        seller.updated_at = datetime.utcnow()

        # Log admin action
        from app import AdminActionLog
        action_log = AdminActionLog(
            admin_user_id=user.id,
            action_type='seller_commission_update',
            target_type='seller',
            target_id=seller_id,
            details={
                'old_commission_rate': old_commission_rate,
                'new_commission_rate': commission_rate,
                'reason': reason
            },
            created_at=datetime.utcnow()
        )

        db.session.add(action_log)
        db.session.commit()

        return success_response(
            data={
                "seller_id": seller_id,
                "old_commission_rate": old_commission_rate,
                "new_commission_rate": commission_rate,
                "updated_at": seller.updated_at.isoformat()
            },
            message="Seller commission rate updated successfully"
        )

    except Exception as e:
        logger.error(f"Update seller commission error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to update seller commission",
            status_code=500,
            error_code="ADMIN_SELLER_COMMISSION_UPDATE_FAILED"
        )

# Admin Product Management Endpoints

@admin_bp.route('/products', methods=['GET'])
@admin_required_v2()
@rate_limit_v2(limit=60, window=60, per='user')
def get_products(user, admin_user):
    """
    Get all products with pagination and filtering.

    GET /api/v1/admin/products
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status')  # active, inactive, pending
        category = request.args.get('category')
        seller_id = request.args.get('seller_id', type=int)
        search = request.args.get('search', '').strip()

        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=100)

        from app import db, Product, Seller, ProductImage

        # Build query
        query = Product.query

        # Apply filters
        if status:
            if status == 'active':
                query = query.filter_by(is_active=True)
            elif status == 'inactive':
                query = query.filter_by(is_active=False)
            elif status == 'pending':
                query = query.filter_by(status='pending')

        if category:
            query = query.filter(Product.category.ilike(f'%{category}%'))

        if seller_id:
            query = query.filter_by(seller_id=seller_id)

        if search:
            query = query.filter(
                db.or_(
                    Product.name.ilike(f'%{search}%'),
                    Product.description.ilike(f'%{search}%'),
                    Product.sku.ilike(f'%{search}%')
                )
            )

        query = query.order_by(Product.created_at.desc())

        # Execute paginated query
        paginated_products = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # Format products data
        products_data = []
        for product in paginated_products.items:
            # Get seller info
            seller = Seller.query.get(product.seller_id) if product.seller_id else None

            # Get first product image
            first_image = ProductImage.query.filter_by(product_id=product.id).first()

            products_data.append({
                "id": product.id,
                "name": product.name,
                "sku": product.sku,
                "price": float(product.price),
                "category": product.category,
                "brand": getattr(product, 'brand', None),
                "stock_quantity": product.stock_quantity,
                "is_active": product.is_active,
                "status": getattr(product, 'status', 'active'),
                "image_url": first_image.image_url if first_image else None,
                "seller": {
                    "id": seller.id if seller else None,
                    "business_name": seller.business_name if seller else "Allora Direct",
                    "status": seller.status if seller else "approved"
                },
                "statistics": {
                    "total_sales": getattr(product, 'total_sales', 0),
                    "average_rating": product.average_rating or 0.0,
                    "total_reviews": product.total_reviews or 0
                },
                "created_at": product.created_at.isoformat() if product.created_at else None,
                "updated_at": product.updated_at.isoformat() if product.updated_at else None
            })

        return paginated_response(
            data=products_data,
            page=page,
            per_page=per_page,
            total=paginated_products.total,
            message="Products retrieved successfully",
            meta={
                "filters_applied": {
                    "status": status,
                    "category": category,
                    "seller_id": seller_id,
                    "search": search
                }
            }
        )

    except Exception as e:
        logger.error(f"Get products error: {e}")
        return error_response(
            message="Failed to retrieve products",
            status_code=500,
            error_code="ADMIN_PRODUCTS_FETCH_FAILED"
        )

@admin_bp.route('/products/<int:product_id>/status', methods=['PUT'])
@admin_required_v2()
@validate_content_type()
@rate_limit_v2(limit=30, window=300, per='user')
def update_product_status(user, admin_user, product_id):
    """
    Update product status (approve, reject, activate, deactivate).

    PUT /api/v1/admin/products/{product_id}/status
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['action']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        action = data['action']
        reason = data.get('reason', '').strip()

        # Validate action
        valid_actions = ['approve', 'reject', 'activate', 'deactivate']
        if action not in valid_actions:
            return validation_error_response(
                errors={"action": [f"Must be one of: {', '.join(valid_actions)}"]},
                message="Invalid action"
            )

        from app import db, Product

        # Get product
        product = Product.query.get(product_id)
        if not product:
            return not_found_response("Product", product_id)

        old_status = getattr(product, 'status', 'active')
        old_is_active = product.is_active

        # Apply action
        if action == 'approve':
            product.status = 'approved'
            product.is_active = True
        elif action == 'reject':
            product.status = 'rejected'
            product.is_active = False
        elif action == 'activate':
            product.is_active = True
        elif action == 'deactivate':
            product.is_active = False

        product.updated_at = datetime.utcnow()

        # Log admin action
        from app import AdminActionLog
        action_log = AdminActionLog(
            admin_user_id=user.id,
            action_type='product_status_update',
            target_type='product',
            target_id=product_id,
            details={
                'action': action,
                'old_status': old_status,
                'new_status': getattr(product, 'status', 'active'),
                'old_is_active': old_is_active,
                'new_is_active': product.is_active,
                'reason': reason
            },
            created_at=datetime.utcnow()
        )

        db.session.add(action_log)
        db.session.commit()

        return success_response(
            data={
                "product_id": product_id,
                "action": action,
                "new_status": {
                    "status": getattr(product, 'status', 'active'),
                    "is_active": product.is_active
                },
                "updated_at": product.updated_at.isoformat()
            },
            message=f"Product {action} successful"
        )

    except Exception as e:
        logger.error(f"Update product status error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to update product status",
            status_code=500,
            error_code="ADMIN_PRODUCT_STATUS_UPDATE_FAILED"
        )
