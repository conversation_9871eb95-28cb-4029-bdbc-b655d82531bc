"""
Admin Routes
============

Admin management endpoints with consistent response format and proper authorization.
"""

from flask import request
from datetime import datetime, timedelta
import logging

from ..common.versioning import create_versioned_blueprint
from ..common.response_wrapper import (
    success_response, error_response, paginated_response, 
    not_found_response, validation_error_response
)
from ..common.auth_decorators import admin_required_v2, rate_limit_v2, validate_content_type
from ..common.validators import (
    validate_json, validate_required_fields, validate_pagination,
    validate_email
)

logger = logging.getLogger(__name__)

# Create versioned blueprint
admin_bp = create_versioned_blueprint('admin', __name__, url_prefix='/admin')

@admin_bp.route('/dashboard', methods=['GET'])
@admin_required_v2()
def get_dashboard_overview(user, admin_user):
    """
    Get admin dashboard overview with key metrics.
    
    GET /api/v1/admin/dashboard
    """
    try:
        from app import db, User, Product, Order, CommunityPost, Sales
        
        # Calculate date ranges
        today = datetime.utcnow().date()
        yesterday = today - timedelta(days=1)
        last_30_days = today - timedelta(days=30)
        
        # User metrics
        total_users = User.query.filter_by(is_active=True).count()
        new_users_today = User.query.filter(
            db.func.date(User.created_at) == today
        ).count()
        new_users_yesterday = User.query.filter(
            db.func.date(User.created_at) == yesterday
        ).count()
        
        # Product metrics
        total_products = Product.query.filter_by(is_active=True).count()
        low_stock_products = Product.query.filter(
            Product.stock_quantity < 10,
            Product.stock_quantity > 0,
            Product.is_active == True
        ).count()
        out_of_stock_products = Product.query.filter(
            Product.stock_quantity == 0,
            Product.is_active == True
        ).count()
        
        # Order metrics
        total_orders = Order.query.count()
        pending_orders = Order.query.filter_by(status='pending').count()
        orders_today = Order.query.filter(
            db.func.date(Order.created_at) == today
        ).count()
        
        # Sales metrics
        total_revenue = db.session.query(
            db.func.sum(Sales.total_amount)
        ).scalar() or 0
        
        revenue_last_30_days = db.session.query(
            db.func.sum(Sales.total_amount)
        ).filter(
            Sales.created_at >= last_30_days
        ).scalar() or 0
        
        # Community metrics
        total_posts = CommunityPost.query.filter_by(is_active=True).count()
        posts_today = CommunityPost.query.filter(
            db.func.date(CommunityPost.created_at) == today,
            CommunityPost.is_active == True
        ).count()
        
        # Recent activity
        recent_orders = Order.query.order_by(
            Order.created_at.desc()
        ).limit(5).all()
        
        recent_users = User.query.filter_by(is_active=True).order_by(
            User.created_at.desc()
        ).limit(5).all()
        
        dashboard_data = {
            "metrics": {
                "users": {
                    "total": total_users,
                    "new_today": new_users_today,
                    "growth": new_users_today - new_users_yesterday
                },
                "products": {
                    "total": total_products,
                    "low_stock": low_stock_products,
                    "out_of_stock": out_of_stock_products
                },
                "orders": {
                    "total": total_orders,
                    "pending": pending_orders,
                    "today": orders_today
                },
                "revenue": {
                    "total": float(total_revenue),
                    "last_30_days": float(revenue_last_30_days)
                },
                "community": {
                    "total_posts": total_posts,
                    "posts_today": posts_today
                }
            },
            "recent_activity": {
                "orders": [
                    {
                        "id": order.id,
                        "user_id": order.user_id,
                        "status": order.status,
                        "total": float(order.total_amount),
                        "created_at": order.created_at.isoformat() if order.created_at else None
                    }
                    for order in recent_orders
                ],
                "users": [
                    {
                        "id": user.id,
                        "name": f"{user.first_name} {user.last_name}",
                        "email": user.email,
                        "created_at": user.created_at.isoformat() if user.created_at else None
                    }
                    for user in recent_users
                ]
            }
        }
        
        return success_response(
            data=dashboard_data,
            message="Dashboard overview retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get dashboard overview error: {e}")
        return error_response(
            message="Failed to retrieve dashboard overview",
            status_code=500,
            error_code="DASHBOARD_OVERVIEW_FAILED"
        )

@admin_bp.route('/users', methods=['GET'])
@admin_required_v2('user_management')
def get_users(user, admin_user):
    """
    Get users with pagination and filtering for admin management.
    
    GET /api/v1/admin/users
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', '').strip()
        status = request.args.get('status')  # active, inactive
        
        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=100)
        
        from app import User
        
        # Build query
        query = User.query
        
        if search:
            search_filter = f'%{search}%'
            query = query.filter(
                db.or_(
                    User.first_name.ilike(search_filter),
                    User.last_name.ilike(search_filter),
                    User.email.ilike(search_filter)
                )
            )
        
        if status == 'active':
            query = query.filter_by(is_active=True)
        elif status == 'inactive':
            query = query.filter_by(is_active=False)
        
        query = query.order_by(User.created_at.desc())
        
        # Execute paginated query
        paginated_users = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        users_data = []
        for user_item in paginated_users.items:
            users_data.append({
                "id": user_item.id,
                "first_name": user_item.first_name,
                "last_name": user_item.last_name,
                "email": user_item.email,
                "phone": getattr(user_item, 'phone', None),
                "is_active": user_item.is_active,
                "email_verified": getattr(user_item, 'email_verified', False),
                "created_at": user_item.created_at.isoformat() if user_item.created_at else None,
                "last_login": user_item.last_login.isoformat() if getattr(user_item, 'last_login', None) else None
            })
        
        return paginated_response(
            data=users_data,
            page=page,
            per_page=per_page,
            total=paginated_users.total,
            message="Users retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get users error: {e}")
        return error_response(
            message="Failed to retrieve users",
            status_code=500,
            error_code="USERS_FETCH_FAILED"
        )

@admin_bp.route('/users/<int:user_id>/status', methods=['PUT'])
@admin_required_v2('user_management')
@validate_content_type()
def update_user_status(user, admin_user, user_id):
    """
    Update user account status (activate/deactivate).
    
    PUT /api/v1/admin/users/{user_id}/status
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data
        
        # Validate required fields
        required_fields = ['is_active']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp
        
        is_active = data['is_active']
        reason = data.get('reason', '')
        
        if not isinstance(is_active, bool):
            return validation_error_response(
                errors={"is_active": ["Must be a boolean value"]},
                message="Invalid status value"
            )
        
        from app import db, User
        
        # Find user
        target_user = User.query.get(user_id)
        if not target_user:
            return not_found_response("User", user_id)
        
        # Prevent admin from deactivating themselves
        if target_user.id == user.id:
            return error_response(
                message="Cannot modify your own account status",
                status_code=400,
                error_code="SELF_MODIFICATION_NOT_ALLOWED"
            )
        
        # Update user status
        old_status = target_user.is_active
        target_user.is_active = is_active
        target_user.updated_at = datetime.utcnow()
        
        # Log the action (you might want to create an AdminActionLog model)
        action_type = "activate" if is_active else "deactivate"
        logger.info(f"Admin {user.id} {action_type}d user {user_id}. Reason: {reason}")
        
        db.session.commit()
        
        user_data = {
            "id": target_user.id,
            "name": f"{target_user.first_name} {target_user.last_name}",
            "email": target_user.email,
            "is_active": target_user.is_active,
            "status_changed": old_status != is_active,
            "updated_at": target_user.updated_at.isoformat()
        }
        
        return success_response(
            data=user_data,
            message=f"User account {'activated' if is_active else 'deactivated'} successfully"
        )
        
    except Exception as e:
        logger.error(f"Update user status error: {e}")
        db.session.rollback()
        return error_response(
            message="Failed to update user status",
            status_code=500,
            error_code="USER_STATUS_UPDATE_FAILED"
        )

@admin_bp.route('/products/pending', methods=['GET'])
@admin_required_v2('content_management')
def get_pending_products(user, admin_user):
    """
    Get products pending admin approval.
    
    GET /api/v1/admin/products/pending
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        # Validate pagination
        page, per_page = validate_pagination(page, per_page, max_per_page=50)
        
        from app import Product, User
        
        # Get pending products (assuming there's a status field)
        query = Product.query.filter_by(
            status='pending'  # Assuming products have a status field
        ).order_by(Product.created_at.desc())
        
        # Execute paginated query
        paginated_products = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        products_data = []
        for product in paginated_products.items:
            # Get seller info if available
            seller_info = None
            if hasattr(product, 'seller_id') and product.seller_id:
                from app import Seller
                seller = Seller.query.get(product.seller_id)
                if seller:
                    seller_user = User.query.get(seller.user_id)
                    seller_info = {
                        "id": seller.id,
                        "business_name": seller.business_name,
                        "contact_email": seller_user.email if seller_user else None
                    }
            
            products_data.append({
                "id": product.id,
                "name": product.name,
                "description": product.description,
                "price": float(product.price),
                "category": product.category,
                "stock_quantity": product.stock_quantity,
                "seller": seller_info,
                "created_at": product.created_at.isoformat() if product.created_at else None
            })
        
        return paginated_response(
            data=products_data,
            page=page,
            per_page=per_page,
            total=paginated_products.total,
            message="Pending products retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get pending products error: {e}")
        return error_response(
            message="Failed to retrieve pending products",
            status_code=500,
            error_code="PENDING_PRODUCTS_FETCH_FAILED"
        )

@admin_bp.route('/system/health', methods=['GET'])
@admin_required_v2()
def get_system_health(user, admin_user):
    """
    Get system health status and metrics.
    
    GET /api/v1/admin/system/health
    """
    try:
        from app import db
        import psutil
        import os
        
        # Database health
        try:
            db.session.execute('SELECT 1')
            db_status = "healthy"
            db_error = None
        except Exception as e:
            db_status = "unhealthy"
            db_error = str(e)
        
        # System metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # Application metrics
        uptime = datetime.utcnow() - datetime.fromtimestamp(psutil.Process(os.getpid()).create_time())
        
        health_data = {
            "status": "healthy" if db_status == "healthy" else "degraded",
            "timestamp": datetime.utcnow().isoformat(),
            "services": {
                "database": {
                    "status": db_status,
                    "error": db_error
                },
                "application": {
                    "status": "healthy",
                    "uptime_seconds": int(uptime.total_seconds())
                }
            },
            "system_metrics": {
                "cpu_percent": cpu_percent,
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "percent": memory.percent
                },
                "disk": {
                    "total": disk.total,
                    "free": disk.free,
                    "percent": (disk.used / disk.total) * 100
                }
            }
        }
        
        return success_response(
            data=health_data,
            message="System health retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Get system health error: {e}")
        return error_response(
            message="Failed to retrieve system health",
            status_code=500,
            error_code="SYSTEM_HEALTH_FAILED"
        )

@admin_bp.route('/sign-in', methods=['POST'])
@rate_limit_v2(limit=3, window=300, per='ip', block_duration=1800)
@validate_content_type()
def admin_sign_in():
    """
    Admin login endpoint.

    POST /api/v1/admin/sign-in
    """
    try:
        # Validate JSON
        is_valid, data = validate_json()
        if not is_valid:
            return data

        # Validate required fields
        required_fields = ['email', 'password']
        is_valid, error_resp = validate_required_fields(data, required_fields)
        if not is_valid:
            return error_resp

        email = data['email'].strip().lower()
        password = data['password']

        from app import db, User, AdminUser, bcrypt
        from flask_jwt_extended import create_access_token, create_refresh_token

        # Find user
        user = User.query.filter_by(email=email).first()

        if not user or not bcrypt.check_password_hash(user.password_hash, password):
            return unauthorized_response("Invalid email or password")

        if not user.is_active:
            return forbidden_response("Account is deactivated")

        # Check if user is an admin
        admin_user = AdminUser.query.filter_by(user_id=user.id).first()
        if not admin_user:
            return forbidden_response("Admin access required")

        # Create tokens with admin role
        additional_claims = {"role": "admin", "admin_id": admin_user.id}
        access_token = create_access_token(
            identity=user.id,
            additional_claims=additional_claims
        )
        refresh_token = create_refresh_token(identity=user.id)

        # Update last login
        user.last_login = datetime.utcnow()
        db.session.commit()

        user_data = {
            "id": user.id,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "role": "admin",
            "admin_permissions": {
                "user_management": getattr(admin_user, 'user_management', False),
                "content_management": getattr(admin_user, 'content_management', False),
                "system_management": getattr(admin_user, 'system_management', False),
                "analytics_access": getattr(admin_user, 'analytics_access', False)
            }
        }

        return success_response(
            data={
                "user": user_data,
                "access_token": access_token,
                "refresh_token": refresh_token
            },
            message="Admin login successful"
        )

    except Exception as e:
        logger.error(f"Admin login error: {e}")
        return error_response(
            message="Admin login failed",
            status_code=500,
            error_code="ADMIN_LOGIN_FAILED"
        )
