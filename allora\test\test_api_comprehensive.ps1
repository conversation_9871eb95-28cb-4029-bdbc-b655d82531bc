# Comprehensive Allora Backend API Testing Script
# ===============================================

$baseUrl = "http://localhost:5000"
$testResults = @()

Write-Host "Starting Comprehensive Allora Backend API Testing..." -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan

# First, login to get auth token
Write-Host "`nLogging in to get auth token..." -ForegroundColor Yellow
$loginData = @{
    email = "<EMAIL>"
    password = "TestPass123!"
} | ConvertTo-Json

try {
    $response = Invoke-WebRequest -Uri "$baseUrl/api/login" -Method POST -Body $loginData -ContentType "application/json"
    $loginResponse = $response.Content | ConvertFrom-Json
    $global:authToken = $loginResponse.token
    Write-Host "Login successful, token obtained" -ForegroundColor Green
} catch {
    Write-Host "Login failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

$headers = @{
    "Authorization" = "Bearer $global:authToken"
    "Content-Type" = "application/json"
}

# Test 1: Products API
Write-Host "`n1. Testing Products API..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/api/products" -Method GET
    Write-Host "Products API Status: $($response.StatusCode)" -ForegroundColor Green
    $products = $response.Content | ConvertFrom-Json
    Write-Host "Products count: $($products.Count)" -ForegroundColor White
    $testResults += "Products API: PASS"
} catch {
    Write-Host "Products API Failed: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += "Products API: FAIL"
}

# Test 2: Categories API
Write-Host "`n2. Testing Categories API..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/api/categories" -Method GET
    Write-Host "Categories API Status: $($response.StatusCode)" -ForegroundColor Green
    $categories = $response.Content | ConvertFrom-Json
    Write-Host "Categories count: $($categories.Count)" -ForegroundColor White
    $testResults += "Categories API: PASS"
} catch {
    Write-Host "Categories API Failed: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += "Categories API: FAIL"
}

# Test 3: Cart API - Add to Cart
Write-Host "`n3. Testing Cart API - Add Item..." -ForegroundColor Yellow
$cartData = @{
    product_id = 1
    quantity = 2
} | ConvertTo-Json

try {
    $response = Invoke-WebRequest -Uri "$baseUrl/api/cart/add" -Method POST -Body $cartData -Headers $headers
    Write-Host "Add to Cart Status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor White
    $testResults += "Add to Cart: PASS"
} catch {
    Write-Host "Add to Cart Failed: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += "Add to Cart: FAIL"
}

# Test 4: Cart API - Get Cart
Write-Host "`n4. Testing Cart API - Get Cart..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/api/cart" -Method GET -Headers $headers
    Write-Host "Get Cart Status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor White
    $testResults += "Get Cart: PASS"
} catch {
    Write-Host "Get Cart Failed: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += "Get Cart: FAIL"
}

# Test 5: User Profile API
Write-Host "`n5. Testing User Profile API..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/api/user/profile" -Method GET -Headers $headers
    Write-Host "User Profile Status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor White
    $testResults += "User Profile: PASS"
} catch {
    Write-Host "User Profile Failed: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += "User Profile: FAIL"
}

# Test 6: Search API
Write-Host "`n6. Testing Search API..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/api/search?q=test" -Method GET
    Write-Host "Search API Status: $($response.StatusCode)" -ForegroundColor Green
    $searchResults = $response.Content | ConvertFrom-Json
    Write-Host "Search results count: $($searchResults.Count)" -ForegroundColor White
    $testResults += "Search API: PASS"
} catch {
    Write-Host "Search API Failed: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += "Search API: FAIL"
}

# Test 7: Orders API - Get Orders
Write-Host "`n7. Testing Orders API..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/api/orders" -Method GET -Headers $headers
    Write-Host "Orders API Status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor White
    $testResults += "Orders API: PASS"
} catch {
    Write-Host "Orders API Failed: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += "Orders API: FAIL"
}

# Test 8: Wishlist API
Write-Host "`n8. Testing Wishlist API..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/api/wishlist" -Method GET -Headers $headers
    Write-Host "Wishlist API Status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor White
    $testResults += "Wishlist API: PASS"
} catch {
    Write-Host "Wishlist API Failed: $($_.Exception.Message)" -ForegroundColor Red
    $testResults += "Wishlist API: FAIL"
}

# Test Summary
Write-Host "`nComprehensive API Test Summary:" -ForegroundColor Cyan
Write-Host "===============================" -ForegroundColor Cyan
foreach ($result in $testResults) {
    if ($result -like "*PASS*") {
        Write-Host "$result" -ForegroundColor Green
    } else {
        Write-Host "$result" -ForegroundColor Red
    }
}

$passCount = ($testResults | Where-Object { $_ -like "*PASS*" }).Count
$totalCount = $testResults.Count
Write-Host "`nOverall Results: $passCount/$totalCount tests passed" -ForegroundColor Cyan

Write-Host "`nComprehensive API Testing Complete!" -ForegroundColor Cyan
