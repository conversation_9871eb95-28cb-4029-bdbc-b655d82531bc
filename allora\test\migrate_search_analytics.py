#!/usr/bin/env python3
"""
Database migration script for Search Analytics
This script updates the search_analytics table to match the new schema
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from sqlalchemy import text
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_search_analytics():
    """Migrate search_analytics table to new schema"""
    
    with app.app_context():
        try:
            # Check if the table exists and get its current structure
            result = db.session.execute(text("SHOW TABLES LIKE 'search_analytics'"))
            table_exists = result.fetchone() is not None
            
            if table_exists:
                logger.info("Found existing search_analytics table, checking schema...")
                
                # Check current columns
                result = db.session.execute(text("DESCRIBE search_analytics"))
                columns = {row[0]: str(row[1]) for row in result.fetchall()}
                logger.info(f"Current columns: {list(columns.keys())}")

                # Check if we need to migrate
                needs_migration = False

                # Check for old column names
                if 'search_query' in columns and 'query' not in columns:
                    logger.info("Need to rename search_query to query")
                    needs_migration = True

                # Check if id is integer (old) vs string (new)
                if 'id' in columns and 'int' in columns['id'].lower():
                    logger.info("Need to change id from integer to string")
                    needs_migration = True
                
                # Check for missing columns
                required_columns = [
                    'query', 'filters', 'search_type', 'timestamp', 
                    'results_count', 'response_time_ms', 'elasticsearch_time_ms',
                    'clicked_results', 'conversion_events'
                ]
                
                missing_columns = [col for col in required_columns if col not in columns]
                if missing_columns:
                    logger.info(f"Missing columns: {missing_columns}")
                    needs_migration = True
                
                if needs_migration:
                    logger.info("Starting migration...")
                    
                    # Backup existing data
                    logger.info("Backing up existing data...")
                    backup_result = db.session.execute(text("SELECT * FROM search_analytics"))
                    backup_data = backup_result.fetchall()
                    logger.info(f"Backed up {len(backup_data)} records")
                    
                    # Drop the old table
                    logger.info("Dropping old search_analytics table...")
                    db.session.execute(text("DROP TABLE search_analytics"))
                    db.session.commit()
                    
                    # Create new table with correct schema
                    logger.info("Creating new search_analytics table...")
                    create_new_table()
                    
                    # Migrate data if any existed
                    if backup_data:
                        logger.info("Migrating existing data...")
                        migrate_data(backup_data, columns)
                    
                    logger.info("Migration completed successfully!")
                else:
                    logger.info("Table schema is already up to date")
            else:
                logger.info("search_analytics table doesn't exist, creating new one...")
                create_new_table()
                
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            db.session.rollback()
            raise

def create_new_table():
    """Create the new search_analytics table with correct schema"""
    
    create_table_sql = """
    CREATE TABLE search_analytics (
        id VARCHAR(36) PRIMARY KEY,
        user_id VARCHAR(36) NULL,
        session_id VARCHAR(64) NOT NULL,
        query TEXT NOT NULL,
        filters JSON NULL,
        search_type VARCHAR(20) NOT NULL,
        timestamp DATETIME NOT NULL,
        results_count INT NOT NULL,
        response_time_ms FLOAT NOT NULL,
        elasticsearch_time_ms FLOAT NOT NULL,
        clicked_results JSON NULL,
        conversion_events JSON NULL,
        user_agent TEXT NULL,
        ip_address VARCHAR(45) NULL,
        INDEX idx_user_id (user_id),
        INDEX idx_session_id (session_id),
        INDEX idx_timestamp (timestamp),
        INDEX idx_search_type (search_type)
    )
    """
    
    db.session.execute(text(create_table_sql))
    db.session.commit()
    logger.info("Created new search_analytics table")

def migrate_data(backup_data, old_columns):
    """Migrate data from old schema to new schema"""
    import uuid
    from datetime import datetime
    
    migrated_count = 0
    
    for row in backup_data:
        try:
            # Convert old row to new format
            old_data = dict(zip(old_columns.keys(), row))
            
            # Generate new UUID for id
            new_id = str(uuid.uuid4())
            
            # Map old columns to new columns
            query = old_data.get('search_query', old_data.get('query', ''))
            user_id = str(old_data.get('user_id')) if old_data.get('user_id') else None
            session_id = old_data.get('session_id', old_data.get('guest_session_id', str(uuid.uuid4())))
            search_type = old_data.get('search_type', 'text')
            timestamp = old_data.get('created_at', datetime.utcnow())
            results_count = old_data.get('results_count', 0)
            clicked_results = old_data.get('clicked_results', [])
            filters_applied = old_data.get('filters_applied', {})
            user_agent = old_data.get('user_agent', '')
            ip_address = old_data.get('ip_address', '')
            
            # Insert migrated record
            insert_sql = """
            INSERT INTO search_analytics (
                id, user_id, session_id, query, filters, search_type, 
                timestamp, results_count, response_time_ms, elasticsearch_time_ms,
                clicked_results, conversion_events, user_agent, ip_address
            ) VALUES (
                :id, :user_id, :session_id, :query, :filters, :search_type,
                :timestamp, :results_count, :response_time_ms, :elasticsearch_time_ms,
                :clicked_results, :conversion_events, :user_agent, :ip_address
            )
            """
            
            db.session.execute(text(insert_sql), {
                'id': new_id,
                'user_id': user_id,
                'session_id': session_id,
                'query': query,
                'filters': filters_applied,
                'search_type': search_type,
                'timestamp': timestamp,
                'results_count': results_count,
                'response_time_ms': 0.0,  # Default for old records
                'elasticsearch_time_ms': 0.0,  # Default for old records
                'clicked_results': clicked_results,
                'conversion_events': [],  # Default for old records
                'user_agent': user_agent,
                'ip_address': ip_address
            })
            
            migrated_count += 1
            
        except Exception as e:
            logger.warning(f"Failed to migrate record: {e}")
            continue
    
    db.session.commit()
    logger.info(f"Successfully migrated {migrated_count} records")

def create_related_tables():
    """Create related search analytics tables"""
    
    # Create search_clicks table
    create_clicks_sql = """
    CREATE TABLE IF NOT EXISTS search_clicks (
        id VARCHAR(36) PRIMARY KEY,
        search_id VARCHAR(36) NOT NULL,
        product_id VARCHAR(36) NOT NULL,
        position INT NOT NULL,
        timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        user_id VARCHAR(36) NULL,
        session_id VARCHAR(64) NOT NULL,
        INDEX idx_search_id (search_id),
        INDEX idx_product_id (product_id),
        INDEX idx_timestamp (timestamp)
    )
    """
    
    # Create search_conversions table
    create_conversions_sql = """
    CREATE TABLE IF NOT EXISTS search_conversions (
        id VARCHAR(36) PRIMARY KEY,
        search_id VARCHAR(36) NOT NULL,
        product_id VARCHAR(36) NOT NULL,
        conversion_type VARCHAR(20) NOT NULL,
        conversion_value FLOAT NULL,
        timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        user_id VARCHAR(36) NULL,
        session_id VARCHAR(64) NOT NULL,
        INDEX idx_search_id (search_id),
        INDEX idx_product_id (product_id),
        INDEX idx_conversion_type (conversion_type),
        INDEX idx_timestamp (timestamp)
    )
    """
    
    db.session.execute(text(create_clicks_sql))
    db.session.execute(text(create_conversions_sql))
    db.session.commit()
    logger.info("Created search_clicks and search_conversions tables")

if __name__ == '__main__':
    logger.info("Starting search analytics migration...")
    migrate_search_analytics()

    # Create related tables within the same app context
    with app.app_context():
        create_related_tables()

    logger.info("Migration completed!")
