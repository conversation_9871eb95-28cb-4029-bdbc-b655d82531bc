#!/usr/bin/env python3
"""
Database Model Fix Script
========================

This script fixes all the missing columns in database models
identified by the structure test.

Author: Allora Development Team
Date: 2025-07-13
"""

import re

def fix_models():
    """Fix all database models with missing columns"""
    
    # Read the app.py file
    with open('app.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Define fixes for each model
    fixes = [
        # PaymentTransaction - add created_at, updated_at
        {
            'pattern': r'(class PaymentTransaction\(db\.Model\):.*?)(    # Relationships)',
            'replacement': r'\1    # Timestamps\n    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n\2'
        },
        
        # PriceHistory - add created_at
        {
            'pattern': r'(class PriceHistory\(db\.Model\):.*?recorded_date = db\.Column\(db\.DateTime, nullable=False, default=datetime\.utcnow\))(.*?product = db\.relationship)',
            'replacement': r'\1\n    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)\2    product = db.relationship'
        },
        
        # ProductVariant - add created_at
        {
            'pattern': r'(class ProductVariant\(db\.Model\):.*?is_available = db\.Column\(db\.Boolean, nullable=False, default=True\))(.*?product = db\.relationship)',
            'replacement': r'\1\n    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)\2    product = db.relationship'
        },
        
        # RMAApproval - add created_at
        {
            'pattern': r'(class RMAApproval\(db\.Model\):.*?decided_at = db\.Column\(db\.DateTime, nullable=True\))(.*?# Relationships)',
            'replacement': r'\1\n    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)\n\n\2'
        },
        
        # RMADocument - add created_at
        {
            'pattern': r'(class RMADocument\(db\.Model\):.*?is_public = db\.Column\(db\.Boolean, nullable=False, default=False\))(.*?# Relationships)',
            'replacement': r'\1\n    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)\n\n\2'
        },
        
        # RecentlyViewed - add created_at
        {
            'pattern': r'(class RecentlyViewed\(db\.Model\):.*?viewed_at = db\.Column\(db\.DateTime, nullable=False, default=datetime\.utcnow\))(.*?user = db\.relationship)',
            'replacement': r'\1\n    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)\2    user = db.relationship'
        },
        
        # Refund - add created_at
        {
            'pattern': r'(class Refund\(db\.Model\):.*?updated_at = db\.Column\(db\.DateTime, nullable=False, default=datetime\.utcnow, onupdate=datetime\.utcnow\))(.*?# Relationships)',
            'replacement': r'\1\n    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)\n\n\2'
        },
        
        # Sales - add created_at
        {
            'pattern': r'(class Sales\(db\.Model\):.*?sale_date = db\.Column\(db\.DateTime, nullable=False, default=datetime\.utcnow\))(.*?product = db\.relationship)',
            'replacement': r'\1\n    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)\2    product = db.relationship'
        },
        
        # UserInteractionLog - add created_at
        {
            'pattern': r'(class UserInteractionLog\(db\.Model\):.*?timestamp = db\.Column\(db\.DateTime, nullable=False, default=datetime\.utcnow\))(.*?def to_dict)',
            'replacement': r'\1\n    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)\n\n\2'
        },
        
        # UserSession - add created_at
        {
            'pattern': r'(class UserSession\(db\.Model\):.*?session_data = db\.Column\(db\.JSON, nullable=True\))(.*?def to_dict)',
            'replacement': r'\1\n    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)\n\n\2'
        },
        
        # VisualSearchAnalytics - add created_at
        {
            'pattern': r'(class VisualSearchAnalytics\(db\.Model\):.*?image_size = db\.Column\(db\.Integer, nullable=True\))(.*?# Cookie Consent)',
            'replacement': r'\1\n    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)\n\n\2'
        },
        
        # Wishlist - add created_at, updated_at
        {
            'pattern': r'(class Wishlist\(db\.Model\):.*?added_at = db\.Column\(db\.DateTime, nullable=False, default=datetime\.utcnow\))(.*?user = db\.relationship)',
            'replacement': r'\1\n    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)\2    user = db.relationship'
        },
        
        # InventorySyncLog - add created_at
        {
            'pattern': r'(class InventorySyncLog\(db\.Model\):.*?notes = db\.Column\(db\.Text, nullable=True\))(.*?# Relationships)',
            'replacement': r'\1\n    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)\n\n\2'
        },
        
        # SearchAnalytics - add missing columns
        {
            'pattern': r'(class SearchAnalytics\(db\.Model\):.*?created_at = db\.Column\(db\.DateTime, nullable=False, default=datetime\.utcnow\))',
            'replacement': r'\1\n    response_time_ms = db.Column(db.Integer, nullable=True)\n    conversion_events = db.Column(db.JSON, nullable=True)\n    elasticsearch_time_ms = db.Column(db.Integer, nullable=True)'
        }
    ]
    
    # Apply fixes
    for fix in fixes:
        content = re.sub(fix['pattern'], fix['replacement'], content, flags=re.DOTALL)
    
    # Write back to file
    with open('app.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ All database model fixes applied successfully!")

if __name__ == '__main__':
    fix_models()
