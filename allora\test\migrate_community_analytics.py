#!/usr/bin/env python3
"""
Database migration script for Community Analytics features
This script creates the new tables needed for hashtag tracking and community analytics
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from sqlalchemy import text

def create_analytics_tables():
    """Create new tables for community analytics"""

    with app.app_context():
        try:
            print("Starting community analytics migration...")

            # Create all tables defined in models
            print("Creating/updating database tables...")
            db.create_all()
            print("✓ All tables created/updated")

            # Check if we need to add new columns to existing community_post table
            inspector = db.inspect(db.engine)
            columns = [col['name'] for col in inspector.get_columns('community_post')]

            print("Checking community_post table for new columns...")

            # Add new columns if they don't exist
            with db.engine.connect() as conn:
                if 'post_type' not in columns:
                    print("Adding post_type column to community_post...")
                    conn.execute(text("ALTER TABLE community_post ADD COLUMN post_type VARCHAR(20) NOT NULL DEFAULT 'text'"))
                    conn.commit()
                    print("✓ post_type column added")

                if 'image_url' not in columns:
                    print("Adding image_url column to community_post...")
                    conn.execute(text("ALTER TABLE community_post ADD COLUMN image_url VARCHAR(500) NULL"))
                    conn.commit()
                    print("✓ image_url column added")

                if 'video_url' not in columns:
                    print("Adding video_url column to community_post...")
                    conn.execute(text("ALTER TABLE community_post ADD COLUMN video_url VARCHAR(500) NULL"))
                    conn.commit()
                    print("✓ video_url column added")

                if 'likes_count' not in columns:
                    print("Adding likes_count column to community_post...")
                    conn.execute(text("ALTER TABLE community_post ADD COLUMN likes_count INT NOT NULL DEFAULT 0"))
                    conn.commit()
                    print("✓ likes_count column added")

                if 'comments_count' not in columns:
                    print("Adding comments_count column to community_post...")
                    conn.execute(text("ALTER TABLE community_post ADD COLUMN comments_count INT NOT NULL DEFAULT 0"))
                    conn.commit()
                    print("✓ comments_count column added")

                if 'shares_count' not in columns:
                    print("Adding shares_count column to community_post...")
                    conn.execute(text("ALTER TABLE community_post ADD COLUMN shares_count INT NOT NULL DEFAULT 0"))
                    conn.commit()
                    print("✓ shares_count column added")

                if 'updated_at' not in columns:
                    print("Adding updated_at column to community_post...")
                    conn.execute(text("ALTER TABLE community_post ADD COLUMN updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))
                    conn.commit()
                    print("✓ updated_at column added")

            
            print("\n🎉 Community analytics migration completed successfully!")
            print("New features available:")
            print("- Hashtag tracking and trending topics")
            print("- Community statistics")
            print("- Post likes and comments")
            print("- Enhanced post types (photo, video, review, etc.)")
            
        except Exception as e:
            print(f"❌ Migration failed: {e}")
            return False
        
        return True

if __name__ == '__main__':
    success = create_analytics_tables()
    if success:
        print("\n✅ Migration completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Migration failed!")
        sys.exit(1)
