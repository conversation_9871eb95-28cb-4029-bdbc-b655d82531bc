#!/usr/bin/env python3
"""
Elasticsearch Connection and Search Test Script
===============================================

This script tests the Elasticsearch connection and search functionality
to diagnose any issues with the search implementation.

Usage:
    python test_elasticsearch.py

Author: Allora Development Team
Date: 2025-01-09
"""

import os
import sys
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_elasticsearch_connection():
    """Test basic Elasticsearch connection"""
    print("🔍 Testing Elasticsearch Connection...")
    print("=" * 50)
    
    try:
        from elasticsearch_config import get_elasticsearch_client, es_config
        
        # Test configuration
        print(f"✅ Elasticsearch enabled: {es_config.enabled}")
        print(f"✅ Host: {es_config.es_host}:{es_config.es_port}")
        print(f"✅ Scheme: {es_config.es_scheme}")
        
        # Test client connection
        es_client = get_elasticsearch_client()
        if es_client is None:
            print("❌ Elasticsearch client is None")
            return False
        
        # Test cluster health
        health = es_client.cluster.health()
        print(f"✅ Cluster status: {health['status']}")
        print(f"✅ Number of nodes: {health['number_of_nodes']}")
        
        # Test indices
        indices = es_client.cat.indices(format='json')
        allora_indices = [idx for idx in indices if idx['index'].startswith('allora_')]
        print(f"✅ Allora indices found: {len(allora_indices)}")
        
        for idx in allora_indices:
            print(f"   - {idx['index']}: {idx['docs.count']} documents")
        
        return True
        
    except Exception as e:
        print(f"❌ Elasticsearch connection failed: {e}")
        return False

def test_search_engine():
    """Test the search engine functionality"""
    print("\n🔍 Testing Search Engine...")
    print("=" * 50)
    
    try:
        from elasticsearch_search import get_search_engine
        
        search_engine = get_search_engine()
        print(f"✅ Search engine created: {type(search_engine).__name__}")
        print(f"✅ Elasticsearch available: {search_engine.elasticsearch_available}")
        
        # Test basic search
        results = search_engine.search_products(
            query="test",
            page=1,
            per_page=5
        )
        
        print(f"✅ Search executed successfully")
        print(f"   - Total results: {results.get('total', 0)}")
        print(f"   - Products returned: {len(results.get('products', []))}")
        print(f"   - Fallback used: {results.get('fallback_used', False)}")
        print(f"   - Message: {results.get('message', 'No message')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Search engine test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_autocomplete():
    """Test autocomplete functionality"""
    print("\n🔍 Testing Autocomplete...")
    print("=" * 50)
    
    try:
        from elasticsearch_search import get_search_engine
        
        search_engine = get_search_engine()
        
        # Test autocomplete
        suggestions = search_engine.get_autocomplete_suggestions("test", limit=5)
        
        print(f"✅ Autocomplete executed successfully")
        print(f"   - Suggestions returned: {len(suggestions.get('suggestions', []))}")
        print(f"   - Spell suggestions: {len(suggestions.get('spell_suggestions', []))}")
        
        if suggestions.get('suggestions'):
            print("   - Sample suggestions:")
            for i, suggestion in enumerate(suggestions['suggestions'][:3]):
                print(f"     {i+1}. {suggestion.get('text', 'N/A')} ({suggestion.get('type', 'N/A')})")
        
        return True
        
    except Exception as e:
        print(f"❌ Autocomplete test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_elasticsearch_manager():
    """Test Elasticsearch manager functionality"""
    print("\n🔍 Testing Elasticsearch Manager...")
    print("=" * 50)
    
    try:
        from elasticsearch_manager import get_elasticsearch_manager
        
        es_manager = get_elasticsearch_manager()
        if es_manager is None:
            print("❌ Elasticsearch manager is None")
            return False
        
        print(f"✅ Elasticsearch manager created")
        
        # Test index creation
        indices_created = es_manager.create_indices()
        print(f"✅ Indices creation: {'Success' if indices_created else 'Failed'}")
        
        # Test product indexing status
        try:
            from app import Product
            from flask import Flask
            
            app = Flask(__name__)
            app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL')
            app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
            
            from flask_sqlalchemy import SQLAlchemy
            db = SQLAlchemy(app)
            
            with app.app_context():
                product_count = Product.query.count()
                print(f"✅ Products in database: {product_count}")
                
                if product_count > 0:
                    # Test indexing a single product
                    first_product = Product.query.first()
                    if first_product:
                        index_result = es_manager.index_single_product(first_product.id)
                        print(f"✅ Single product indexing: {'Success' if index_result else 'Failed'}")
        
        except Exception as db_error:
            print(f"⚠️ Database test skipped: {db_error}")
        
        return True
        
    except Exception as e:
        print(f"❌ Elasticsearch manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_search_api_integration():
    """Test search API integration"""
    print("\n🔍 Testing Search API Integration...")
    print("=" * 50)
    
    try:
        import requests
        
        # Test main search endpoint
        response = requests.get('http://localhost:5000/api/search?q=test', timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Search API endpoint working")
            print(f"   - Status: {response.status_code}")
            print(f"   - Success: {data.get('success', False)}")
            print(f"   - Total results: {data.get('data', {}).get('total', 0)}")
            print(f"   - Fallback used: {data.get('data', {}).get('fallback_used', False)}")
        else:
            print(f"❌ Search API failed: {response.status_code}")
            return False
        
        # Test autocomplete endpoint
        response = requests.get('http://localhost:5000/api/search/autocomplete?q=test', timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Autocomplete API endpoint working")
            print(f"   - Status: {response.status_code}")
            print(f"   - Success: {data.get('success', False)}")
            print(f"   - Suggestions: {len(data.get('data', {}).get('suggestions', []))}")
        else:
            print(f"❌ Autocomplete API failed: {response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Search API integration test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 ELASTICSEARCH SEARCH FUNCTIONALITY TEST")
    print("=" * 60)
    print(f"🕒 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("Elasticsearch Connection", test_elasticsearch_connection),
        ("Search Engine", test_search_engine),
        ("Autocomplete", test_autocomplete),
        ("Elasticsearch Manager", test_elasticsearch_manager),
        ("Search API Integration", test_search_api_integration),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Elasticsearch search is working correctly.")
    else:
        print("⚠️ Some tests failed. Please check the errors above.")
        
        # Provide troubleshooting suggestions
        print("\n💡 TROUBLESHOOTING SUGGESTIONS:")
        if not results.get("Elasticsearch Connection", False):
            print("   - Check if Elasticsearch service is running")
            print("   - Verify ELASTICSEARCH_HOST and ELASTICSEARCH_PORT in .env")
            print("   - Check firewall settings")
        
        if not results.get("Search Engine", False):
            print("   - Check elasticsearch_search.py imports")
            print("   - Verify database connection")
            print("   - Check product data in database")
        
        if not results.get("Search API Integration", False):
            print("   - Check if Flask backend is running on port 5000")
            print("   - Verify API routes are registered")
            print("   - Check CORS configuration")

if __name__ == '__main__':
    main()
