import razorpay
import os
from dotenv import load_dotenv

load_dotenv()

def test_razorpay_config():
    try:
        key_id = os.getenv('RAZORPAY_KEY_ID')
        key_secret = os.getenv('RAZORPAY_KEY_SECRET')
        
        if not key_id or not key_secret:
            print("❌ Razorpay credentials not configured")
            return False
        
        # Initialize Razorpay client
        client = razorpay.Client(auth=(key_id, key_secret))
        
        # Test API connection
        payments = client.payment.all({'count': 1})
        print("✅ Razorpay connection successful")
        
        # Create test order
        order_data = {
            'amount': 100,  # Amount in paise (₹1.00)
            'currency': 'INR',
            'receipt': 'test_receipt_001'
        }
        
        order = client.order.create(data=order_data)
        print(f"✅ Test order created: {order['id']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Razorpay test failed: {e}")
        return False

if __name__ == "__main__":
    test_razorpay_config()